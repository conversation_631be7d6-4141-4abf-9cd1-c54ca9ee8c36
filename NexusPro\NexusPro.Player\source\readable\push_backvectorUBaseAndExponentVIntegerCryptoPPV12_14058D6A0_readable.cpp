/**
 * @file push_backvectorUBaseAndExponentVIntegerCryptoPPV12_14058D6A0_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: push_backvectorUBaseAndExponentVIntegerCryptoPPV12_14058D6A0
 * @note Address: 0x14058D6A0
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: ?push_back@?$std::vector@U?$BaseAndExponent@VInteger@CryptoPP@@V12@@CryptoPP@@V?$allocator@U?$BaseAndExponent@VInteger@CryptoPP@@V12@@CryptoPP@@@std@@@std@@QEAAXAEBU?$BaseAndExponent@VInteger@CryptoPP@@V12@@CryptoPP@@@Z
 *Address: 0x14058D6A0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"
#include <vector>

// Additional STL includes for compilation
#include <iterator>
#include <memory>



int __fastcall std::vector<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer > >>::push_back(__int64 a1, __int64 a2)
{
  unsigned __int64 v2; // rax@1
  unsigned __int64 v3; // rax@1
  __int64 v4; // rax@2
  char v6; // [sp+20h] [bp-58h]@3
  char*v7; // [sp+38h] [bp-40h]@3
  char v8; // [sp+40h] [bp-38h]@3
  unsigned __int64 v9; // [sp+58h] [bp-20h]@1
  __int64 v10; // [sp+60h] [bp-18h]@3
  __int64 v11; // [sp+80h] [bp+8h]@1
  __int64 v12; // [sp+88h] [bp+10h]@1

  v12 = a2;
  v11 = a1;
  ((DWORD)(v2) = std::vector<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer > >>::size(a1);
  v9 = v2;
  ((DWORD)(v3) = std::vector<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer > >>::capacity(v11);
  void if(v9 >= v3 );
{
    v7 = &v6;
    v10 = std::vector<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer > >>::end(
            v11,
            (__int64)&v6);
    std::vector<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer > >>::insert(
      v11,
      &v8,
      v10,
      v12);
    ((DWORD)(v4) = std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer > >>::~_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer > >>();
  }
  else
  {
    ((DWORD)(v4) = std::vector<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer > >>::_Ufill(
                    v11,
                    *((QWORD*)v11 + 24),
                    1i64,
                    v12);
    *((QWORD*)v11 + 24) = v4;
  }
  return v4;
}





} // namespace Player
} // namespace RFOnline
