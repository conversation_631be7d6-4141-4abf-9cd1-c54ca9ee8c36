/**
 * @file j_PlayerMacroUpdateCNetworkEXAEAA_NHPEADZ_140007716_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_PlayerMacroUpdateCNetworkEXAEAA_NHPEADZ_140007716
 * @note Address: 0x140007716
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?PlayerMacroUpdate@CNetworkEX@@AEAA_NHPEAD@Z
 *Address: 0x140007716
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


bool __fastcall CNetworkEX::PlayerMacroUpdate(CNetworkEX *this, int n, char *pBuf)
{
  return CNetworkEX::PlayerMacroUpdate(this, n, pBuf);
}



} // namespace Player
} // namespace RFOnline
