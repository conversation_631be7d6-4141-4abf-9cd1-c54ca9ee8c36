/**
 * @file j_pc_NPCLinkCheckItemRequest_UseCPlayerQEAAEPEAU_S_140010D8E_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_NPCLinkCheckItemRequest_UseCPlayerQEAAEPEAU_S_140010D8E
 * @note Address: 0x140010D8E
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_NPCLinkCheckItemRequest_Use@CPlayer@@QEAAEPEAU_STORAGE_POS_INDIV@@@Z
 *Address: 0x140010D8E
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


char __fastcall CPlayer::pc_NPCLinkCheckItemRequest_Use(CPlayer *this, _STORAGE_POS_INDIV *pStorage)
{
  return CPlayer::pc_NPCLinkCheckItemRequest_Use(this, pStorage);
}



} // namespace Player
} // namespace RFOnline
