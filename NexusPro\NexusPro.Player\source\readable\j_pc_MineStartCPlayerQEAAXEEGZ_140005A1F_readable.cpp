/**
 * @file j_pc_MineStartCPlayerQEAAXEEGZ_140005A1F_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_MineStartCPlayerQEAAXEEGZ_140005A1F
 * @note Address: 0x140005A1F
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_MineStart@CPlayer@@QEAAXEEG@Z
 *Address: 0x140005A1F
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_MineStart(CPlayer *this, char byMineIndex, char byOreIndex, unsigned __int16 wBatterySerial)
{
  CPlayer::pc_MineStart(this, byMineIndex, byOreIndex, wBatterySerial);
}



} // namespace Player
} // namespace RFOnline
