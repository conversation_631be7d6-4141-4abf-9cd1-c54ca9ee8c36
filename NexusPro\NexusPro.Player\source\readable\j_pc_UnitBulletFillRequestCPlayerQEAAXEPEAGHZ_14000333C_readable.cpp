/**
 * @file j_pc_UnitBulletFillRequestCPlayerQEAAXEPEAGHZ_14000333C_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_UnitBulletFillRequestCPlayerQEAAXEPEAGHZ_14000333C
 * @note Address: 0x14000333C
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_UnitBulletFillRequest@CPlayer@@QEAAXEPEAGH@Z
 *Address: 0x14000333C
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_UnitBulletFillRequest(CPlayer *this, char bySlotIndex, unsigned __int16 *pwBulletIndex, int bUseNPCLinkIntem)
{
  CPlayer::pc_UnitBulletFillRequest(this, bySlotIndex, pwBulletIndex, bUseNPCLinkIntem);
}



} // namespace Player
} // namespace RFOnline
