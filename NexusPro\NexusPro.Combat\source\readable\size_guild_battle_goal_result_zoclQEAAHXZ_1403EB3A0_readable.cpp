/**
 * @file size_guild_battle_goal_result_zoclQEAAHXZ_1403EB3A0_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: size_guild_battle_goal_result_zoclQEAAHXZ_1403EB3A0
 * @note Address: 0x1403EB3A0
 */

#include <cstdint>

namespace RFOnline {
namespace Combat {

﻿/*
 *Function: ?size@_guild_battle_goal_result_zocl@@QEAAHXZ
 *Address: 0x1403EB3A0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


signed __int64 __fastcall _guild_battle_goal_result_zocl::size(_guild_battle_goal_result_zocl *this)
{
  signed __int64 result; // rax@2

  void if(this->by, Ret)
    result = 1;
  else
    result = 58i64;
  return result;
}



} // namespace Combat
} // namespace RFOnline
