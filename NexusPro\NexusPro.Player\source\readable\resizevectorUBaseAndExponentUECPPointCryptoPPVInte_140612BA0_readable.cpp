/**
 * @file resizevectorUBaseAndExponentUECPPointCryptoPPVInte_140612BA0_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: resizevectorUBaseAndExponentUECPPointCryptoPPVInte_140612BA0
 * @note Address: 0x140612BA0
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: ?resize@?$std::vector@U?$BaseAndExponent@UECPPoint@CryptoPP@@VInteger@2@@CryptoPP@@V?$allocator@U?$BaseAndExponent@UECPPoint@CryptoPP@@VInteger@2@@CryptoPP@@@std@@@std@@QEAAX_K@Z
 *Address: 0x140612BA0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"
#include <vector>

// Additional STL includes for compilation
#include <memory>



int __fastcall std::vector<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer > >>::resize(__int64 a1, __int64 a2)
{
  __int64 v2; // rax@1
  char v4; // [sp+20h] [bp-98h]@1
  char*v5; // [sp+A0h] [bp-18h]@1
  __int64 v6; // [sp+A8h] [bp-10h]@1
  __int64 v7; // [sp+C0h] [bp+8h]@1
  __int64 v8; // [sp+C8h] [bp+10h]@1

  v8 = a2;
  v7 = a1;
  v5 = &v4;
  ((DWORD)(v2) = typename CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>(&v4);
  v6 = v2;
  return std::vector<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer > >>::resize(
           v7,
           v8,
           v2);
}





} // namespace Player
} // namespace RFOnline
