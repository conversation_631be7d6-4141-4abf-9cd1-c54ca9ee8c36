/**
 * @file j_pc_RequestQuestFromNPCCPlayerQEAAXPEAVCItemStore_14000A4D4_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_RequestQuestFromNPCCPlayerQEAAXPEAVCItemStore_14000A4D4
 * @note Address: 0x14000A4D4
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_RequestQuestFromNPC@CPlayer@@QEAAXPEAVCItemStore@@K@Z
 *Address: 0x14000A4D4
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_RequestQuestFromNPC(CPlayer *this, CItemStore *pStore, unsigned int dwNPCQuestIndex)
{
  CPlayer::pc_RequestQuestFromNPC(this, pStore, dwNPCQuestIndex);
}



} // namespace Player
} // namespace RFOnline
