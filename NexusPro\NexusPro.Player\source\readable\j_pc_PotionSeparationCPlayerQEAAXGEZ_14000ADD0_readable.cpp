/**
 * @file j_pc_PotionSeparationCPlayerQEAAXGEZ_14000ADD0_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_PotionSeparationCPlayerQEAAXGEZ_14000ADD0
 * @note Address: 0x14000ADD0
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_PotionSeparation@CPlayer@@QEAAXGE@Z
 *Address: 0x14000ADD0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_PotionSeparation(CPlayer *this, unsigned __int16 wSerial, char byAmount)
{
  CPlayer::pc_PotionSeparation(this, wSerial, byAmount);
}



} // namespace Player
} // namespace RFOnline
