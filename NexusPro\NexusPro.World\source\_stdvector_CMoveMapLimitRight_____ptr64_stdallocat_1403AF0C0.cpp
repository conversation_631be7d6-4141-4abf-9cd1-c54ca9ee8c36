﻿/*
 *Function: _std::vector_CMoveMapLimitRight_____ptr64_std::allocator_CMoveMapLimitRight_____ptr64___::insert_::_1_::dtor$1
 *Address: 0x1403AF0C0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"
#include <vector>

// Additional STL includes for compilation
#include <iterator>
#include <memory>



void __fastcall std::vector_CMoveMapLimitRight_____ptr64_std::allocator_CMoveMapLimitRight_____ptr64___::insert_::_1_::dtor_1(__int64 a1, __int64 a2)
{
  if(*((D WO RD*)a2 + 120) &1 )
  {
    *((DWORD*)a2 + 120) &= 0xFFFFFFFE;
    std::_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight * > >::~_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight * > >((std::_Vector_iterator<CMoveMapLimitRight *,std::allocator<CMoveMapLimitRight *>  > *)(a2 + 40));
  }
}



