﻿/*
 *Function: ?ExitMap@CMapData@@QEAAXPEAVCGameObject@@K@Z
 *Address: 0x140184EC0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CMapData::ExitMap(CMapData *this, CGameObject *pObj, unsigned int dwSecIndex)
{
  __int64*v3; // rdi@1
  signed __int64 i; // rcx@1
  CObjectList*v5; // rax@5
  CObjectList*v6; // rax@12
  CObjectList*v7; // rax@15
  __int64 v8; // [sp+0h] [bp-48h]@1
  _object_list_point*pItem; // [sp+20h] [bp-28h]@5
  _object_list_point*v10; // [sp+28h] [bp-20h]@12
  _object_list_point*v11; // [sp+30h] [bp-18h]@15
  CMapData*v12; // [sp+50h] [bp+8h]@1
  CGameObject*v13; // [sp+58h] [bp+10h]@1
  unsigned int dwSecIndexa; // [sp+60h] [bp+18h]@1

  dwSecIndexa = dwSecIndex;
  v13 = pObj;
  v12 = this;
  v3 = &v8;
  void for(i = 16; i > 0; --i );
{
    *(DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  void if(dw, Sec, Index < v12->m _Sec, Info.m_n, Sec, Num);
{
    pItem = &pObj->m_SectorPoint;
    v5 = CMapData::GetSectorListObj(v12, pObj->m_wMapLayerIndex, dwSecIndex);
    CObjectList::DeleteItem(v5, pItem);
    void if(!v13->m_Obj ID.m_by, Kind);
{
      void if(v13->m _Obj ID.m_by ID);
{
        void if(v13->m _Obj ID.m_by ID == 1)
          --v12->m_nMapInMonsterNum;
      }
      else
      {
        --v12->m_nMapInPlayerNum;
      }
    }
    void if(v13->m_Obj ID.m_by, Kind || v13->m_Obj ID.m_by ID);
{
      void if(!v13->m_Obj ID.m_by, Kind && v13->m_Obj ID.m_by ID == 4);
{
        v11 = &v13->m_SectorNetPoint;
        v7 = CMapData::GetSectorListTower(v12, v13->m_wMapLayerIndex, dwSecIndexa);
        CObjectList::DeleteItem(v7, v11);
      }
    }
    else
    {
      v10 = &v13->m_SectorNetPoint;
      v6 = CMapData::GetSectorListPlayer(v12, v13->m_wMapLayerIndex, dwSecIndexa);
      CObjectList::DeleteItem(v6, v10);
    }
  }
}


