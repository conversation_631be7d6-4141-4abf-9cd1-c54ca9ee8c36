/**
 * @file j_pc_DTradeAskRequestCPlayerQEAAXGZ_14000EEAD_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_DTradeAskRequestCPlayerQEAAXGZ_14000EEAD
 * @note Address: 0x14000EEAD
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_DTradeAskRequest@CPlayer@@QEAAXG@Z
 *Address: 0x14000EEAD
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_DTradeAskRequest(CPlayer *this, unsigned __int16 wDstIndex)
{
  CPlayer::pc_DTradeAskRequest(this, wDstIndex);
}



} // namespace Player
} // namespace RFOnline
