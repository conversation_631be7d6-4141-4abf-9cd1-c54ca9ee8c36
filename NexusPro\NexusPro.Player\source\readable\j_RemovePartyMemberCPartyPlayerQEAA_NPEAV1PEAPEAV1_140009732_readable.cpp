/**
 * @file j_RemovePartyMemberCPartyPlayerQEAA_NPEAV1PEAPEAV1_140009732_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_RemovePartyMemberCPartyPlayerQEAA_NPEAV1PEAPEAV1_140009732
 * @note Address: 0x140009732
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?RemovePartyMember@CPartyPlayer@@QEAA_NPEAV1@PEAPEAV1@@Z
 *Address: 0x140009732
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


bool __fastcall CPartyPlayer::RemovePartyMember(CPartyPlayer *this, CPartyPlayer *pExiter, CPartyPlayer **ppoutNewBoss)
{
  return CPartyPlayer::RemovePartyMember(this, pExiter, ppoutNewBoss);
}



} // namespace Player
} // namespace RFOnline
