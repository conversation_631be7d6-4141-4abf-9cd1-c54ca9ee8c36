/**
 * @file j_pc_ExchangeGoldForDalantCPlayerQEAAXKZ_14000D2AB_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_ExchangeGoldForDalantCPlayerQEAAXKZ_14000D2AB
 * @note Address: 0x14000D2AB
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_ExchangeGoldForDalant@CPlayer@@QEAAXK@Z
 *Address: 0x14000D2AB
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_ExchangeGoldForDalant(CPlayer *this, unsigned int dwGold)
{
  CPlayer::pc_ExchangeGoldForDalant(this, dwGold);
}



} // namespace Player
} // namespace RFOnline
