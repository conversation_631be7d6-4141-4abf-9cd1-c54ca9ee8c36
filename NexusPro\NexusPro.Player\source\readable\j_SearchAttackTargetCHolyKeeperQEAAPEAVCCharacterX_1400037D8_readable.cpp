/**
 * @file j_SearchAttackTargetCHolyKeeperQEAAPEAVCCharacterX_1400037D8_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_SearchAttackTargetCHolyKeeperQEAAPEAVCCharacterX_1400037D8
 * @note Address: 0x1400037D8
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?SearchAttackTarget@CHolyKeeper@@QEAAPEAVCCharacter@@XZ
 *Address: 0x1400037D8
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


CCharacter *__fastcall CHolyKeeper::SearchAttackTarget(CHoly, Keeper *this)
{
  return CHolyKeeper::SearchAttackTarget(this);
}



} // namespace Player
} // namespace RFOnline
