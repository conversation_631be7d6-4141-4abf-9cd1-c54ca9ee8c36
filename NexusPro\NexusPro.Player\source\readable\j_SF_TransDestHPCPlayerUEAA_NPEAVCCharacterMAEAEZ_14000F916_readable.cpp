/**
 * @file j_SF_TransDestHPCPlayerUEAA_NPEAVCCharacterMAEAEZ_14000F916_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_SF_TransDestHPCPlayerUEAA_NPEAVCCharacterMAEAEZ_14000F916
 * @note Address: 0x14000F916
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?SF_TransDestHP@CPlayer@@UEAA_NPEAVCCharacter@@MAEAE@Z
 *Address: 0x14000F916
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


bool __fastcall CPlayer::SF_TransDestHP(CPlayer *this, CCharacter *pDstObj, float fEffectValue, char *byRet)
{
  return CPlayer::SF_TransDestHP(this, pDstObj, fEffectValue, byRet);
}



} // namespace Player
} // namespace RFOnline
