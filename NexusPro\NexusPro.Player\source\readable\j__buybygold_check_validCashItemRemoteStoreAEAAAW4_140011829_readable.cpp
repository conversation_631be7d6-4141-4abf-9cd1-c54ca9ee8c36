/**
 * @file j__buybygold_check_validCashItemRemoteStoreAEAAAW4_140011829_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j__buybygold_check_validCashItemRemoteStoreAEAAAW4_140011829
 * @note Address: 0x140011829
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?_buybygold_check_valid@CashItemRemoteStore@@AEAA?AW4CS_RCODE@@PEAVCPlayer@@PEAU_request_csi_buy_clzo@@PEAU_param_cashitem_dblog@@@Z
 *Address: 0x140011829
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


CS_RCODE __fastcall CashItemRemoteStore::_buybygold_check_valid(CashItemRemoteStore *this, CPlayer *pOne, _request_csi_buy_clzo *pRecv, _param_cashitem_dblog *pSheet)
{
  return CashItemRemoteStore::_buybygold_check_valid(this, pOne, pRecv, pSheet);
}



} // namespace Player
} // namespace RFOnline
