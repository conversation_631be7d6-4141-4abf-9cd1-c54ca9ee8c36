/**
 * @file j_pc_UseFireCrackerCPlayerQEAAHGZ_14000B519_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_UseFireCrackerCPlayerQEAAHGZ_14000B519
 * @note Address: 0x14000B519
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_UseFireCracker@CPlayer@@QEAAHG@Z
 *Address: 0x14000B519
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


int __fastcall CPlayer::pc_UseFireCracker(CPlayer *this, unsigned __int16 wItemSerial)
{
  return CPlayer::pc_UseFireCracker(this, wItemSerial);
}



} // namespace Player
} // namespace RFOnline
