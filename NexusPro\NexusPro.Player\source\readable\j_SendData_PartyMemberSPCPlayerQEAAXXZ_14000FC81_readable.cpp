/**
 * @file j_SendData_PartyMemberSPCPlayerQEAAXXZ_14000FC81_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_SendData_PartyMemberSPCPlayerQEAAXXZ_14000FC81
 * @note Address: 0x14000FC81
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?SendData_PartyMemberSP@CPlayer@@QEAAXXZ
 *Address: 0x14000FC81
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::SendData_PartyMemberSP(CPlayer *this)
{
  CPlayer::SendData_PartyMemberSP(this);
}



} // namespace Player
} // namespace RFOnline
