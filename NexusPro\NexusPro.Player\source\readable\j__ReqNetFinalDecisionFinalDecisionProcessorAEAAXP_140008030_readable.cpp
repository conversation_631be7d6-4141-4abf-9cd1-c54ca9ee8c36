/**
 * @file j__ReqNetFinalDecisionFinalDecisionProcessorAEAAXP_140008030_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j__ReqNetFinalDecisionFinalDecisionProcessorAEAAXP_140008030
 * @note Address: 0x140008030
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?_ReqNetFinalDecision@FinalDecisionProcessor@@AEAAXPEAVCPlayer@@@Z
 *Address: 0x140008030
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall FinalDecisionProcessor::_ReqNetFinalDecision(FinalDecisionProcessor *this, CPlayer *pOne)
{
  FinalDecisionProcessor::_ReqNetFinalDecision(this, pOne);
}



} // namespace Player
} // namespace RFOnline
