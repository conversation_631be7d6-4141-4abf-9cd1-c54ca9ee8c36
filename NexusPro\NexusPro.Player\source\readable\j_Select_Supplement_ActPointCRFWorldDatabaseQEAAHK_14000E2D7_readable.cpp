/**
 * @file j_Select_Supplement_ActPointCRFWorldDatabaseQEAAHK_14000E2D7_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_Select_Supplement_ActPointCRFWorldDatabaseQEAAHK_14000E2D7
 * @note Address: 0x14000E2D7
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?Select_Supplement_ActPoint@CRFWorldDatabase@@QEAAHKPEAU_worlddb_character_supplement_info@@@Z
 *Address: 0x14000E2D7
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


int __fastcall CRFWorldDatabase::Select_Supplement_ActPoint(CRFWorldDatabase *this, unsigned int dwSerial, _worlddb_character_supplement_info *pSupplement)
{
  return CRFWorldDatabase::Select_Supplement_ActPoint(this, dwSerial, pSupplement);
}



} // namespace Player
} // namespace RFOnline
