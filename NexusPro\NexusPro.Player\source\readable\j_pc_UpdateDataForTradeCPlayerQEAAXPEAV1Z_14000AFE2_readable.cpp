/**
 * @file j_pc_UpdateDataForTradeCPlayerQEAAXPEAV1Z_14000AFE2_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_UpdateDataForTradeCPlayerQEAAXPEAV1Z_14000AFE2
 * @note Address: 0x14000AFE2
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_UpdateDataForTrade@CPlayer@@QEAAXPEAV1@@Z
 *Address: 0x14000AFE2
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_UpdateDataForTrade(CPlayer *this, CPlayer *pTrader)
{
  CPlayer::pc_UpdateDataForTrade(this, pTrader);
}



} // namespace Player
} // namespace RFOnline
