/**
 * @file size_attack_trap_inform_zoclQEAAHXZ_140141420_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: size_attack_trap_inform_zoclQEAAHXZ_140141420
 * @note Address: 0x140141420
 */

#include <cstdint>

namespace RFOnline {
namespace Combat {

﻿/*
 *Function: ?size@_attack_trap_inform_zocl@@QEAAHXZ
 *Address: 0x140141420
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


signed __int64 __fastcall _attack_trap_inform_zocl::size(_attack_trap_inform_zocl *this)
{
  void if(this->by, List, Num > 32)
    this->byListNum = 0;
  return 326 - 10i64 * (32 - this->byListNum);
}



} // namespace Combat
} // namespace RFOnline
