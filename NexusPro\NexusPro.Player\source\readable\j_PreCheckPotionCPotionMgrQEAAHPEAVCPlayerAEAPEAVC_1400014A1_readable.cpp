/**
 * @file j_PreCheckPotionCPotionMgrQEAAHPEAVCPlayerAEAPEAVC_1400014A1_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_PreCheckPotionCPotionMgrQEAAHPEAVCPlayerAEAPEAVC_1400014A1
 * @note Address: 0x1400014A1
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?PreCheckPotion@CPotionMgr@@QEAAHPEAVCPlayer@@AEAPEAVCCharacter@@PEBU_PotionItem_fld@@KPEAU_skill_fld@@_N@Z
 *Address: 0x1400014A1
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


int __fastcall CPotionMgr::PreCheckPotion(CPotionMgr *this, CPlayer *pUsePlayer, CCharacter **pTarget<PERSON>hara<PERSON>, _PotionItem_fld *pfB, unsigned int nCurTime, _skill_fld *pFld, bool bCheckDist)
{
  return CPotionMgr::PreCheckPotion(this, pUsePlayer, pTargetCharacter, pfB, nCurTime, pFld, bCheckDist);
}



} // namespace Player
} // namespace RFOnline
