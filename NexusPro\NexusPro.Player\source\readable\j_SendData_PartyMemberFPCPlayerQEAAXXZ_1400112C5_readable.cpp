/**
 * @file j_SendData_PartyMemberFPCPlayerQEAAXXZ_1400112C5_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_SendData_PartyMemberFPCPlayerQEAAXXZ_1400112C5
 * @note Address: 0x1400112C5
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?SendData_PartyMemberFP@CPlayer@@QEAAXXZ
 *Address: 0x1400112C5
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::SendData_PartyMemberFP(CPlayer *this)
{
  CPlayer::SendData_PartyMemberFP(this);
}



} // namespace Player
} // namespace RFOnline
