/**
 * @file j_pc_ForceInvenChangeCPlayerQEAAXPEAU_STORAGE_POS__14000EA89_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_ForceInvenChangeCPlayerQEAAXPEAU_STORAGE_POS__14000EA89
 * @note Address: 0x14000EA89
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_ForceInvenChange@CPlayer@@QEAAXPEAU_STORAGE_POS_INDIV@@G@Z
 *Address: 0x14000EA89
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_ForceInvenChange(CPlayer *this, _STORAGE_POS_INDIV *pItem, unsigned __int16 wReplaceSerial)
{
  CPlayer::pc_ForceInvenChange(this, pItem, wReplaceSerial);
}



} // namespace Player
} // namespace RFOnline
