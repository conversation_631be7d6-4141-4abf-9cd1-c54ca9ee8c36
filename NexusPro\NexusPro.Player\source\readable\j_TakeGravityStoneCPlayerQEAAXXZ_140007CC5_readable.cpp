/**
 * @file j_TakeGravityStoneCPlayerQEAAXXZ_140007CC5_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_TakeGravityStoneCPlayerQEAAXXZ_140007CC5
 * @note Address: 0x140007CC5
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?TakeGravityStone@CPlayer@@QEAAXXZ
 *Address: 0x140007CC5
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::TakeGravityStone(CPlayer *this)
{
  CPlayer::TakeGravityStone(this);
}



} // namespace Player
} // namespace RFOnline
