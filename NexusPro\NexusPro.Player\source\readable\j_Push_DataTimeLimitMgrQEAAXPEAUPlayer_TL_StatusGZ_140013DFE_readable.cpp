/**
 * @file j_Push_DataTimeLimitMgrQEAAXPEAUPlayer_TL_StatusGZ_140013DFE_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_Push_DataTimeLimitMgrQEAAXPEAUPlayer_TL_StatusGZ_140013DFE
 * @note Address: 0x140013DFE
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?Push_Data@TimeLimitMgr@@QEAAXPEAUPlayer_TL_Status@@G@Z
 *Address: 0x140013DFE
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall TimeLimitMgr::Push_Data(TimeLimitMgr *this, Player_TL_Status *data, unsigned __int16 wIndex)
{
  TimeLimitMgr::Push_Data(this, data, wIndex);
}



} // namespace Player
} // namespace RFOnline
