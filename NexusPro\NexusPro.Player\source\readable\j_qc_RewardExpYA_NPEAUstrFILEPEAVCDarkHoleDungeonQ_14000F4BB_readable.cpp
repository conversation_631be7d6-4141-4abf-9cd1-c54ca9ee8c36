/**
 * @file j_qc_RewardExpYA_NPEAUstrFILEPEAVCDarkHoleDungeonQ_14000F4BB_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_qc_RewardExpYA_NPEAUstrFILEPEAVCDarkHoleDungeonQ_14000F4BB
 * @note Address: 0x14000F4BB
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?qc_RewardExp@@YA_NPEAUstrFILE@@PEAVCDarkHoleDungeonQuestSetup@@PEAD@Z
 *Address: 0x14000F4BB
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


bool __fastcall qc_RewardExp(strFILE *fstr, CDarkHoleDungeonQuestSetup *pSetup, char *pszoutErrMsg);
{
  return qc_RewardExp(fstr, pSetup, pszoutErrMsg);
}



} // namespace Player
} // namespace RFOnline
