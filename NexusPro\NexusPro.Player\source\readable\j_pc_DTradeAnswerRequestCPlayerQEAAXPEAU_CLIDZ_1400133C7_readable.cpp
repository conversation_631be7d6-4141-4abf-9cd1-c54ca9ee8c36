/**
 * @file j_pc_DTradeAnswerRequestCPlayerQEAAXPEAU_CLIDZ_1400133C7_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_DTradeAnswerRequestCPlayerQEAAXPEAU_CLIDZ_1400133C7
 * @note Address: 0x1400133C7
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_DTradeAnswerRequest@CPlayer@@QEAAXPEAU_CLID@@@Z
 *Address: 0x1400133C7
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_DTradeAnswerRequest(CPlayer *this, _CLID *pidAsker)
{
  CPlayer::pc_DTradeAnswerRequest(this, pid<PERSON><PERSON>);
}



} // namespace Player
} // namespace RFOnline
