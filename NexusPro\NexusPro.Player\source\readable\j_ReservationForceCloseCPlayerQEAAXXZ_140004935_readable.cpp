/**
 * @file j_ReservationForceCloseCPlayerQEAAXXZ_140004935_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_ReservationForceCloseCPlayerQEAAXXZ_140004935
 * @note Address: 0x140004935
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?ReservationForceClose@CPlayer@@QEAAXXZ
 *Address: 0x140004935
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::ReservationForceClose(CPlayer *this)
{
  CPlayer::ReservationForceClose(this);
}



} // namespace Player
} // namespace RFOnline
