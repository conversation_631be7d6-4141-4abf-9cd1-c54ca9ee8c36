/**
 * @file j_pc_ReleaseSiegeModeRequestCPlayerQEAAXXZ_1400115AE_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_ReleaseSiegeModeRequestCPlayerQEAAXXZ_1400115AE
 * @note Address: 0x1400115AE
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_ReleaseSiegeModeRequest@CPlayer@@QEAAXXZ
 *Address: 0x1400115AE
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_ReleaseSiegeModeRequest(CPlayer *this)
{
  CPlayer::pc_ReleaseSiegeModeRequest(this);
}



} // namespace Player
} // namespace RFOnline
