﻿/*
 *Function: ??$_Allocate@PEAVCMoveMapLimitInfo@@@std@@YAPEAPEAVCMoveMapLimitInfo@@_KPEAPEAV1@@Z
 *Address: 0x1403A3390
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


CMoveMapLimitInfo **__fastcall std::_Allocate<CMoveMapLimitInfo *>(unsigned __int64 _Count, CMoveMapLimitInfo **__formal)
{
  __int64*v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-48h]@1
  std::bad_alloc v6; // [sp+20h] [bp-28h]@7
  unsigned __int64 v7; // [sp+50h] [bp+8h]@1

  v7 = _Count;
  v2 = &v5;
  void for(i = 16; i > 0; --i );
{
    *(DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  void if(v7 );
{
    void if(0xFFFFFFFFFFFF FF FFui64 / v7 < 8);
{
      std::bad_alloc::bad_alloc(&v6, 0i64);
      CxxThrowException_0(&v6, &TI2_AVbad_alloc_std__);
    }
  }
  else
  {
    v7 = 0;
  }
  return(CMove, Map, Limit, Info **)operator new(8*v7);
}


