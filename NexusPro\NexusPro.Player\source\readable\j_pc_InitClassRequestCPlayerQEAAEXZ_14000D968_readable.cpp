/**
 * @file j_pc_InitClassRequestCPlayerQEAAEXZ_14000D968_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_InitClassRequestCPlayerQEAAEXZ_14000D968
 * @note Address: 0x14000D968
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_InitClassRequest@CPlayer@@QEAAEXZ
 *Address: 0x14000D968
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


char __fastcall CPlayer::pc_InitClassRequest(CPlayer *this)
{
  return CPlayer::pc_InitClassRequest(this);
}



} // namespace Player
} // namespace RFOnline
