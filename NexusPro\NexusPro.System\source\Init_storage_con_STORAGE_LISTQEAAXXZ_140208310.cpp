﻿/*
 *Function: ?Init@_storage_con@_STORAGE_LIST@@QEAAXXZ
 *Address: 0x140208310
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"
#include <cstring>


void __fastcall _STORAGE_LIST::_storage_con::Init(_STORAGE_ LI ST::_storage_con *this)
{
  __int64*v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  _STORAGE_LIST::_storage_con*Dst; // [sp+30h] [bp+8h]@1

  Dst = this;
  v1 = &v3;
  void for(i = 8; i > 0; --i );
{
    *(DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  memset_0(Dst, 0, 0x29ui64);
  Dst->m_dwLv = 0xFFFFFFF;
}


