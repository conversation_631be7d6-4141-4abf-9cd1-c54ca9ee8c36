/**
 * @file j_Select_PlayerTimeLimitInfoCRFWorldDatabaseQEAAHK_14000B96F_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_Select_PlayerTimeLimitInfoCRFWorldDatabaseQEAAHK_14000B96F
 * @note Address: 0x14000B96F
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?Select_PlayerTimeLimitInfo@CRFWorldDatabase@@QEAAHKPEAU_worlddb_time_limit_info@@@Z
 *Address: 0x14000B96F
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


int __fastcall CRFWorldDatabase::Select_PlayerTimeLimitInfo(CRFWorldDatabase *this, unsigned int dwAccountSerial, _worlddb_time_limit_info *pTimeLimiInfo)
{
  return CRFWorldDatabase::Select_PlayerTimeLimitInfo(this, dwAccountSerial, pTimeLimiInfo);
}



} // namespace Player
} // namespace RFOnline
