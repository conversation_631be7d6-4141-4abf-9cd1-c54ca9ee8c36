/**
 * @file j_pc_MakeTowerRequestCPlayerQEAAXGGEPEAU__material_14000B708_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_MakeTowerRequestCPlayerQEAAXGGEPEAU__material_14000B708
 * @note Address: 0x14000B708
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_MakeTowerRequest@CPlayer@@QEAAXGGEPEAU__material@_make_tower_request_clzo@@PEAMPEAG@Z
 *Address: 0x14000B708
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_MakeTowerRequest(CPlayer *this, unsigned __int16 wSkillIndex, unsigned __int16 wTowerItemSerial, char byMaterialNum, _make_tower_request_clzo::__material *pMaterial, float *pfPos, unsigned __int16 *pConsumeSerial)
{
  CPlayer::pc_MakeTowerRequest(this, wSkillIndex, wTowerItemSerial, byMaterialNum, pMaterial, pfPos, pConsumeSerial);
}



} // namespace Player
} // namespace RFOnline
