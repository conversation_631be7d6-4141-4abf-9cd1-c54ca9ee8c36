/**
 * @file j_SF_ReturnBindPositionCPlayerUEAA_NPEAVCCharacter_140006311_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_SF_ReturnBindPositionCPlayerUEAA_NPEAVCCharacter_140006311
 * @note Address: 0x140006311
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?SF_ReturnBindPosition@CPlayer@@UEAA_NPEAVCCharacter@@M@Z
 *Address: 0x140006311
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


bool __fastcall CPlayer::SF_ReturnBindPosition(CPlayer *this, CCharacter *pDstObj, float fEffectValue)
{
  return CPlayer::SF_ReturnBindPosition(this, pDstObj, fEffectValue);
}



} // namespace Player
} // namespace RFOnline
