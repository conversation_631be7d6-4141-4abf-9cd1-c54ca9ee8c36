/**
 * @file j_SendMsg_AnimusHPInformCPlayerQEAAXXZ_1400093F9_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_SendMsg_AnimusHPInformCPlayerQEAAXXZ_1400093F9
 * @note Address: 0x1400093F9
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?SendMsg_AnimusHPInform@CPlayer@@QEAAXXZ
 *Address: 0x1400093F9
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::SendMsg_AnimusHPInform(CPlayer *this)
{
  CPlayer::SendMsg_AnimusHPInform(this);
}



} // namespace Player
} // namespace RFOnline
