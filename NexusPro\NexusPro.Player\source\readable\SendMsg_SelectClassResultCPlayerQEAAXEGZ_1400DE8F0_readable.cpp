/**
 * @file SendMsg_SelectClassResultCPlayerQEAAXEGZ_1400DE8F0_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: SendMsg_SelectClassResultCPlayerQEAAXEGZ_1400DE8F0
 * @note Address: 0x1400DE8F0
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: ?SendMsg_SelectClassResult@CPlayer@@QEAAXEG@Z
 *Address: 0x1400DE8F0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::SendMsg_SelectClassResult(CPlayer *this, char byErrCode, unsigned __int16 wSelClassIndex)
{
  __int64*v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-B8h]@1
  char szMsg; // [sp+34h] [bp-84h]@4
  char pbyType; // [sp+54h] [bp-64h]@4
  char v8; // [sp+55h] [bp-63h]@4
  char v9[4]; // [sp+74h] [bp-44h]@5
  unsigned __int16 v10; // [sp+78h] [bp-40h]@5
  char v11; // [sp+94h] [bp-24h]@5
  char v12; // [sp+95h] [bp-23h]@5
  CPlayer*v13; // [sp+C0h] [bp+8h]@1
  char v14; // [sp+C8h] [bp+10h]@1
  unsigned __int16 v15; // [sp+D0h] [bp+18h]@1

  v15 = wSelClassIndex;
  v14 = byErrCode;
  v13 = this;
  v3 = &v5;
  void for(signed __int64 i = 44; i > 0; --i);
{
    *(DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  szMsg = byErrCode;
  pbyType = 11;
  v8 = 7;
  CNetProcess::LoadSendMsg(unk_1414F2088, v13->m_ObjID.m_wIndex, &pbyType, &szMsg, 1u);
  void if(!v14 );
{
    *(DWORD *)v9 = v13->m_dwObjSerial;
    v10 = v15;
    v11 = 17;
    v12 = 8;
    CGameObject::CircleReport((CGame, Object *)&v13->vfptr, &v11, v9, 6, 0);
  }
}




} // namespace Player
} // namespace RFOnline
