/**
 * @file j_pc_GuildOfferSuggestRequestCPlayerQEAAXEKPEADKKK_140007AA4_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_GuildOfferSuggestRequestCPlayerQEAAXEKPEADKKK_140007AA4
 * @note Address: 0x140007AA4
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_GuildOfferSuggestRequest@CPlayer@@QEAAXEKPEADKKK@Z
 *Address: 0x140007AA4
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_GuildOfferSuggestRequest(CPlayer *this, char byMatterType, unsigned int dwMatterDst, char *pwszComment, unsigned int dwMatterObj1, unsigned int dwMatterObj2, unsigned int dwMatterObj3)
{
  CPlayer::pc_GuildOfferSuggestRequest(
    this,
    byMatterType,
    dwMatterDst,
    pwszComment,
    dwMatterObj1,
    dwMatterObj2,
    dwMatterObj3);
}



} // namespace Player
} // namespace RFOnline
