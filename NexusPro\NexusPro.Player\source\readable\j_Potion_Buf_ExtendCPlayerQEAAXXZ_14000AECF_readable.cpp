/**
 * @file j_Potion_Buf_ExtendCPlayerQEAAXXZ_14000AECF_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_Potion_Buf_ExtendCPlayerQEAAXXZ_14000AECF
 * @note Address: 0x14000AECF
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?Potion_Buf_Extend@CPlayer@@QEAAXXZ
 *Address: 0x14000AECF
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::Potion_Buf_Extend(CPlayer *this)
{
  CPlayer::Potion_Buf_Extend(this);
}



} // namespace Player
} // namespace RFOnline
