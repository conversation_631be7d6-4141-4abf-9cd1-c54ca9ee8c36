/**
 * @file j_SendMsg_AnimusReturnResultCPlayerQEAAXEGEZ_14000440D_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_SendMsg_AnimusReturnResultCPlayerQEAAXEGEZ_14000440D
 * @note Address: 0x14000440D
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?SendMsg_AnimusReturnResult@CPlayer@@QEAAXEGE@Z
 *Address: 0x14000440D
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::SendMsg_AnimusReturnResult(CPlayer *this, char byRetCode, unsigned __int16 wAnimusItemSerial, char byReturnType)
{
  CPlayer::SendMsg_AnimusReturnResult(this, byRetCode, wAnimusItemSerial, byReturnType);
}



} // namespace Player
} // namespace RFOnline
