/**
 * @file j_pc_DTradeBetRequestCPlayerQEAAXEKZ_14000DA30_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_DTradeBetRequestCPlayerQEAAXEKZ_14000DA30
 * @note Address: 0x14000DA30
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_DTradeBetRequest@CPlayer@@QEAAXEK@Z
 *Address: 0x14000DA30
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_DTradeBetRequest(CPlayer *this, char byMoneyUnit, unsigned int dwBetAmount)
{
  CPlayer::pc_DTradeBetRequest(this, byMoneyUnit, dwBetAmount);
}



} // namespace Player
} // namespace RFOnline
