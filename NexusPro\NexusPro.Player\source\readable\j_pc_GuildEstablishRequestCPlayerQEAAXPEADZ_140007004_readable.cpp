/**
 * @file j_pc_GuildEstablishRequestCPlayerQEAAXPEADZ_140007004_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_GuildEstablishRequestCPlayerQEAAXPEADZ_140007004
 * @note Address: 0x140007004
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_GuildEstablishRequest@CPlayer@@QEAAXPEAD@Z
 *Address: 0x140007004
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_GuildEstablishRequest(CPlayer *this, char *pwszGuildName)
{
  CPlayer::pc_GuildEstablishRequest(this, pwszGuildName);
}



} // namespace Player
} // namespace RFOnline
