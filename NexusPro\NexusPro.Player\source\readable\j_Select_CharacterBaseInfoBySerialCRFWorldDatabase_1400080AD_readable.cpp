/**
 * @file j_Select_CharacterBaseInfoBySerialCRFWorldDatabase_1400080AD_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_Select_CharacterBaseInfoBySerialCRFWorldDatabase_1400080AD
 * @note Address: 0x1400080AD
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?Select_CharacterBaseInfoBySerial@CRFWorldDatabase@@QEAAEKPEAU_worlddb_character_base_info_array@@@Z
 *Address: 0x1400080AD
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


char __fastcall CRFWorldDatabase::Select_CharacterBaseInfoBySerial(CRFWorldDatabase *this, unsigned int dwAccountSerial, _worlddb_character_base_info_array *pCharacterDataArray)
{
  return CRFWorldDatabase::Select_CharacterBaseInfoBySerial(this, dwAccountSerial, pCharacterDataArray);
}



} // namespace Player
} // namespace RFOnline
