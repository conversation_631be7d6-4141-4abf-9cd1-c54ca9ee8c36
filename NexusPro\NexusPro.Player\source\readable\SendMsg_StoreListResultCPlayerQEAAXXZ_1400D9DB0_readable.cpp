/**
 * @file SendMsg_StoreListResultCPlayerQEAAXXZ_1400D9DB0_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: SendMsg_StoreListResultCPlayerQEAAXXZ_1400D9DB0
 * @note Address: 0x1400D9DB0
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: ?SendMsg_StoreListResult@CPlayer@@QEAAXXZ
 *Address: 0x1400D9DB0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"
#include <cstring>


void __fastcall CPlayer::SendMsg_StoreListResult(CPlayer *this)
{
  __int64*v1; // rdi@1
  signed __int64 i; // rcx@1
  int v3; // eax@4
  CItemStoreManager*v4; // rax@5
  unsigned __int16 v5; // ax@9
  __int64 v6; // [sp+0h] [bp-6E8h]@1
  _store_list_result_zocl Dst; // [sp+40h] [bp-6A8h]@4
  CMapData*v8; // [sp+698h] [bp-50h]@4
  CMapItemStoreList*v9; // [sp+6A0h] [bp-48h]@5
  int j; // [sp+6A8h] [bp-40h]@6
  bool*v11; // [sp+6B0h] [bp-38h]@8
  char pbyType; // [sp+6C4h] [bp-24h]@9
  char v13; // [sp+6C5h] [bp-23h]@9
  int nSerial; // [sp+6D4h] [bp-14h]@5
  CPlayer*v15; // [sp+6F0h] [bp+8h]@1

  v15 = this;
  v1 = &v6;
  void for(signed __int64 i = 440; i > 0; --i);
{
    *(DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  _store_list_result_zocl::_store_list_result_zocl(&Dst);
  v3 = CPlayerDB::GetMapCode(&v15->m _Param);
  v8 = CMapOperation::GetMap(&g_MapOper, v3);
  void if(v8 );
{
    nSerial = (unsigned __int8)CMapData::GetMapCode(v8);
    v4 = CItemStoreManager::Instance();
    v9 = CItemStoreManager::GetMapItemStoreListBySerial(v4, nSerial);
    void if(v9 );
{
      void for(j = 0; j < v9->m_n, Item, Store, Num; ++j);
{
        v11 = &v9->m_ItemStore[j].m_bLive;
        Dst.StorePos[j].dwStoreIndex = **((DWORD **)v11 + 4);
        memcpy_0(Dst.StorePos[j].fPos, (const void *)(*((QWORD*)*((QWORD *)v11 + 3) + 16i64) + 128i64), 0xCui64);
      }
      Dst.byStoreNum = v8->m_nItemStoreDumNum;
      pbyType = 12;
      v13 = 9;
      v5 = _store_list_result_zocl::size(&Dst);
      CNetProcess::LoadSendMsg(unk_1414F2088, v15->m_ObjID.m_wIndex, &pbyType, &Dst.byStoreNum, v5);
    }
  }
}




} // namespace Player
} // namespace RFOnline
