/**
 * @file j_pc_TrunkIoSwapRequestCPlayerQEAAXEEGGZ_14000161D_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_TrunkIoSwapRequestCPlayerQEAAXEEGGZ_14000161D
 * @note Address: 0x14000161D
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_TrunkIoSwapRequest@CPlayer@@QEAAXEEGG@Z
 *Address: 0x14000161D
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_TrunkIoSwapRequest(CPlayer *this, char byStartStorageIndex, char byTarStorageIndex, unsigned __int16 wStartItemSerial, unsigned __int16 wTarItemSerial)
{
  CPlayer::pc_TrunkIoSwapRequest(this, byStartStorageIndex, byTarStorageIndex, wStartItemSerial, wTarItemSerial);
}



} // namespace Player
} // namespace RFOnline
