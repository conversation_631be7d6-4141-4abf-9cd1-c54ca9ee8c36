/**
 * @file j_pc_DarkHoleGiveupOutRequestCPlayerQEAAXXZ_14000D2D8_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_DarkHoleGiveupOutRequestCPlayerQEAAXXZ_14000D2D8
 * @note Address: 0x14000D2D8
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_DarkHoleGiveupOutRequest@CPlayer@@QEAAXXZ
 *Address: 0x14000D2D8
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_DarkHoleGiveupOutRequest(CPlayer *this)
{
  CPlayer::pc_DarkHoleGiveupOutRequest(this);
}



} // namespace Player
} // namespace RFOnline
