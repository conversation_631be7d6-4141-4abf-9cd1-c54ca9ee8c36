/**
 * @file j_regist_to_mapAutominePersonalQEAA_NPEAVCPlayerPE_14001137E_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_regist_to_mapAutominePersonalQEAA_NPEAVCPlayerPE_14001137E
 * @note Address: 0x14001137E
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?regist_to_map@AutominePersonal@@QEAA_NPEAVCPlayer@@PEAU_db_con@_STORAGE_LIST@@EKM@Z
 *Address: 0x14001137E
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


bool __fastcall AutominePersonal::regist_to_map(AutominePersonal *this, CPlayer *pOne, _STORAGE_LIST::_db_con *pDstItem, char byDummyIndex, unsigned int dwObjSerial, float fDelayProf)
{
  return AutominePersonal::regist_to_map(this, pOne, pDstItem, byDummyIndex, dwObjSerial, fDelayProf);
}



} // namespace Player
} // namespace RFOnline
