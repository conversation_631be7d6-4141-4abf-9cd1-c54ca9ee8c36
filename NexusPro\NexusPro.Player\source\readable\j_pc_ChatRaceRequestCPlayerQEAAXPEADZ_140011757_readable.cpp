/**
 * @file j_pc_ChatRaceRequestCPlayerQEAAXPEADZ_140011757_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_ChatRaceRequestCPlayerQEAAXPEADZ_140011757
 * @note Address: 0x140011757
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_ChatRaceRequest@CPlayer@@QEAAXPEAD@Z
 *Address: 0x140011757
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_ChatRaceRequest(CPlayer *this, char *pwszChatData)
{
  CPlayer::pc_ChatRaceRequest(this, pwszChatData);
}



} // namespace Player
} // namespace RFOnline
