/**
 * @file j_Select_CharacterNameCRFWorldDatabaseQEAA_NKPEAD0_14000C4E6_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_Select_CharacterNameCRFWorldDatabaseQEAA_NKPEAD0_14000C4E6
 * @note Address: 0x14000C4E6
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?Select_CharacterName@CRFWorldDatabase@@QEAA_NKPEAD0@Z
 *Address: 0x14000C4E6
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


bool __fastcall CRFWorldDatabase::Select_CharacterName(CRFWorldDatabase *this, unsigned int dwSerial, char *pwszCharacterName, char *szAccount)
{
  return CRFWorldDatabase::Select_CharacterName(this, dwSerial, pwszCharacterName, szAccount);
}



} // namespace Player
} // namespace RFOnline
