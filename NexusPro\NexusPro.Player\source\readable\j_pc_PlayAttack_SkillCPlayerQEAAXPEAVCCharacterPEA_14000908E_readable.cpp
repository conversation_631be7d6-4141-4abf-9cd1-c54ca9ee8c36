/**
 * @file j_pc_PlayAttack_SkillCPlayerQEAAXPEAVCCharacterPEA_14000908E_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_PlayAttack_SkillCPlayerQEAAXPEAVCCharacterPEA_14000908E
 * @note Address: 0x14000908E
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_PlayAttack_Skill@CPlayer@@QEAAXPEAVC<PERSON>haracter@@PEAMEGGPEAGG@Z
 *Address: 0x14000908E
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_PlayAttack_Skill(CPlayer *this, CCharacter *pDst, float *pfAttackPos, char byEffectCode, unsigned __int16 wSkillIndex, unsigned __int16 wBulletSerial, unsigned __int16 *pConsumeSerial, unsigned __int16 wEffBtSerial)
{
  CPlayer::pc_PlayAttack_Skill(
    this,
    pDst,
    pfAttackPos,
    byEffectCode,
    wSkillIndex,
    wBulletSerial,
    pConsumeSerial,
    wEffBtSerial);
}



} // namespace Player
} // namespace RFOnline
