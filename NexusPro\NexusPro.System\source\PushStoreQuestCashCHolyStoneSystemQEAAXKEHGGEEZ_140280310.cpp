﻿/*
 *Function: ?PushStoreQuestCash@CHolyStoneSystem@@QEAAXKEHGGEE@Z
 *Address: 0x140280310
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CHolyStoneSystem::PushStoreQuestCash(CHolyStoneSystem *this, unsigned int dwAvatorSerial, char byQuestType, int nPvpPoint, unsigned __int16 wKillPoint, unsigned __int16 wDiePoint, char byCristalBattleDBInfo, char byHSKTime)
{
  __int64*v8; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v10; // [sp+0h] [bp-48h]@1
  int j; // [sp+20h] [bp-28h]@5
  _QUEST_CASH*v12; // [sp+28h] [bp-20h]@8
  _QUEST_CASH*v13; // [sp+30h] [bp-18h]@15
  CHolyStoneSystem*v14; // [sp+50h] [bp+8h]@1
  unsigned int v15; // [sp+58h] [bp+10h]@1
  char v16; // [sp+60h] [bp+18h]@1
  int v17; // [sp+68h] [bp+20h]@1

  v17 = nPvpPoint;
  v16 = byQuestType;
  v15 = dwAvatorSerial;
  v14 = this;
  v8 = &v10;
  void for(i = 16; i > 0; --i );
{
    *(DWORD *)v8 = -858993460;
    v8 = (__int64 *)((char *)v8 + 4);
  }
  void if(v14->m _Save, Data.m_n, Scene, Code == 1);
{
    void for(j = 0; j < 5064; ++j );
{
      v12 = &v14->m_cashQuest[j];
      if(_QUEST_ CA SH::isload(v12) && v12->dwAvatorSerial == v15 )
      {
        v12->byQuestType = v16;
        v12->nPvpPoint = v17;
        v12->wKillPoint = wKillPoint;
        v12->wDiePoint = wDiePoint;
        v12->byCristalBattleDBInfo = byCristalBattleDBInfo;
        v12->byHSKTime = byHSKTime;
        return;
      }
    }
    void for(j = 0; j < 5064; ++j );
{
      v13 = &v14->m_cashQuest[j];
      if(!_QUEST_ CA SH::isload(v13) )
      {
        v13->dwAvatorSerial = v15;
        v13->byQuestType = v16;
        v13->nPvpPoint = v17;
        v13->wKillPoint = wKillPoint;
        v13->wDiePoint = wDiePoint;
        v13->byCristalBattleDBInfo = byCristalBattleDBInfo;
        v13->byHSKTime = byHSKTime;
        return;
      }
    }
  }
}


