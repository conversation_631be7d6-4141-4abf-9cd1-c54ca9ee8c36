/**
 * @file j_SendData_PartyMemberLvCPlayerQEAAXXZ_14001134C_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_SendData_PartyMemberLvCPlayerQEAAXXZ_14001134C
 * @note Address: 0x14001134C
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?SendData_PartyMemberLv@CPlayer@@QEAAXXZ
 *Address: 0x14001134C
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::SendData_PartyMemberLv(CPlayer *this)
{
  CPlayer::SendData_PartyMemberLv(this);
}



} // namespace Player
} // namespace RFOnline
