/**
 * @file j_pc_MakeTrapRequestCPlayerQEAAXGGPEAMPEAGZ_140005C54_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_MakeTrapRequestCPlayerQEAAXGGPEAMPEAGZ_140005C54
 * @note Address: 0x140005C54
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_MakeTrapRequest@CPlayer@@QEAAXGGPEAMPEAG@Z
 *Address: 0x140005C54
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_MakeTrapRequest(CPlayer *this, unsigned __int16 wSkillIndex, unsigned __int16 wTrapItemSerial, float *pfPos, unsigned __int16 *pConsumeSerial)
{
  CPlayer::pc_MakeTrapRequest(this, wSkillIndex, wTrapItemSerial, pfPos, pConsumeSerial);
}



} // namespace Player
} // namespace RFOnline
