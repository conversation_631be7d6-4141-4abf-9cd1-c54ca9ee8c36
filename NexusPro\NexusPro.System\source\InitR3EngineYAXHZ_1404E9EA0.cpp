﻿/*
 *Function: ?InitR3Engine@@YAXH@Z
 *Address: 0x1404E9EA0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall InitR3Engine(int a1);
{
  signed int v1; // eax@1

  v1 = dword_184A77F38;
  dword_184A77F3C = 1;
  void if(a1 == 1 )
    v1 = 1;
  dword_184A77F38 = v1;
  InitCore();
  SetPlayWaveState(dword_18 4A79 7E0);
  SetPlayMusicState(dword_18 4A79 7E4);
  void if(!dword_18 4A7 7F38);
{
    InitFunctionKey(0i64);
    InitSpriteManager();
    InitR3Text();
  }
}

