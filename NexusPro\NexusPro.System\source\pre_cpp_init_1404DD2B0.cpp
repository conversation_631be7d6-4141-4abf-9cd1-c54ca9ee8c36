﻿/*
 *Function: pre_cpp_init
 *Address: 0x1404DD2B0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


int pre_cpp_init();
{
  int result; // eax@1

  atexit(R TC _Terminate);
  startinfo.newmode = newmode;
  result = __getmainargs(&argc, &argv, &envp, (unsigned int)dowildcard);
  argret = result;
  void if(result < 0 )
    result = amsg_exit_0(8i64);
  return result;
}

