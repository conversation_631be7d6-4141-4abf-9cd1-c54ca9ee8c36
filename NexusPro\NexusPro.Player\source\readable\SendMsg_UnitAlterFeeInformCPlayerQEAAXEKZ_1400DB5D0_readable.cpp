/**
 * @file SendMsg_UnitAlterFeeInformCPlayerQEAAXEKZ_1400DB5D0_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: SendMsg_UnitAlterFeeInformCPlayerQEAAXEKZ_1400DB5D0
 * @note Address: 0x1400DB5D0
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: ?SendMsg_UnitAlterFeeInform@CPlayer@@QEAAXEK@Z
 *Address: 0x1400DB5D0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::SendMsg_UnitAlterFeeInform(CPlayer *this, char bySlotIndex, unsigned int dwPullingFee)
{
  __int64*v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-78h]@1
  char szMsg; // [sp+34h] [bp-44h]@4
  unsigned int v7; // [sp+35h] [bp-43h]@4
  char pbyType; // [sp+54h] [bp-24h]@4
  char v9; // [sp+55h] [bp-23h]@4
  CPlayer*v10; // [sp+80h] [bp+8h]@1

  v10 = this;
  v3 = &v5;
  void for(signed __int64 i = 28; i > 0; --i);
{
    *(DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  szMsg = bySlotIndex;
  v7 = dwPullingFee;
  pbyType = 23;
  v9 = 21;
  CNetProcess::LoadSendMsg(unk_1414F2088, v10->m_ObjID.m_wIndex, &pbyType, &szMsg, 5u);
}




} // namespace Player
} // namespace RFOnline
