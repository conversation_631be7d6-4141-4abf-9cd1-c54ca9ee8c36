/**
 * @file j_PushDamageCLootingMgrQEAAXPEAVCPlayerGZ_140010087_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_PushDamageCLootingMgrQEAAXPEAVCPlayerGZ_140010087
 * @note Address: 0x140010087
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?PushDamage@CLootingMgr@@QEAAXPEAVCPlayer@@G@Z
 *Address: 0x140010087
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CLootingMgr::PushDamage(CLootingMgr *this, CPlayer *pAtter, unsigned __int16 wDamage)
{
  CLootingMgr::PushDamage(this, pAtter, wDamage);
}



} // namespace Player
} // namespace RFOnline
