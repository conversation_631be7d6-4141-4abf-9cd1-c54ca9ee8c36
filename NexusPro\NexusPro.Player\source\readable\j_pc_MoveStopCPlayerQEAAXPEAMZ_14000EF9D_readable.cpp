/**
 * @file j_pc_MoveStopCPlayerQEAAXPEAMZ_14000EF9D_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_MoveStopCPlayerQEAAXPEAMZ_14000EF9D
 * @note Address: 0x14000EF9D
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_MoveStop@CPlayer@@QEAAXPEAM@Z
 *Address: 0x14000EF9D
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_MoveStop(CPlayer *this, float *pfCur)
{
  CPlayer::pc_MoveStop(this, pfCur);
}



} // namespace Player
} // namespace RFOnline
