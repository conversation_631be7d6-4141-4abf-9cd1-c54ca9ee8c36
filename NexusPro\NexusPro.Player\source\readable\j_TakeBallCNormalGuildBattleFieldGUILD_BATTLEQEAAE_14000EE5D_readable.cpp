/**
 * @file j_TakeBallCNormalGuildBattleFieldGUILD_BATTLEQEAAE_14000EE5D_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_TakeBallCNormalGuildBattleFieldGUILD_BATTLEQEAAE_14000EE5D
 * @note Address: 0x14000EE5D
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?TakeBall@CNormalGuildBattleField@GUILD_BATTLE@@QEAAEHPEAVCPlayer@@@Z
 *Address: 0x14000EE5D
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


char __fastcall GUILD_BATTLE::CNormalGuildBattleField::TakeBall(GUILD_BATTLE::CNormalGuildBattleField *this, int iPortalInx, CPlayer *pkPlayer)
{
  return GUILD_BATTLE::CNormalGuildBattleField::TakeBall(this, iPortalInx, pkPlayer);
}



} // namespace Player
} // namespace RFOnline
