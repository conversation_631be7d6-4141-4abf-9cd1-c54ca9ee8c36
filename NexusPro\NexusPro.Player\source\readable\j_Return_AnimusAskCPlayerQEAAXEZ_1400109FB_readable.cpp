/**
 * @file j_Return_AnimusAskCPlayerQEAAXEZ_1400109FB_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_Return_AnimusAskCPlayerQEAAXEZ_1400109FB
 * @note Address: 0x1400109FB
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?Return_AnimusAsk@CPlayer@@QEAAXE@Z
 *Address: 0x1400109FB
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::Return_AnimusAsk(CPlayer *this, char byReturnType)
{
  CPlayer::Return_AnimusAsk(this, byReturnType);
}



} // namespace Player
} // namespace RFOnline
