/**
 * @file j_pc_RealMovPosCPlayerQEAAXPEAMZ_140003779_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_RealMovPosCPlayerQEAAXPEAMZ_140003779
 * @note Address: 0x140003779
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_RealMovPos@CPlayer@@QEAAXPEAM@Z
 *Address: 0x140003779
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_RealMovPos(CPlayer *this, float *pfCur)
{
  CPlayer::pc_RealMovPos(this, pfCur);
}



} // namespace Player
} // namespace RFOnline
