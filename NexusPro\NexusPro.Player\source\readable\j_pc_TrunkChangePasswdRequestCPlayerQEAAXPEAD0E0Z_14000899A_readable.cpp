/**
 * @file j_pc_TrunkChangePasswdRequestCPlayerQEAAXPEAD0E0Z_14000899A_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_TrunkChangePasswdRequestCPlayerQEAAXPEAD0E0Z_14000899A
 * @note Address: 0x14000899A
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_TrunkChangePasswdRequest@CPlayer@@QEAAXPEAD0E0@Z
 *Address: 0x14000899A
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_TrunkChangePasswdRequest(CPlayer *this, char *pwszPrevPassword, char *pwszChngPassword, char byHintIndex, char *pwszHintAnswer)
{
  CPlayer::pc_TrunkChangePasswdRequest(this, pwszPrevPassword, pwszChngPassword, byHintIndex, pwszHintAnswer);
}



} // namespace Player
} // namespace RFOnline
