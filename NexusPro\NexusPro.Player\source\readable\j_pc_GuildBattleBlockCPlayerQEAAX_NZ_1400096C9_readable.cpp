/**
 * @file j_pc_GuildBattleBlockCPlayerQEAAX_NZ_1400096C9_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_GuildBattleBlockCPlayerQEAAX_NZ_1400096C9
 * @note Address: 0x1400096C9
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_GuildBattleBlock@CPlayer@@QEAAX_N@Z
 *Address: 0x1400096C9
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_GuildBattleBlock(CPlayer *this, bool bBlock)
{
  CPlayer::pc_GuildBattleBlock(this, bBlock);
}



} // namespace Player
} // namespace RFOnline
