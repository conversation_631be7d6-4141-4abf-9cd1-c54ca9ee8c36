/**
 * @file j_pc_MineCancleCPlayerQEAAXXZ_140002275_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_MineCancleCPlayerQEAAXXZ_140002275
 * @note Address: 0x140002275
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_MineCancle@CPlayer@@QEAAXXZ
 *Address: 0x140002275
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_MineCancle(CPlayer *this)
{
  CPlayer::pc_MineCancle(this);
}



} // namespace Player
} // namespace RFOnline
