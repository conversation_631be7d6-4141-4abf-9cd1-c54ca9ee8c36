/**
 * @file j_PostSendRequestCPostSystemManagerQEAA_NPEAVCPlay_140008265_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_PostSendRequestCPostSystemManagerQEAA_NPEAVCPlay_140008265
 * @note Address: 0x140008265
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?PostSendRequest@CPostSystemManager@@QEAA_NPEAVCPlayer@@PEAD11PEAU_STORAGE_POS_INDIV@@KE@Z
 *Address: 0x140008265
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


bool __fastcall CPostSystemManager::PostSendRequest(CPostSystemManager *this, CPlayer *pOne, char *wszRecvName, char *wszTitle, char *wszContent, _STORAGE_POS_INDIV *pItemInfo, unsigned int dwGold, char byRace)
{
  return CPostSystemManager::PostSendRequest(this, pOne, wszRecvName, wszTitle, wszContent, pItemInfo, dwGold, byRace);
}



} // namespace Player
} // namespace RFOnline
