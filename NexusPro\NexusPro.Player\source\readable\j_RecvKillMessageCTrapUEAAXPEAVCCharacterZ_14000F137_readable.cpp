/**
 * @file j_RecvKillMessageCTrapUEAAXPEAVCCharacterZ_14000F137_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_RecvKillMessageCTrapUEAAXPEAVCCharacterZ_14000F137
 * @note Address: 0x14000F137
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?RecvKillMessage@CTrap@@UEAAXPEAVCCharacter@@@Z
 *Address: 0x14000F137
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CTrap::RecvKillMessage(CTrap *this, CCharacter *pDier)
{
  CTrap::RecvKillMessage(this, pDier);
}



} // namespace Player
} // namespace RFOnline
