/**
 * @file j_SumMinuteOneCPlayerQEAAKPEAU_SYSTEMTIMEZ_14000FAD3_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_SumMinuteOneCPlayerQEAAKPEAU_SYSTEMTIMEZ_14000FAD3
 * @note Address: 0x14000FAD3
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?SumMinuteOne@CPlayer@@QEAAKPEAU_SYSTEMTIME@@@Z
 *Address: 0x14000FAD3
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


unsigned int __fastcall CPlayer::SumMinuteOne(CPlayer *this, _SYSTEMTIME *tm)
{
  return CPlayer::SumMinuteOne(this, tm);
}



} // namespace Player
} // namespace RFOnline
