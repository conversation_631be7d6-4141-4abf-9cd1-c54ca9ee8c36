/**
 * @file j_mgr_whisperCPlayerQEAA_NPEADZ_14000254F_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_mgr_whisperCPlayerQEAA_NPEADZ_14000254F
 * @note Address: 0x14000254F
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?mgr_whisper@CPlayer@@QEAA_NPEAD@Z
 *Address: 0x14000254F
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


bool __fastcall CPlayer::mgr_whisper(CPlayer *this, char *pwszMsg)
{
  return CPlayer::mgr_whisper(this, pwszMsg);
}



} // namespace Player
} // namespace RFOnline
