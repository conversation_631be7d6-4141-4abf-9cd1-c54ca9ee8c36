/**
 * @file j_StartCNormalGuildBattleManagerGUILD_BATTLEQEAAEP_14000E5AC_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_StartCNormalGuildBattleManagerGUILD_BATTLEQEAAEP_14000E5AC
 * @note Address: 0x14000E5AC
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?Start@CNormalGuildBattleManager@GUILD_BATTLE@@QEAAEPEAVCPlayer@@KK@Z
 *Address: 0x14000E5AC
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


char __fastcall GUILD_BATTLE::CNormalGuildBattleManager::Start(GUILD_BATTLE::CNormalGuildBattleManager *this, CPlayer *pkPlayer, unsigned int dwGuildSerial, unsigned int dwCharacSerial)
{
  return GUILD_BATTLE::CNormalGuildBattleManager::Start(this, pkPlayer, dwGuildSerial, dwCharacSerial);
}



} // namespace Player
} // namespace RFOnline
