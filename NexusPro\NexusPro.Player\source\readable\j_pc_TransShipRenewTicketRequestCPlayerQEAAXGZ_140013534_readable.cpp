/**
 * @file j_pc_TransShipRenewTicketRequestCPlayerQEAAXGZ_140013534_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_TransShipRenewTicketRequestCPlayerQEAAXGZ_140013534
 * @note Address: 0x140013534
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_TransShipRenewTicketRequest@CPlayer@@QEAAXG@Z
 *Address: 0x140013534
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_TransShipRenewTicketRequest(CPlayer *this, unsigned __int16 wTicketItemSerial)
{
  CPlayer::pc_TransShipRenewTicketRequest(this, wTicketItemSerial);
}



} // namespace Player
} // namespace RFOnline
