﻿/*
 *Function: ?EnterWorldRequest@CNetworkEX@@AEAA_NHPEAU_MSG_HEADER@@PEAD@Z
 *Address: 0x1401D0D30
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


char __fastcall CNetworkEX::EnterWorldRequest(CNetworkEX *this, int n, _MSG_HEADER *pMsgHeader, char *pBuf)
{
  __int64*v4; // rdi@1
  signed __int64 i; // rcx@1
  CNationSettingManager*v6; // rax@4
  char result; // al@5
  unsigned __int16 v8; // ax@15
  __int64 v9; // [sp+0h] [bp-88h]@1
  unsigned __int16 nLen[4]; // [sp+20h] [bp-68h]@16
  char*v11; // [sp+30h] [bp-58h]@4
  char v12; // [sp+38h] [bp-50h]@6
  int v13; // [sp+3Ch] [bp-4Ch]@6
  _enter_world_result_zone v14; // [sp+44h] [bp-44h]@15
  char pbyType; // [sp+64h] [bp-24h]@15
  char v16; // [sp+65h] [bp-23h]@15
  _socket*v17; // [sp+78h] [bp-10h]@16
  CNetworkEX*v18; // [sp+90h] [bp+8h]@1
  int na; // [sp+98h] [bp+10h]@1
  _MSG_HEADER*v20; // [sp+A0h] [bp+18h]@1
  char*pBufa; // [sp+A8h] [bp+20h]@1

  pBufa = pBuf;
  v20 = pMsgHeader;
  na = n;
  v18 = this;
  v4 = &v9;
  void for(i = 32; i; --i );
{
    *(DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v11 = pBuf;
  v6 = CTSingleton<CNationSettingManager>::Instance();
  if ( CNationSettingManager::CheckEnterWorldRequest(v6, na, pBufa) )
  {
    v12 = 0;
    v13 = v20->m_wSize - 62;
    void if(v13 );
{
      CLogFile::Write(&v18->m_LogFile, "KeyCheckError.. Socket( %d) : ( pMsgHeader->m_wSize - sizeof(_MSG_HE AD ER) ) - sizeof( _enter_world_request_zone ) != 0 ",
        (unsigned int)na);
      v12 = -14;
    }
    else if ( strlen_0(v11 + 25) == 32 )
    {
      void if(strcmp_0(CMainThread::ms_szClientVerCheck, "X")
        && strncmp(CMainThread::ms_szClientVerCheck, v11 + 25, 0x20ui64) )
      {
        CLogFile::Write(&v18->m_LogFile, "KeyCheckError.. Socket( %d) : EnterWorldRequest().. if( strncmp( CMainThread::ms_szClientVerCheck, pRecv->szCl"
          "ientVerCheckKey, CMainThread::eClinetCheckMax ) )",
          (unsigned int)na);
        v12 = -14;
      }
    }
    else
    {
      CLogFile::Write(&v18->m_LogFile, "KeyCheckError.. Socket( %d) : EnterWorldRequest().. if(strlen(p, Recv->sz, Client, Ver, Check, Key) != CMainThread::eClinetCheckMax )",
        (unsigned int)na);
      v12 = -14;
    }
    void if(v12 );
{
      v14.byResult = v12;
      v14.byUserGrade = 0;
      v14.bySvrType = unk_1799C9AE9;
      pbyType = 1;
      v16 = 2;
      v8 = _enter_world_result_zone::size(&v14);
      CNetProcess::LoadSendMsg(unk_1414F2088, g_UserDB[na].m_idWorld.wIndex, &pbyType, &v14.byResult, v8);
      CNetworkEX::Close(v18, 0, na, 1, 0i64);
      result = 1;
    }
    else
    {
      v17 = CNetWorking::GetSocket((CNet, Working *)&v18->vfptr, 0, na);
      *(QWORD *)nLen = v11 + 4;
      if ( CUserDB::Enter_Account(
             &g_UserDB[na],
             *(DWORD *)v11,
             v17->m_Addr.sin_addr.S_un.S_addr,
             *((DWORD*)v11 + 21),
             (unsigned int *)v11 + 1) )
      {
        v17->m_bEnterCheck = 1;
        CNetProcess::StartSpeedHackCheck(v18->m_pProcess[0], na, g_UserDB[na].m_szAccountID);
        result = 1;
      }
      else
      {
        CNetworkEX::Close(v18, 0, na, 0, 0i64);
        result = 1;
      }
    }
  }
  else
  {
    result = 0;
  }
  return result;
}


