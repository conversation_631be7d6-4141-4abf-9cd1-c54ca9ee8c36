/**
 * @file j_SendCRaceBossMsgControllerQEAA_NPEAVCPlayerPEBDZ_1400080A8_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_SendCRaceBossMsgControllerQEAA_NPEAVCPlayerPEBDZ_1400080A8
 * @note Address: 0x1400080A8
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?Send@CRaceBossMsgController@@QEAA_NPEAVCPlayer@@PEBD@Z
 *Address: 0x1400080A8
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


bool __fastcall CRaceBossMsgController::Send(CRaceBossMsgController *this, CPlayer *pkSender, const char *pwszMsg)
{
  return CRaceBossMsgController::Send(this, pkS<PERSON>, pwszMsg);
}



} // namespace Player
} // namespace RFOnline
