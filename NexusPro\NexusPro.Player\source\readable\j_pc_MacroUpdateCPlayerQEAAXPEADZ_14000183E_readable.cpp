/**
 * @file j_pc_MacroUpdateCPlayerQEAAXPEADZ_14000183E_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_MacroUpdateCPlayerQEAAXPEADZ_14000183E
 * @note Address: 0x14000183E
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_MacroUpdate@CPlayer@@QEAAXPEAD@Z
 *Address: 0x14000183E
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_MacroUpdate(CPlayer *this, char *pBuf)
{
  CPlayer::pc_MacroUpdate(this, pBuf);
}



} // namespace Player
} // namespace RFOnline
