/**
 * @file j_pc_PartyJoinApplicationAnswerCPlayerQEAAXPEAU_CL_140009944_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_PartyJoinApplicationAnswerCPlayerQEAAXPEAU_CL_140009944
 * @note Address: 0x140009944
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_PartyJoinApplicationAnswer@CPlayer@@QEAAXPEAU_CLID@@@Z
 *Address: 0x140009944
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_PartyJoinApplicationAnswer(CPlayer *this, _CLID *pidApplicant)
{
  CPlayer::pc_PartyJoinApplicationAnswer(this, pidApplicant);
}



} // namespace Player
} // namespace RFOnline
