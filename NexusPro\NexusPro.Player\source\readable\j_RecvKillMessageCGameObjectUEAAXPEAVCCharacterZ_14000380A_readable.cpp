/**
 * @file j_RecvKillMessageCGameObjectUEAAXPEAVCCharacterZ_14000380A_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_RecvKillMessageCGameObjectUEAAXPEAVCCharacterZ_14000380A
 * @note Address: 0x14000380A
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?RecvKillMessage@CGameObject@@UEAAXPEAVCCharacter@@@Z
 *Address: 0x14000380A
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CGameObject::RecvKillMessage(CGameObject *this, CCharacter *pDier)
{
  CGameObject::RecvKillMessage(this, pDier);
}



} // namespace Player
} // namespace RFOnline
