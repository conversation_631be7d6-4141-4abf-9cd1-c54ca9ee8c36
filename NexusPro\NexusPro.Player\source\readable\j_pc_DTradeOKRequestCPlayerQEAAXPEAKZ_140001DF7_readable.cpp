/**
 * @file j_pc_DTradeOKRequestCPlayerQEAAXPEAKZ_140001DF7_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_DTradeOKRequestCPlayerQEAAXPEAKZ_140001DF7
 * @note Address: 0x140001DF7
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_DTradeOKRequest@CPlayer@@QEAAXPEAK@Z
 *Address: 0x140001DF7
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_DTradeOKRequest(CPlayer *this, unsigned int *pdwKey)
{
  CPlayer::pc_DTradeOKRequest(this, pdwKey);
}



} // namespace Player
} // namespace RFOnline
