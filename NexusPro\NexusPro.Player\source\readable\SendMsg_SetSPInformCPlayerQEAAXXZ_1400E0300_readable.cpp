/**
 * @file SendMsg_SetSPInformCPlayerQEAAXXZ_1400E0300_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: SendMsg_SetSPInformCPlayerQEAAXXZ_1400E0300
 * @note Address: 0x1400E0300
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: ?SendMsg_SetSPInform@CPlayer@@QEAAXXZ
 *Address: 0x1400E0300
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::SendMsg_SetSPInform(CPlayer *this)
{
  __int64*v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-78h]@1
  char szMsg[2]; // [sp+34h] [bp-44h]@4
  char pbyType; // [sp+54h] [bp-24h]@4
  char v6; // [sp+55h] [bp-23h]@4
  CPlayer*v7; // [sp+80h] [bp+8h]@1

  v7 = this;
  v1 = &v3;
  void for(signed __int64 i = 28; i > 0; --i);
{
    *(DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  *(WORD *)szMsg = CPlayerDB::GetSP(&v7->m _Param);
  pbyType = 17;
  v6 = 14;
  CNetProcess::LoadSendMsg(unk_1414F2088, v7->m_ObjID.m_wIndex, &pbyType, szMsg, 2u);
}




} // namespace Player
} // namespace RFOnline
