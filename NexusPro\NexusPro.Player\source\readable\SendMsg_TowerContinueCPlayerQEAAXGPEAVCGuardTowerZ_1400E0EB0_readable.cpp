/**
 * @file SendMsg_TowerContinueCPlayerQEAAXGPEAVCGuardTowerZ_1400E0EB0_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: SendMsg_TowerContinueCPlayerQEAAXGPEAVCGuardTowerZ_1400E0EB0
 * @note Address: 0x1400E0EB0
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: ?SendMsg_TowerContinue@CPlayer@@QEAAXGPEAVCGuardTower@@@Z
 *Address: 0x1400E0EB0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::SendMsg_TowerContinue(CPlayer *this, unsigned __int16 wItemSerial, CGuardTower *pTwr)
{
  __int64*v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-88h]@1
  char szMsg[2]; // [sp+38h] [bp-50h]@4
  __int16 v7; // [sp+3Ah] [bp-4Eh]@4
  unsigned __int16 v8; // [sp+3Ch] [bp-4Ch]@4
  unsigned int v9; // [sp+3Eh] [bp-4Ah]@4
  char pbyType; // [sp+64h] [bp-24h]@4
  char v11; // [sp+65h] [bp-23h]@4
  CPlayer*v12; // [sp+90h] [bp+8h]@1

  v12 = this;
  v3 = &v5;
  void for(i = 32; i; --i );
{
    *(DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  *(WORD *)szMsg = wItemSerial;
  v7 = pTwr->m_pRecordSet->m_dwIndex;
  v8 = pTwr->m_ObjID.m_wIndex;
  v9 = pTwr->m_dwObjSerial;
  pbyType = 17;
  v11 = 30;
  CNetProcess::LoadSendMsg(unk_1414F2088, v12->m_ObjID.m_wIndex, &pbyType, szMsg, 0xAu);
}




} // namespace Player
} // namespace RFOnline
