/**
 * @file j_pc_GiveItemCPlayerQEAA_NAEAU_db_con_STORAGE_LIST_140013935_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_GiveItemCPlayerQEAA_NAEAU_db_con_STORAGE_LIST_140013935
 * @note Address: 0x140013935
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_GiveItem@CPlayer@@QEAA_NAEAU_db_con@_STORAGE_LIST@@PEAD_N@Z
 *Address: 0x140013935
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


bool __fastcall CPlayer::pc_GiveItem(CPlayer *this, _STORAGE_LIST::_db_con *kItem, char *szR<PERSON>on, bool bDrop)
{
  return CPlayer::pc_GiveItem(this, kItem, szReason, bDrop);
}



} // namespace Player
} // namespace RFOnline
