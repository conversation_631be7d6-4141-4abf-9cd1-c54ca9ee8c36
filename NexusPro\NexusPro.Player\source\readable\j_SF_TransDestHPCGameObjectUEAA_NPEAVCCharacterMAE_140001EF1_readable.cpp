/**
 * @file j_SF_TransDestHPCGameObjectUEAA_NPEAVCCharacterMAE_140001EF1_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_SF_TransDestHPCGameObjectUEAA_NPEAVCCharacterMAE_140001EF1
 * @note Address: 0x140001EF1
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?SF_TransDestHP@CGameObject@@UEAA_NPEAVCCharacter@@MAEAE@Z
 *Address: 0x140001EF1
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


bool __fastcall CGameObject::SF_TransDestHP(CGameObject *this, CCharacter *pDstObj, float fEffectValue, char *byRet)
{
  return CGameObject::SF_TransDestHP(this, pDstObj, fEffectValue, byRet);
}



} // namespace Player
} // namespace RFOnline
