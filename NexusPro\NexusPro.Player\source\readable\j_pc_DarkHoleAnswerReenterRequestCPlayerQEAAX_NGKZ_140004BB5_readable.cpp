/**
 * @file j_pc_DarkHoleAnswerReenterRequestCPlayerQEAAX_NGKZ_140004BB5_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_DarkHoleAnswerReenterRequestCPlayerQEAAX_NGKZ_140004BB5
 * @note Address: 0x140004BB5
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_DarkHoleAnswerReenterRequest@CPlayer@@QEAAX_NGK@Z
 *Address: 0x140004BB5
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_DarkHoleAnswerReenterRequest(CPlayer *this, bool bEnter, unsigned __int16 wChannelIndex, unsigned int dwChannelSerial)
{
  CPlayer::pc_DarkHoleAnswerReenterRequest(this, bEnter, wChannelIndex, dwChannelSerial);
}



} // namespace Player
} // namespace RFOnline
