/**
 * @file j_ProcessEnterCReturnGateControllerIEAAHIPEAVCPlay_14000AF74_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_ProcessEnterCReturnGateControllerIEAAHIPEAVCPlay_14000AF74
 * @note Address: 0x14000AF74
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?ProcessEnter@CReturnGateController@@IEAAHIPEAVCPlayer@@@Z
 *Address: 0x14000AF74
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


int __fastcall CReturnGateController::ProcessEnter(CReturnGateController *this, unsigned int uiGateInx, CPlayer *pkObj)
{
  return CReturnGateController::ProcessEnter(this, uiGateInx, pkObj);
}



} // namespace Player
} // namespace RFOnline
