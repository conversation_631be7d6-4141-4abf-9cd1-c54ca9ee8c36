/**
 * @file j_SendMsg_AlterBoosterCPlayerQEAAXXZ_140013B79_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_SendMsg_AlterBoosterCPlayerQEAAXXZ_140013B79
 * @note Address: 0x140013B79
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?SendMsg_AlterBooster@CPlayer@@QEAAXXZ
 *Address: 0x140013B79
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::SendMsg_AlterBooster(CPlayer *this)
{
  CPlayer::SendMsg_AlterBooster(this);
}



} // namespace Player
} // namespace RFOnline
