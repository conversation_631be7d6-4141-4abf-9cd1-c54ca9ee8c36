/**
 * @file SendMsg_RemainOreRateCPlayerQEAAXXZ_1400E85D0_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: SendMsg_RemainOreRateCPlayerQEAAXXZ_1400E85D0
 * @note Address: 0x1400E85D0
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: ?SendMsg_RemainOreRate@CPlayer@@QEAAXXZ
 *Address: 0x1400E85D0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::SendMsg_RemainOreRate(CPlayer *this)
{
  __int64*v1; // rdi@1
  signed __int64 i; // rcx@1
  COreAmountMgr*v3; // rax@4
  __int64 v4; // [sp+0h] [bp-78h]@1
  char szMsg; // [sp+34h] [bp-44h]@4
  char pbyType; // [sp+54h] [bp-24h]@4
  char v7; // [sp+55h] [bp-23h]@4
  CPlayer*v8; // [sp+80h] [bp+8h]@1

  v8 = this;
  v1 = &v4;
  void for(signed __int64 i = 28; i > 0; --i);
{
    *(DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v3 = COreAmountMgr::Instance();
  szMsg = COreAmountMgr::GetDepositRate(v3);
  pbyType = 14;
  v7 = 68;
  CNetProcess::LoadSendMsg(unk_1414F2088, v8->m_ObjID.m_wIndex, &pbyType, &szMsg, 1u);
}




} // namespace Player
} // namespace RFOnline
