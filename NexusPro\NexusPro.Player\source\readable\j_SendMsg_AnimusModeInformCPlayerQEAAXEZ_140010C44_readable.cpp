/**
 * @file j_SendMsg_AnimusModeInformCPlayerQEAAXEZ_140010C44_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_SendMsg_AnimusModeInformCPlayerQEAAXEZ_140010C44
 * @note Address: 0x140010C44
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?SendMsg_AnimusModeInform@CPlayer@@QEAAXE@Z
 *Address: 0x140010C44
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::SendMsg_AnimusModeInform(CPlayer *this, char byMode)
{
  CPlayer::SendMsg_AnimusModeInform(this, byMode);
}



} // namespace Player
} // namespace RFOnline
