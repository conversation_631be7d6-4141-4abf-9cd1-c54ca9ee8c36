/**
 * @file j_pc_ChatMapRequestCPlayerQEAAXPEADZ_140011937_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_ChatMapRequestCPlayerQEAAXPEADZ_140011937
 * @note Address: 0x140011937
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_ChatMapRequest@CPlayer@@QEAAXPEAD@Z
 *Address: 0x140011937
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_ChatMapRequest(CPlayer *this, char *pwszChatData)
{
  CPlayer::pc_ChatMapRequest(this, pwszChatData);
}



} // namespace Player
} // namespace RFOnline
