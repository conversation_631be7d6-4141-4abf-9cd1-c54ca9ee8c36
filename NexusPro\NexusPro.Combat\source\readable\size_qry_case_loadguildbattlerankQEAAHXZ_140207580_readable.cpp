/**
 * @file size_qry_case_loadguildbattlerankQEAAHXZ_140207580_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: size_qry_case_loadguildbattlerankQEAAHXZ_140207580
 * @note Address: 0x140207580
 */

#include <cstdint>

namespace RFOnline {
namespace Combat {

﻿/*
 *Function: ?size@_qry_case_loadguildbattlerank@@QEAAHXZ
 *Address: 0x140207580
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


signed __int64 __fastcall _qry_case_loadguildbattlerank::size(_qry_case_loadguildbattlerank *this)
{
  return 2i64;
}



} // namespace Combat
} // namespace RFOnline
