/**
 * @file j_pc_SelectClassRequestCPlayerQEAAXGEZ_140008AEE_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_SelectClassRequestCPlayerQEAAXGEZ_140008AEE
 * @note Address: 0x140008AEE
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_SelectClassRequest@CPlayer@@QEAAXGE@Z
 *Address: 0x140008AEE
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_SelectClassRequest(CPlayer *this, unsigned __int16 wSelClassIndex, char bySelectRewardItem)
{
  CPlayer::pc_SelectClassRequest(this, wSelClassIndex, bySelectRewardItem);
}



} // namespace Player
} // namespace RFOnline
