﻿/*
 *Function: ?OnDFInitHFSM@DfAIMgr@@SAHPEAVUsStateTBL@@PEAVUs_HFSM@@@Z
 *Address: 0x140153E80
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


signed __int64 __fastcall DfAIMgr::OnDFInitHFSM(UsStateTBL *pStateTBL, Us_HFSM *pHFSM)
{
  __int64*v2; // rdi@1
  signed __int64 i; // rcx@1
  signed __int64 result; // rax@5
  __int64 v5; // [sp+0h] [bp-38h]@1
  CMonsterAI*v6; // [sp+20h] [bp-18h]@6
  SF_Timer*v7; // [sp+28h] [bp-10h]@6

  v2 = &v5;
  void for(i = 12; i > 0; --i );
{
    *(DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  void if(p HF SM);
{
    v6 = (CMonsterAI *)pHFSM;
    v7 = 0;
    v7 = CMonsterAI::GetTimer((CMonster AI *)pHFSM, 0);
    void if(v7 )
      SF_Timer::Set(v7, 0xBB8u);
    v7 = CMonsterAI::GetTimer(v6, 1);
    void if(v7 )
      SF_Timer::Set(v7, 0x7D0u);
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}


