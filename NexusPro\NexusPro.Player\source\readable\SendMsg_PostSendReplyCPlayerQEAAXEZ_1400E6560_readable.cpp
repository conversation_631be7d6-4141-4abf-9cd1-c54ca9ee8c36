/**
 * @file SendMsg_PostSendReplyCPlayerQEAAXEZ_1400E6560_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: SendMsg_PostSendReplyCPlayerQEAAXEZ_1400E6560
 * @note Address: 0x1400E6560
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: ?SendMsg_PostSendReply@CPlayer@@QEAAXE@Z
 *Address: 0x1400E6560
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::SendMsg_PostSendReply(CPlayer *this, char byErrCode)
{
  __int64*v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-78h]@1
  _post_result_zocl v5; // [sp+34h] [bp-44h]@4
  char pbyType; // [sp+54h] [bp-24h]@6
  char v7; // [sp+55h] [bp-23h]@6
  CPlayer*v8; // [sp+80h] [bp+8h]@1
  char v9; // [sp+88h] [bp+10h]@1

  v9 = byErrCode;
  v8 = this;
  v2 = &v4;
  void for(signed __int64 i = 28; i > 0; --i);
{
    *(DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  _post_result_zocl::_post_result_zocl(&v5);
  v5.byErrCode = v9;
  void if(!v9 )
    v5.dwGold = CPlayerDB::GetGold(&v8->m _Param);
  pbyType = 58;
  v7 = 2;
  CNetProcess::LoadSendMsg(unk_1414F2088, v8->m_ObjID.m_wIndex, &pbyType, &v5.byErrCode, 5u);
}




} // namespace Player
} // namespace RFOnline
