/**
 * @file j_pc_GuildRoomOutRequestCPlayerQEAAXPEAU_guildroom_140012BFC_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_GuildRoomOutRequestCPlayerQEAAXPEAU_guildroom_140012BFC
 * @note Address: 0x140012BFC
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_GuildRoomOutRequest@CPlayer@@QEAAXPEAU_guildroom_out_request_clzo@@@Z
 *Address: 0x140012BFC
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_GuildRoomOutRequest(CPlayer *this, _guildroom_out_request_clzo *pProtocol)
{
  CPlayer::pc_GuildRoomOutRequest(this, pProtocol);
}



} // namespace Player
} // namespace RFOnline
