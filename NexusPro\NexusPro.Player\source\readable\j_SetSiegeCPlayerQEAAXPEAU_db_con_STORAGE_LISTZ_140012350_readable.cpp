/**
 * @file j_SetSiegeCPlayerQEAAXPEAU_db_con_STORAGE_LISTZ_140012350_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_SetSiegeCPlayerQEAAXPEAU_db_con_STORAGE_LISTZ_140012350
 * @note Address: 0x140012350
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?SetSiege@CPlayer@@QEAAXPEAU_db_con@_STORAGE_LIST@@@Z
 *Address: 0x140012350
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::SetSiege(CPlayer *this, _STORAGE_LIST::_db_con *pSiegeItem)
{
  CPlayer::SetSiege(this, pSiegeItem);
}



} // namespace Player
} // namespace RFOnline
