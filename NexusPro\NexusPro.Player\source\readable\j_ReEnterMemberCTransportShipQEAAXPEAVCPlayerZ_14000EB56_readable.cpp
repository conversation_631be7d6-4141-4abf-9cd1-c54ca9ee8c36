/**
 * @file j_ReEnterMemberCTransportShipQEAAXPEAVCPlayerZ_14000EB56_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_ReEnterMemberCTransportShipQEAAXPEAVCPlayerZ_14000EB56
 * @note Address: 0x14000EB56
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?ReEnterMember@CTransportShip@@QEAAXPEAVCPlayer@@@Z
 *Address: 0x14000EB56
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CTransportShip::ReEnterMember(CTransportShip *this, CPlayer *pExiter)
{
  CTransportShip::ReEnterMember(this, pExiter);
}



} // namespace Player
} // namespace RFOnline
