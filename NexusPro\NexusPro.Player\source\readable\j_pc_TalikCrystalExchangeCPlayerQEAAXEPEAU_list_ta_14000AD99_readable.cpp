/**
 * @file j_pc_TalikCrystalExchangeCPlayerQEAAXEPEAU_list_ta_14000AD99_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_TalikCrystalExchangeCPlayerQEAAXEPEAU_list_ta_14000AD99
 * @note Address: 0x14000AD99
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_TalikCrystalExchange@CPlayer@@QEAAXEPEAU_list@_talik_crystal_exchange_clzo@@@Z
 *Address: 0x14000AD99
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_TalikCrystalExchange(CPlayer *this, char byExchangeNum, _talik_crystal_exchange_clzo::_list *pList)
{
  CPlayer::pc_TalikCrystalExchange(this, byExchange<PERSON>um, pList);
}



} // namespace Player
} // namespace RFOnline
