/**
 * @file j_pc_TrunkPotionDivisionCPlayerQEAAXGGGEZ_140003D50_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_TrunkPotionDivisionCPlayerQEAAXGGGEZ_140003D50
 * @note Address: 0x140003D50
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_TrunkPotionDivision@CPlayer@@QEAAXGGGE@Z
 *Address: 0x140003D50
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_TrunkPotionDivision(CPlayer *this, unsigned __int16 wStartSerial, unsigned __int16 wTarSerial, unsigned __int16 wMoveAmount, char byStorageIndex)
{
  CPlayer::pc_TrunkPotionDivision(this, wStartSerial, wTarSerial, wMoveAmount, byStorageIndex);
}



} // namespace Player
} // namespace RFOnline
