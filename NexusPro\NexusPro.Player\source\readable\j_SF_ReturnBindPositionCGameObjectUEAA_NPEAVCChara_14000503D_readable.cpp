/**
 * @file j_SF_ReturnBindPositionCGameObjectUEAA_NPEAVCChara_14000503D_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_SF_ReturnBindPositionCGameObjectUEAA_NPEAVCChara_14000503D
 * @note Address: 0x14000503D
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?SF_ReturnBindPosition@CGameObject@@UEAA_NPEAVCCharacter@@M@Z
 *Address: 0x14000503D
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


bool __fastcall CGameObject::SF_ReturnBindPosition(CGameObject *this, CCharacter *pDstObj, float fEffectValue)
{
  return CGameObject::SF_ReturnBindPosition(this, pDstObj, fEffectValue);
}



} // namespace Player
} // namespace RFOnline
