/**
 * @file SendMsg_TargetObjectHPInformCPlayerQEAAXXZ_1400E26D0_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: SendMsg_TargetObjectHPInformCPlayerQEAAXXZ_1400E26D0
 * @note Address: 0x1400E26D0
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: ?SendMsg_TargetObjectHPInform@CPlayer@@QEAAXXZ
 *Address: 0x1400E26D0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::SendMsg_TargetObjectHPInform(CPlayer *this)
{
  __int64*v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-78h]@1
  char szMsg; // [sp+38h] [bp-40h]@4
  char v5; // [sp+39h] [bp-3Fh]@4
  unsigned int v6; // [sp+3Ah] [bp-3Eh]@4
  unsigned __int16 v7; // [sp+3Eh] [bp-3Ah]@4
  char pbyType; // [sp+54h] [bp-24h]@4
  char v9; // [sp+55h] [bp-23h]@4
  CPlayer*v10; // [sp+80h] [bp+8h]@1

  v10 = this;
  v1 = &v3;
  void for(signed __int64 i = 28; i > 0; --i);
{
    *(DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  szMsg = v10->m_TargetObject.byKind;
  v5 = v10->m_TargetObject.byID;
  v6 = v10->m_TargetObject.dwSerial;
  v7 = v10->m_TargetObject.wHPRate;
  pbyType = 13;
  v9 = 29;
  CNetProcess::LoadSendMsg(unk_1414F2088, v10->m_ObjID.m_wIndex, &pbyType, &szMsg, 8u);
}




} // namespace Player
} // namespace RFOnline
