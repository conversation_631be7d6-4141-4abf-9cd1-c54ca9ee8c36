/**
 * @file j__UpdateRateSendToAllPlayerYAXXZ_1400071C1_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j__UpdateRateSendToAllPlayerYAXXZ_1400071C1
 * @note Address: 0x1400071C1
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?_UpdateRateSendToAllPlayer@@YAXXZ
 *Address: 0x1400071C1
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void _UpdateRateSendToAllPlayer(void);
{
  _UpdateRateSendToAllPlayer();
}



} // namespace Player
} // namespace RFOnline
