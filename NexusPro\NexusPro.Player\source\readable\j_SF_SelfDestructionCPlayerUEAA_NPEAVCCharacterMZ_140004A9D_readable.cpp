/**
 * @file j_SF_SelfDestructionCPlayerUEAA_NPEAVCCharacterMZ_140004A9D_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_SF_SelfDestructionCPlayerUEAA_NPEAVCCharacterMZ_140004A9D
 * @note Address: 0x140004A9D
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?SF_SelfDestruction@CPlayer@@UEAA_NPEAVCCharacter@@M@Z
 *Address: 0x140004A9D
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


bool __fastcall CPlayer::SF_SelfDestruction(CPlayer *this, CCharacter *pDstObj, float fEffectValue)
{
  return CPlayer::SF_SelfDestruction(this, pDstObj, fEffectValue);
}



} // namespace Player
} // namespace RFOnline
