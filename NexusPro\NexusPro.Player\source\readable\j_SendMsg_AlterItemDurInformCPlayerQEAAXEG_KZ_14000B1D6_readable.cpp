/**
 * @file j_SendMsg_AlterItemDurInformCPlayerQEAAXEG_KZ_14000B1D6_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_SendMsg_AlterItemDurInformCPlayerQEAAXEG_KZ_14000B1D6
 * @note Address: 0x14000B1D6
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?SendMsg_AlterItemDurInform@CPlayer@@QEAAXEG_K@Z
 *Address: 0x14000B1D6
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::SendMsg_AlterItemDurInform(CPlayer *this, char byStorageCode, unsigned __int16 wItemSerial, unsigned __int64 dwDur)
{
  CPlayer::SendMsg_AlterItemDurInform(this, byStorageCode, wItemSerial, dwDur);
}



} // namespace Player
} // namespace RFOnline
