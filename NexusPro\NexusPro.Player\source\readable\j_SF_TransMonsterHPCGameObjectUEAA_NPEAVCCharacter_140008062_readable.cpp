/**
 * @file j_SF_TransMonsterHPCGameObjectUEAA_NPEAVCCharacter_140008062_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_SF_TransMonsterHPCGameObjectUEAA_NPEAVCCharacter_140008062
 * @note Address: 0x140008062
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?SF_TransMonsterHP@CGameObject@@UEAA_NPEAVCCharacter@@M@Z
 *Address: 0x140008062
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


bool __fastcall CGameObject::SF_TransMonsterHP(CGameObject *this, CCharacter *pDstObj, float fEffectValue)
{
  return CGameObject::SF_TransMonsterHP(this, pDstObj, fEffectValue);
}



} // namespace Player
} // namespace RFOnline
