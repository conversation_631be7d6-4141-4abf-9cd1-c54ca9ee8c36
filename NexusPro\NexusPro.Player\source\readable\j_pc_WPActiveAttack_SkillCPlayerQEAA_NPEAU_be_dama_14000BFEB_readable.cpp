/**
 * @file j_pc_WPActiveAttack_SkillCPlayerQEAA_NPEAU_be_dama_14000BFEB_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_WPActiveAttack_SkillCPlayerQEAA_NPEAU_be_dama_14000BFEB
 * @note Address: 0x14000BFEB
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_WPActiveAttack_Skill@CPlayer@@QEAA_NPEAU_be_damaged_char@@PEAH1PEAU_skill_fld@@EG@Z
 *Address: 0x14000BFEB
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


bool __fastcall CPlayer::pc_WPActiveAttack_Skill(CPlayer *this, _be_damaged_char *pDamList, int *nDamagedObjNum, int *nShotNum, _skill_fld *pSkillFld, char byEffectCode, unsigned __int16 wBulletSerial)
{
  return CPlayer::pc_WPActiveAttack_Skill(
           this,
           pDamList,
           nDamagedObjNum,
           nShotNum,
           pSkillFld,
           byEffectCode,
           wBulletSerial);
}



} // namespace Player
} // namespace RFOnline
