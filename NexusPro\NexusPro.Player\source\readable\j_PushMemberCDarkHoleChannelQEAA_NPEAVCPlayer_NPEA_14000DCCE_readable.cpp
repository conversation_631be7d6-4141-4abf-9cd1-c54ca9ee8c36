/**
 * @file j_PushMemberCDarkHoleChannelQEAA_NPEAVCPlayer_NPEA_14000DCCE_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_PushMemberCDarkHoleChannelQEAA_NPEAVCPlayer_NPEA_14000DCCE
 * @note Address: 0x14000DCCE
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?PushMember@CDarkHoleChannel@@QEAA_NPEAVCPlayer@@_NPEAVCMapData@@GPEAM@Z
 *Address: 0x14000DCCE
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


bool __fastcall CDarkHoleChannel::PushMember(CDarkHoleChannel *this, CPlayer *pMember, bool bReconnect, CMapData *pOldMap, unsigned __int16 wLastLayer, float *pfOldPos)
{
  return CDarkHoleChannel::Push<PERSON><PERSON><PERSON>(this, pMember, bReconnect, pOldMap, wLastLayer, pfOldPos);
}



} // namespace Player
} // namespace RFOnline
