/**
 * @file j_ProcessRequestRecallCRecallEffectControllerIEAAE_14000B550_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_ProcessRequestRecallCRecallEffectControllerIEAAE_14000B550
 * @note Address: 0x14000B550
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?ProcessRequestRecall@CRecallEffectController@@IEAAEPEAVCPlayer@@PEAVCCharacter@@AEAPEAVCRecallRequest@@_N33@Z
 *Address: 0x14000B550
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


char __fastcall CRecallEffectController::ProcessRequestRecall(CRecallEffectController *this, CPlayer *pkPerformer, CCharacter *pkDest, CRecallRequest **pkRequest, bool bRecallParty, bool bStone, bool bBattleModeUse)
{
  return CRecallEffectController::ProcessRequestRecall(
           this,
           pkPerformer,
           pkDest,
           pkRequest,
           bRecallParty,
           bStone,
           bBattleModeUse);
}



} // namespace Player
} // namespace RFOnline
