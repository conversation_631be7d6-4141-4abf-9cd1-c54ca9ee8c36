/**
 * @file j_PushDQSUpdatePlyerVoteInfoCPlayerQEAAXXZ_1400056A5_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_PushDQSUpdatePlyerVoteInfoCPlayerQEAAXXZ_1400056A5
 * @note Address: 0x1400056A5
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?PushDQSUpdatePlyerVoteInfo@CPlayer@@QEAAXXZ
 *Address: 0x1400056A5
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::PushDQSUpdatePlyerVoteInfo(CPlayer *this)
{
  CPlayer::PushDQSUpdatePlyerVoteInfo(this);
}



} // namespace Player
} // namespace RFOnline
