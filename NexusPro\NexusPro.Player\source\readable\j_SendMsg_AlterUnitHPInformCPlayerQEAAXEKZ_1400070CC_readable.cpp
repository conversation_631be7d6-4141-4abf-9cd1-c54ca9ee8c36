/**
 * @file j_SendMsg_AlterUnitHPInformCPlayerQEAAXEKZ_1400070CC_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_SendMsg_AlterUnitHPInformCPlayerQEAAXEKZ_1400070CC
 * @note Address: 0x1400070CC
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?SendMsg_AlterUnitHPInform@CPlayer@@QEAAXEK@Z
 *Address: 0x1400070CC
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::SendMsg_AlterUnitHPInform(CPlayer *this, char bySlotIndex, unsigned int dwGauge)
{
  CPlayer::SendMsg_AlterUnitHPInform(this, bySlotIndex, dwGauge);
}



} // namespace Player
} // namespace RFOnline
