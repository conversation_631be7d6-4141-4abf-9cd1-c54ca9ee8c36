/**
 * @file j_SendMsg_AlterPvPCashCPlayerQEAAXHZ_140005A42_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_SendMsg_AlterPvPCashCPlayerQEAAXHZ_140005A42
 * @note Address: 0x140005A42
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?SendMsg_AlterPvPCash@CPlayer@@QEAAXH@Z
 *Address: 0x140005A42
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::SendMsg_AlterPvPCash(CPlayer *this, int nIOCode)
{
  CPlayer::SendMsg_AlterPvPCash(this, nIOCode);
}



} // namespace Player
} // namespace RFOnline
