﻿/*
 *Function: _std::_Uninit_fill_n_CHEAT_COMMAND_____ptr64_unsigned___int64_CHEAT_COMMAND_std::allocator_CHEAT_COMMAND____::_1_::catch$0
 *Address: 0x140220FE0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// Additional STL includes for compilation
#include <memory>



void __fastcall __noreturn std::_Uninit_fill_n_CHEAT_COMMAND_____ptr64_unsigned___int64_CHEAT_COMMAND_std::allocator_CHEAT_COMMAND____::_1_::catch_0(__int64 a1, __int64 a2)
{
  __int64 i; // rbp@1

  for(i = a2; *((Q WO RD*)i + 32) != *((QWORD*)i + 64); *((QWORD*)i + 32) += 32i64 )
    typename std::allocator<CHEAT_COMMAND>::destroy(*(std::allocator<CHEAT_COM MA ND> **)(i + 88), *(CHEAT_COMMAND **)(i + 32));
  CxxThrowException_0(0i64, 0i64);
}



