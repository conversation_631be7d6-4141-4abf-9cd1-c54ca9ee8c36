﻿/*
 *Function: ?Loop@CPvpUserAndGuildRankingSystem@@QEAAXXZ
 *Address: 0x14032B1B0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPvpUserAndGuildRankingSystem::Loop(CPvp, User, And, Guild, Ranking, System *this)
{
  __int64*v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  CPvpUserAndGuildRankingSystem*v4; // [sp+30h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  void for(i = 8; i > 0; --i );
{
    *(DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  void if(unk_179 9C60 8D)
    CUserRankingProcess::Loop(&v4->m_k, User, Ranking, Process);
}


