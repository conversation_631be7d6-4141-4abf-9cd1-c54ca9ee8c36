/**
 * @file j_pc_MovePortalCPlayerQEAAXHPEAGZ_1400016CC_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_MovePortalCPlayerQEAAXHPEAGZ_1400016CC
 * @note Address: 0x1400016CC
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_MovePortal@CPlayer@@QEAAXHPEAG@Z
 *Address: 0x1400016CC
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_MovePortal(CPlayer *this, int nPortalIndex, unsigned __int16 *pConsumeSerial)
{
  CPlayer::pc_MovePortal(this, nPortalIndex, pConsumeSerial);
}



} // namespace Player
} // namespace RFOnline
