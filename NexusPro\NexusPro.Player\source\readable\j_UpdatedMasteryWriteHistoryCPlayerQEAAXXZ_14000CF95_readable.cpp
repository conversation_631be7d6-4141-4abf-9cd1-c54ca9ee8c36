/**
 * @file j_UpdatedMasteryWriteHistoryCPlayerQEAAXXZ_14000CF95_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_UpdatedMasteryWriteHistoryCPlayerQEAAXXZ_14000CF95
 * @note Address: 0x14000CF95
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?UpdatedMasteryWriteHistory@CPlayer@@QEAAXXZ
 *Address: 0x14000CF95
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::UpdatedMasteryWriteHistory(CPlayer *this)
{
  CPlayer::UpdatedMasteryWriteHistory(this);
}



} // namespace Player
} // namespace RFOnline
