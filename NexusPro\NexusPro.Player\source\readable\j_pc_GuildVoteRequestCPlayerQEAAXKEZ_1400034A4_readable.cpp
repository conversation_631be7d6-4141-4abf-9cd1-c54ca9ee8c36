/**
 * @file j_pc_GuildVoteRequestCPlayerQEAAXKEZ_1400034A4_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_GuildVoteRequestCPlayerQEAAXKEZ_1400034A4
 * @note Address: 0x1400034A4
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_GuildVoteRequest@CPlayer@@QEAAXKE@Z
 *Address: 0x1400034A4
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_GuildVoteRequest(CPlayer *this, unsigned int dwMatterVoteSynKey, char byVoteCode)
{
  CPlayer::pc_GuildVoteRequest(this, dwMatterVoteSynKey, byVoteCode);
}



} // namespace Player
} // namespace RFOnline
