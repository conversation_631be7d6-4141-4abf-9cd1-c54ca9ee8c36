﻿/*
 *Function: ?InitData@_AVATOR_DATA@@QEAAXXZ
 *Address: 0x140077A00
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall _AVATOR_DATA::InitData(_AVATOR_ DA TA *this)
{
  __int64*v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  _AVATOR_DATA*v4; // [sp+30h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  void for(i = 8; i > 0; --i );
{
    *(DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  _AVATOR_DB_BASE::Init(&v4->db, <PERSON>tor);
  _LINK_DB_BASE::Init(&v4->db, <PERSON>);
  _EQUIP_DB_BASE::Init(&v4->db, Equip);
  _FORCE_DB_BASE::Init(&v4->db, Force);
  _ANIMUS_DB_BASE::Init(&v4->db, Animus);
  _STAT_DB_BASE::Init(&v4->db, Stat);
  _INVEN_DB_BASE::Init(&v4->db, Inven);
  _CUTTING_DB_BASE::Init(&v4->db, Cutting);
  _CUTTING_DB_BASE::ReSetOldDataLoad(&v4->db, Cutting);
  _QUEST_DB_BASE::Init(&v4->db, Quest);
  _SFCONT_DB_BASE::Init(&v4->db, Sfcont);
  _TRADE_DB_BASE::Init(&v4->db, Trade);
  _BUDDY_DB_BASE::Init(&v4->db, Buddy);
  _TRUNK_DB_BASE::Init(&v4->db, Trunk);
  _ITEMCOMBINE_DB_BASE::Init(&v4->db, Item, Combine, Ex);
  _PERSONALAMINE_INVEN_DB_BASE::Init(&v4->db, Personal, Amine, Inven);
  _PVPPOINT_LIMIT_DB_BASE::Init(&v4->db, Pvp, Point, Limit);
  _POSTDATA_DB_BASE::Init(&v4->db, Post, Data);
  _CRYMSG_DB_BASE::Init(&v4->db, Boss, Cry);
  _PVP_ORDER_VIEW_DB_BASE::Init(&v4->db, Pvp, Order, View);
  _SUPPLEMENT_DB_BASE::Init(&v4->db, Supplement);
  _PCBANG_PLAY_TIME::Init(&v4->db, Play, Time, I nPcbang);
  _POTION_NEXT_USE_TIME_DB_BASE::Init(&v4->db, Potion, Next, Use, Time);
  _PCBANG_FAVOR_ITEM_DB_BASE::Init(&v4->db, P cBang, Favor, Item);
  v4->m_bCristalBattleDateUpdate = 1;
}


