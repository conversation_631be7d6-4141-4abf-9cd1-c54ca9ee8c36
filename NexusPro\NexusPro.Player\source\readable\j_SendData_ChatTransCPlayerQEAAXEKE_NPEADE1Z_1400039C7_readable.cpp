/**
 * @file j_SendData_ChatTransCPlayerQEAAXEKE_NPEADE1Z_1400039C7_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_SendData_ChatTransCPlayerQEAAXEKE_NPEADE1Z_1400039C7
 * @note Address: 0x1400039C7
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?SendData_ChatTrans@CPlayer@@QEAAXEKE_NPEADE1@Z
 *Address: 0x1400039C7
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::SendData_ChatTrans(CPlayer *this, char byChatType, unsigned int dwSenderSerial, char byRaceCode, bool bFilter, char *pwszMessage, char byPvp<PERSON>rade, char *pwsz<PERSON>ender)
{
  CPlayer::SendData_ChatTrans(
    this,
    byChatType,
    dwSenderSerial,
    byRaceCode,
    bFilter,
    pwszMessage,
    byPvpGrade,
    pwszSender);
}



} // namespace Player
} // namespace RFOnline
