/**
 * @file j_ReleaseCRaceBuffInfoByHolyQuestfGroupQEAA_NHPEAV_14000AB5A_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_ReleaseCRaceBuffInfoByHolyQuestfGroupQEAA_NHPEAV_14000AB5A
 * @note Address: 0x14000AB5A
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?Release@CRaceBuffInfoByHolyQuestfGroup@@QEAA_NHPEAVCPlayer@@@Z
 *Address: 0x14000AB5A
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


bool __fastcall CRaceBuffInfoByHolyQuestfGroup::Release(CRaceBuffInfoByHolyQuestfGroup *this, int iResultType, CPlayer *pkDest)
{
  return CRaceBuffInfoByHolyQuestfGroup::Release(this, iResultType, pkDest);
}



} // namespace Player
} // namespace RFOnline
