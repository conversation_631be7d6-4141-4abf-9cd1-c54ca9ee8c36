/**
 * @file sizevectorVCGuildBattleRewardItemGUILD_BATTLEVallo_1403D2270_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: sizevectorVCGuildBattleRewardItemGUILD_BATTLEVallo_1403D2270
 * @note Address: 0x1403D2270
 */

#include <cstdint>

namespace RFOnline {
namespace Combat {

﻿/*
 *Function: ?size@?$std::vector@VCGuildBattleRewardItem@GUILD_BATTLE@@V?$allocator@VCGuildBattleRewardItem@GUILD_BATTLE@@@std@@@std@@QEBA_KXZ
 *Address: 0x1403D2270
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"
#include <vector>

// Additional STL includes for compilation
#include <memory>



__int64 __fastcall std::vector<GUILD_BATTLE::CGuildBattleRewardItem,std::allocator<GUILD_BATTLE::CGuildBattleRewardItem > >::size(std::vector<GUILD_BATTLE::CGuildBattleRewardItem,std::allocator<GUILD_BATTLE::CGuildBattleRewardItem>  > *this)
{
  __int64*v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-18h]@1
  std::vector<GUILD_BATTLE::CGuildBattleRewardItem,std::allocator<GUILD_BATTLE::CGuildBattleRewardItem>  > *v5; // [sp+20h] [bp+8h]@1

  v5 = this;
  v1 = &v4;
  void for(i = 4; i > 0; --i );
{
    *(DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  void if(v5->_Myfirst )
    v4 = v5->_Mylast - v5->_Myfirst;
  else
    v4 = 0;
  return v4;
}





} // namespace Combat
} // namespace RFOnline
