/**
 * @file j_pc_GuildRoomRentRequestCPlayerQEAAXPEAU_guildroo_140003C2E_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_GuildRoomRentRequestCPlayerQEAAXPEAU_guildroo_140003C2E
 * @note Address: 0x140003C2E
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_GuildRoomRentRequest@CPlayer@@QEAAXPEAU_guildroom_rent_request_clzo@@@Z
 *Address: 0x140003C2E
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_GuildRoomRentRequest(CPlayer *this, _guildroom_rent_request_clzo *pProtocol)
{
  CPlayer::pc_GuildRoomRentRequest(this, pProtocol);
}



} // namespace Player
} // namespace RFOnline
