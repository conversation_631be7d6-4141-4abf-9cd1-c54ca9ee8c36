/**
 * @file j_SendMsg_Assist_ForceCMonsterQEAAXEPEAVCCharacter_14000ADB2_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_SendMsg_Assist_ForceCMonsterQEAAXEPEAVCCharacter_14000ADB2
 * @note Address: 0x14000ADB2
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?SendMsg_Assist_Force@CMonster@@QEAAXEPEAVCCharacter@@PEAU_force_fld@@H@Z
 *Address: 0x14000ADB2
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CMonster::SendMsg_Assist_Force(CMonster *this, char byErrCode, CCharacter *pDst, _force_fld *pForc_fld, int nSFLv)
{
  CMonster::SendMsg_Assist_Force(this, byErrCode, pDst, pForc_fld, nSFLv);
}



} // namespace Player
} // namespace RFOnline
