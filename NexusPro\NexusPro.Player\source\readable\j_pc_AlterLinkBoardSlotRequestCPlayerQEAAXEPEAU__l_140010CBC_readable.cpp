/**
 * @file j_pc_AlterLinkBoardSlotRequestCPlayerQEAAXEPEAU__l_140010CBC_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_AlterLinkBoardSlotRequestCPlayerQEAAXEPEAU__l_140010CBC
 * @note Address: 0x140010CBC
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_AlterLinkBoardSlotRequest@CPlayer@@QEAAXEPEAU__list@_alter_link_slot_request_clzo@@E@Z
 *Address: 0x140010CBC
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_AlterLinkBoardSlotRequest(CPlayer *this, char byNum, _alter_link_slot_request_clzo::__list *pList, char by<PERSON><PERSON><PERSON>)
{
  CPlayer::pc_AlterLinkBoardSlotRequest(this, byNum, pList, byLBLock);
}



} // namespace Player
} // namespace RFOnline
