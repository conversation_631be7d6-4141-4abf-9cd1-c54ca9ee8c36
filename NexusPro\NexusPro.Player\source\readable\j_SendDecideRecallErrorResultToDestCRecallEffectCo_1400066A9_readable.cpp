/**
 * @file j_SendDecideRecallErrorResultToDestCRecallEffectCo_1400066A9_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_SendDecideRecallErrorResultToDestCRecallEffectCo_1400066A9
 * @note Address: 0x1400066A9
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?SendDecideRecallErrorResultToDest@CRecallEffectController@@IEAAXEPEAVCPlayer@@H@Z
 *Address: 0x1400066A9
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CRecallEffectController::SendDecideRecallErrorResultToDest(CRecallEffectController *this, char byErr, CPlayer *pkDest, int nCallerMapCode)
{
  CRecallEffectController::SendDecideRecallErrorResultToDest(this, byErr, pkDest, nCallerMapCode);
}



} // namespace Player
} // namespace RFOnline
