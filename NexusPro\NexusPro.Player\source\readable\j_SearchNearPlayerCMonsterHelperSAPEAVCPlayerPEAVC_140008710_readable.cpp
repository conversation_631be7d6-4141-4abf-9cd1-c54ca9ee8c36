/**
 * @file j_SearchNearPlayerCMonsterHelperSAPEAVCPlayerPEAVC_140008710_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_SearchNearPlayerCMonsterHelperSAPEAVCPlayerPEAVC_140008710
 * @note Address: 0x140008710
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?SearchNearPlayer@CMonsterHelper@@SAPEAVCPlayer@@PEAVCMonster@@H@Z
 *Address: 0x140008710
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


CPlayer *__fastcall CMonsterHelper::SearchNearPlayer(CMonster *pMon, int nType)
{
  return CMonsterHelper::SearchNearPlayer(pMon, nType);
}



} // namespace Player
} // namespace RFOnline
