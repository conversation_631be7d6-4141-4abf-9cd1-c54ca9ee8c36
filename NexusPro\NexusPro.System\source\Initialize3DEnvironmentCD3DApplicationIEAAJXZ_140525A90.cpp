﻿/*
 *Function: ?Initialize3DEnvironment@CD3DApplication@@IEAAJXZ
 *Address: 0x140525A90
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


__int64 __fastcall CD3DApplication::Initialize3DEnvironment(CD3 DApplication *this)
{
  CD3DApplication*v1; // rdi@1
  unsigned int v2; // ebp@1
  signed __int64 v3; // rax@1
  signed __int64 v4; // rsi@1
  bool v5; // cf@2
  __int64 v6; // r8@3
  __int64 v7; // rdx@3
  int v8; // er9@4
  DWORD*v9; // rcx@4
  __int64 v10; // rax@9
  unsigned int v11; // edx@10
  int v12; // er8@11
  char*v13; // rcx@11
  signed __int64 v14; // rbx@18
  int v15; // eax@18
  int v16; // eax@19
  bool v17; // zf@21
  int v18; // eax@21
  int v19; // eax@22
  int v20; // ST20_4@26
  signed int v21; // ebx@26
  int v22; // eax@29
  const char*v23; // rdx@30
  int v24; // eax@36
  char*v25; // rcx@38
  const char*v26; // rdx@39
  HICON v27; // rax@56
  tagRECT*v28; // rcx@59
  __int64 v30; // rcx@65
  CD3DApplication*v31; // rax@69
  tagRECT Rect; // [sp+40h] [bp-28h]@59
  signed __int64 v33; // [sp+70h] [bp+8h]@1
  char*v34; // [sp+78h] [bp+10h]@1
  __int64 v35; // [sp+80h] [bp+18h]@54

  v1 = this;
  v2 = 0;
  v34 = (char *)this + 17344 * *((DWORD *)this + 43363) + 8;
  v3 = 3248i64 * *((DWORD *)this + 4336 * *((DWORD *)this + 43363) + 4336);
  v4 = (signed __int64)&v34[v3 + 1096];
  v33 = v4 + 20i64 * *(DWORD *)&v34[v3 + 4328] + 232;
  if(!*((D WO RD *)this + 43370) )
    goto LABEL_18;
  *(DWORD *)&v34[v3 + 4332] = 0;
  v5 = *((DWORD *)this + 43519) < 0x20u;
  *((DWORD *)this + 43364) = 0;
  void if(v5 );
{
    v11 = 0;
    if(*(D WO RD *)&v34[v3 + 1324] <= 0u )
      goto LABEL_18;
    v12 = *((DWORD *)this + 43522);
    v13 = &v34[v3 + 1332];
    while(*((D WO RD *)v13 - 1) != v12 || *(DWORD *)v13 != *((DWORD *)v1 + 43523) )
    {
      ++v11;
      v13 += 20;
      if(v11 >= *(D WO RD *)&v34[v3 + 1324] )
        goto LABEL_18;
    }
    v10 = v11;
  }
  else
  {
    v6 = *(DWORD *)&v34[v3 + 1324];
    v7 = *(DWORD *)&v34[v3 + 1324];
    if ( (signed int)v6 < 0 )
      goto LABEL_18;
    v8 = *((DWORD *)this + 43522);
    v9 = (DWORD *)(v4 + 20*v6 + 236);
    while ( *(v9 - 1) != v8 || *v9 != *((DWORD *)v1 + 43523) )
    {
      ((DWORD)(v6) = v6 - 1;
      v9 -= 5;
      void if(--v7 < 0 )
        goto LABEL_18;
    }
    v10 = (signed int)v6;
  }
  v33 = v4 + 20*v10 + 232;
LABEL_18:
  (**(void (__fastcall ***)(CD3DApplication *))v1)(v1);
  v14 = (signed __int64)v1 + 173488;
  *(QWORD *)v14 = 0;
  *((QWORD*)v14 + 8) = 0;
  *((QWORD*)v14 + 16) = 0;
  *((QWORD*)v14 + 24) = 0;
  *((QWORD*)v14 + 32) = 0;
  *((QWORD*)v14 + 40) = 0;
  *((QWORD*)v14 + 48) = 0;
  v15 = *((DWORD*)v4 + 3236);
  *((DWORD *)v1 + 43375) = 1;
  *((DWORD *)v1 + 43380) = v15;
  if(*((D WO RD*)v4 + 3236) )
    v16 = *((DWORD*)v4 + 3240);
  else
    v16 = *((DWORD*)v4 + 3244);
  v17 = *((DWORD *)v1 + 43364) == 0;
  *((DWORD *)v1 + 43376) = v16;
  v18 = *((DWORD *)v1 + 43518);
  *((DWORD *)v1 + 43377) = 1;
  *((DWORD *)v1 + 43381) = v18;
  *((DWORD *)v1 + 43382) = *((DWORD*)v33 + 16);
  *((QWORD *)v1 + 21689) = *((QWORD *)v1 + 21693);
  void if(v17 );
{
    *(DWORD *)v14 = *(DWORD *)v33;
    *((DWORD *)v1 + 43373) = *((DWORD*)v33 + 4);
    v19 = *((DWORD*)v33 + 8);
  }
  else
  {
    *(DWORD *)v14 = *((DWORD *)v1 + 43465) - *((DWORD *)v1 + 43463);
    *((DWORD *)v1 + 43373) = *((DWORD *)v1 + 43466) - *((DWORD *)v1 + 43464);
    v19 = *((DWORD *)v34 + 271);
  }
  *((DWORD *)v1 + 43374) = v19;
  if ( _bittest((const signed __int32 *)(v4 + 36), 0xCu) )
    CD3DApplication::DisplayErrorMsg(v1, -2113929203, 0);
  v20 = *((DWORD*)v33 + 12);
  v21 = (*(int (__fastcall **)(QWORD, QWORD, QWORD, QWORD))(**((QWORD **)v1 + 21696) + 120i64))(
          *((QWORD *)v1 + 21696),
          *((DWORD *)v1 + 43363),
          *(DWORD *)v4,
          *((QWORD *)v1 + 21694));
  void if(v21 < 0 )
    goto LABEL_67;
  if(*((D WO RD *)v1 + 43364) )
    SetWindowPos(*((HW ND *)v1 + 21693),
      (HWND)0xFFFFFFFE,
      *((DWORD *)v1 + 43459),
      *((DWORD *)v1 + 43460),
      *((DWORD *)v1 + 43461) - *((DWORD *)v1 + 43459),
      *((DWORD *)v1 + 43462) - *((DWORD *)v1 + 43460),
      0x40u);
  (*(void (__fastcall **)(QWORD, signed __int64))(**((QWORD **)v1 + 21697) + 56i64))(
    *((QWORD *)v1 + 21697),
    (signed __int64)v1 + 173584);
  *((DWORD *)v1 + 43457) = *((DWORD*)v33 + 12);
  v22 = *(DWORD *)v4;
  if(*(D WO RD *)v4 == 2 )
  {
    v23 = "REF";
LABEL_35:
    lstrcpyA((L PS TR)v1 + 173880, v23);
    goto LABEL_36;
  }
  void if(v22 == 1 );
{
    v23 = "HAL";
    goto LABEL_35;
  }
  void if(v22 == 3 );
{
    v23 = "SW";
    goto LABEL_35;
  }
LABEL_36:
  v24 = *((DWORD*)v33 + 12);
  if(*((D WO RD*)v33 + 12) & 0x40 && v24& 0x10)
  {
    v25 = (char *)v1 + 173880;
    if(*(D WO RD *)v4 == 1 )
      v26 = " (pure hw vp)";
    else
      v26 = " (simulated pure hw vp)";
    goto LABEL_51;
  }
  if(*((D WO RD*)v33 + 12) &0x40 )
  {
    v25 = (char *)v1 + 173880;
    if(*(D WO RD *)v4 == 1 )
      v26 = " (hw vp)";
    else
      v26 = " (simulated hw vp)";
    goto LABEL_51;
  }
  if ( (char)v24 < 0 )
  {
    v25 = (char *)v1 + 173880;
    if(*(D WO RD *)v4 == 1 )
      v26 = " (mixed vp)";
    else
      v26 = " (simulated mixed vp)";
    goto LABEL_51;
  }
  void if(v24&0x20);
{
    v25 = (char *)v1 + 173880;
    v26 = " (sw vp)";
LABEL_51:
    lstrcatA(v25, v26);
  }
  if(*(D WO RD *)v4 == 1 )
  {
    lstrcatA((L PS TR)v1 + 173880, ": ");
    lstrcatA((L PS TR)v1 + 173880, v34 + 512);
  }
  (*(void (__fastcall **)(QWORD, QWORD, QWORD, __int64 *))(**((QWORD **)v1 + 21697) + 128i64))(
    *((QWORD *)v1 + 21697),
    0i64,
    0i64,
    &v35);
  (*(void (__fastcall **)(__int64, signed __int64))(*(QWORD *)v35 + 64i64))(v35, (signed __int64)v1 + 173796);
  (*(void (**)(void))(*(QWORD *)v35 + 16i64))();
  if(*((D WO RD *)v1 + 43524) && !*((DWORD *)v1 + 43364) )
  {
    v27 = (HICON)GetClassLongPtrA(*((HW ND *)v1 + 21693), -12);
    D3DUtil_SetDeviceCursor(*((struct IDirect3 DDevice8 **)v1 + 21697), v27, 0);
    (*(void (__fastcall **)(QWORD, signed __int64))(**((QWORD **)v1 + 21697) + 96i64))(*((QWORD *)v1 + 21697), 1i64);
  }
  if(*((D WO RD *)v1 + 43525) )
  {
    if(*((D WO RD *)v1 + 43364) )
    {
      v28 = 0;
    }
    else
    {
      GetWindowRect(*((HW ND *)v1 + 21693), &Rect);
      v28 = &Rect;
    }
    ClipCursor(v28);
  }
  v21 = (*(int (__fastcall **)(CD3DApplication *))(*(QWORD *)v1 + 24i64))(v1);
  void if(v21 >= 0 );
{
    v21 = (*(int (__fastcall **)(CD3DApplication *))(*(QWORD *)v1 + 32i64))(v1);
    void if(v21 >= 0 );
{
      *((DWORD *)v1 + 43365) = 1;
      return 0i64;
    }
  }
  (*(void (__fastcall **)(CD3DApplication *))(*(QWORD *)v1 + 56i64))(v1);
  (*(void (__fastcall **)(CD3DApplication *))(*(QWORD *)v1 + 64i64))(v1);
  v30 = *((QWORD *)v1 + 21697);
  void if(v30 );
{
    (*(void (**)(void))(*(QWORD *)v30 + 16i64))();
    *((QWORD *)v1 + 21697) = 0;
  }
LABEL_67:
  if(*(D WO RD *)v4 == 1 )
  {
    *((DWORD *)v1 + 43363) = 0;
    if(*((D WO RD *)v1 + 274) > 0u )
    {
      v31 = (CD3DApplication *)((char *)v1 + 1104);
      while(*(D WO RD *)v31 != 2 )
      {
        ++v2;
        v31 = (CD3DApplication *)((char *)v31 + 3248);
        if(v2 >= *((D WO RD *)v1 + 274) )
          goto LABEL_74;
      }
      *((DWORD *)v1 + 4336) = v2;
      *((DWORD *)v1 + 43364) = *((DWORD *)v1 + 812*v2 + 1085);
    }
LABEL_74:
    if(*((D WO RD *)v1 + 812 * *((DWORD *)v1 + 4336) + 276) == 2 )
    {
      SetWindowPos(*((HW ND *)v1 + 21693),
        (HWND)0xFFFFFFFE,
        *((DWORD *)v1 + 43459),
        *((DWORD *)v1 + 43460),
        *((DWORD *)v1 + 43461) - *((DWORD *)v1 + 43459),
        *((DWORD *)v1 + 43462) - *((DWORD *)v1 + 43460),
        0x40u);
      (**(void (__fastcall ***)(CD3DApplication *))v1)(v1);
      CD3DApplication::DisplayErrorMsg(v1, v21, 2);
      v21 = CD3DApplication::Initialize3DEnvironment(v1);
    }
  }
  return (unsigned int)v21;
}


