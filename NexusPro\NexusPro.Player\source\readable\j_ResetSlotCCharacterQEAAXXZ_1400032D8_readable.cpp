/**
 * @file j_ResetSlotCCharacterQEAAXXZ_1400032D8_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_ResetSlotCCharacterQEAAXXZ_1400032D8
 * @note Address: 0x1400032D8
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?ResetSlot@CCharacter@@QEAAXXZ
 *Address: 0x1400032D8
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CCharacter::ResetSlot(CCharacter *this)
{
  CCharacter::ResetSlot(this);
}



} // namespace Player
} // namespace RFOnline
