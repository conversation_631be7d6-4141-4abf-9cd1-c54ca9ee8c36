﻿/*
 *Function: ?Init@_ECONOMY_SYSTEM@@QEAAXXZ
 *Address: 0x1402A5B40
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall _ECONOMY_SYSTEM::Init(_ECONOMY_SY ST EM *this)
{
  __int64*v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  _ECONOMY_SYSTEM*v4; // [sp+30h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  void for(i = 8; i > 0; --i );
{
    *(DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v4->m_bLoad = 0;
  _ECONOMY_SYSTEM::CurTradeMoneyInit(v4);
  v4->m_dwLastUpdateTime = timeGetTime();
  v4->m_dwSystemOperStartTime = timeGetTime();
}


