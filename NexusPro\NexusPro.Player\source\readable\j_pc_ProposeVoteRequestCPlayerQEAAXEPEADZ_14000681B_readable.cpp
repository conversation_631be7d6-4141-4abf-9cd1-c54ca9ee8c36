/**
 * @file j_pc_ProposeVoteRequestCPlayerQEAAXEPEADZ_14000681B_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_ProposeVoteRequestCPlayerQEAAXEPEADZ_14000681B
 * @note Address: 0x14000681B
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_ProposeVoteRequest@CPlayer@@QEAAXEPEAD@Z
 *Address: 0x14000681B
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_ProposeVoteRequest(CPlayer *this, char byLimGrade, char *pwszCont)
{
  CPlayer::pc_ProposeVoteRequest(this, byLimGrade, pwszCont);
}



} // namespace Player
} // namespace RFOnline
