/**
 * @file j_SetVoteCPlayerQEAAXHZ_1400100E6_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_SetVoteCPlayerQEAAXHZ_1400100E6
 * @note Address: 0x1400100E6
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?SetVote@CPlayer@@QEAAXH@Z
 *Address: 0x1400100E6
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::SetVote(CPlayer *this, int nSerial)
{
  CPlayer::SetVote(this, nSerial);
}



} // namespace Player
} // namespace RFOnline
