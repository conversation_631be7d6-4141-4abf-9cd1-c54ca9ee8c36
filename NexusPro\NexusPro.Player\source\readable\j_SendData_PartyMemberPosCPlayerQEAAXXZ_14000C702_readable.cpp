/**
 * @file j_SendData_PartyMemberPosCPlayerQEAAXXZ_14000C702_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_SendData_PartyMemberPosCPlayerQEAAXXZ_14000C702
 * @note Address: 0x14000C702
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?SendData_PartyMemberPos@CPlayer@@QEAAXXZ
 *Address: 0x14000C702
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::SendData_PartyMemberPos(CPlayer *this)
{
  CPlayer::SendData_PartyMemberPos(this);
}



} // namespace Player
} // namespace RFOnline
