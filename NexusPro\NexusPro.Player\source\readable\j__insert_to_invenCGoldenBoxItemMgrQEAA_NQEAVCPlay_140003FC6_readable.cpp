/**
 * @file j__insert_to_invenCGoldenBoxItemMgrQEAA_NQEAVCPlay_140003FC6_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j__insert_to_invenCGoldenBoxItemMgrQEAA_NQEAVCPlay_140003FC6
 * @note Address: 0x140003FC6
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?_insert_to_inven@CGoldenBoxItemMgr@@QEAA_NQEAVCPlayer@@EG@Z
 *Address: 0x140003FC6
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


bool __fastcall CGoldenBoxItemMgr::_insert_to_inven(CGoldenBoxItemMgr *this, CPlayer *const pOne, char byTableCode, unsigned __int16 wItemIndex)
{
  return CGoldenBoxItemMgr::_insert_to_inven(this, pOne, byTableCode, wItemIndex);
}



} // namespace Player
} // namespace RFOnline
