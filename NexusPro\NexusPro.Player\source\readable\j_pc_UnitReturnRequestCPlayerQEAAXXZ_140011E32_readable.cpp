/**
 * @file j_pc_UnitReturnRequestCPlayerQEAAXXZ_140011E32_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_UnitReturnRequestCPlayerQEAAXXZ_140011E32
 * @note Address: 0x140011E32
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_UnitReturnRequest@CPlayer@@QEAAXXZ
 *Address: 0x140011E32
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_UnitReturnRequest(CPlayer *this)
{
  CPlayer::pc_UnitReturnRequest(this);
}



} // namespace Player
} // namespace RFOnline
