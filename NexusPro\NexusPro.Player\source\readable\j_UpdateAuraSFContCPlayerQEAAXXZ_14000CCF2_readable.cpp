/**
 * @file j_UpdateAuraSFContCPlayerQEAAXXZ_14000CCF2_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_UpdateAuraSFContCPlayerQEAAXXZ_14000CCF2
 * @note Address: 0x14000CCF2
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?UpdateAuraSFCont@CPlayer@@QEAAXXZ
 *Address: 0x14000CCF2
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::UpdateAuraSFCont(CPlayer *this)
{
  CPlayer::UpdateAuraSFCont(this);
}



} // namespace Player
} // namespace RFOnline
