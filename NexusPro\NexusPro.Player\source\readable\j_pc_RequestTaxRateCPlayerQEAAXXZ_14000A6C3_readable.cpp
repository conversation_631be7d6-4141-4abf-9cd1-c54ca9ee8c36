/**
 * @file j_pc_RequestTaxRateCPlayerQEAAXXZ_14000A6C3_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_RequestTaxRateCPlayerQEAAXXZ_14000A6C3
 * @note Address: 0x14000A6C3
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_RequestTaxRate@CPlayer@@QEAAXXZ
 *Address: 0x14000A6C3
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_RequestTaxRate(CPlayer *this)
{
  CPlayer::pc_RequestTaxRate(this);
}



} // namespace Player
} // namespace RFOnline
