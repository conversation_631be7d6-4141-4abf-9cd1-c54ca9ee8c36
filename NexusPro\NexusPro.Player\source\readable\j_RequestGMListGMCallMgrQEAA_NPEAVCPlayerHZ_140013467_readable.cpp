/**
 * @file j_RequestGMListGMCallMgrQEAA_NPEAVCPlayerHZ_140013467_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_RequestGMListGMCallMgrQEAA_NPEAVCPlayerHZ_140013467
 * @note Address: 0x140013467
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?RequestGMList@GMCallMgr@@QEAA_NPEAVCPlayer@@H@Z
 *Address: 0x140013467
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


bool __fastcall GMCallMgr::RequestGMList(GMCallMgr *this, CPlayer *pOne, int nCurrPageIndex)
{
  return GMCallMgr::RequestGMList(this, pOne, nCurrPageIndex);
}



} // namespace Player
} // namespace RFOnline
