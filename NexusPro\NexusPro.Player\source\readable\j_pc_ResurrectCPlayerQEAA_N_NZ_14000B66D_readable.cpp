/**
 * @file j_pc_ResurrectCPlayerQEAA_N_NZ_14000B66D_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_ResurrectCPlayerQEAA_N_NZ_14000B66D
 * @note Address: 0x14000B66D
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_Resurrect@CPlayer@@QEAA_N_N@Z
 *Address: 0x14000B66D
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


bool __fastcall CPlayer::pc_Resurrect(CPlayer *this, bool bQuickPotion)
{
  return CPlayer::pc_Resurrect(this, bQuickPotion);
}



} // namespace Player
} // namespace RFOnline
