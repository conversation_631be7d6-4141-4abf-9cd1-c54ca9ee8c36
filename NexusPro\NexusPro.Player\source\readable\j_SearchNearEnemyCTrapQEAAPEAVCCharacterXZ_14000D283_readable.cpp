/**
 * @file j_SearchNearEnemyCTrapQEAAPEAVCCharacterXZ_14000D283_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_SearchNearEnemyCTrapQEAAPEAVCCharacterXZ_14000D283
 * @note Address: 0x14000D283
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?SearchNearEnemy@CTrap@@QEAAPEAVCCharacter@@XZ
 *Address: 0x14000D283
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


CCharacter *__fastcall CTrap::SearchNearEnemy(CTrap *this)
{
  return CTrap::SearchNearEnemy(this);
}



} // namespace Player
} // namespace RFOnline
