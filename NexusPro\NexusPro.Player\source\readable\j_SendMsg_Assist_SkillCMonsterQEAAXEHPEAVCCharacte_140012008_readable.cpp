/**
 * @file j_SendMsg_Assist_SkillCMonsterQEAAXEHPEAVCCharacte_140012008_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_SendMsg_Assist_SkillCMonsterQEAAXEHPEAVCCharacte_140012008
 * @note Address: 0x140012008
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?SendMsg_Assist_Skill@CMonster@@QEAAXEHPEAVCCharacter@@PEAU_skill_fld@@H@Z
 *Address: 0x140012008
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CMonster::SendMsg_Assist_Skill(CMonster *this, char byErrCode, int nEffectCode, CCharacter *pDst, _skill_fld *pSkill_fld, int nSFLv)
{
  CMonster::SendMsg_Assist_Skill(this, byErrCode, nEffectCode, pDst, pSkill_fld, nSFLv);
}



} // namespace Player
} // namespace RFOnline
