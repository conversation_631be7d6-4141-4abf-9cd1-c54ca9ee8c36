/**
 * @file j_Select_NotArrangeCharacterCRFWorldDatabaseQEAAEK_14000F9FC_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_Select_NotArrangeCharacterCRFWorldDatabaseQEAAEK_14000F9FC
 * @note Address: 0x14000F9FC
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?Select_NotArrangeCharacter@CRFWorldDatabase@@QEAAEKPEAU_worlddb_arrange_char_info@@@Z
 *Address: 0x14000F9FC
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


char __fastcall CRFWorldDatabase::Select_NotArrangeCharacter(CRFWorldDatabase *this, unsigned int dwAccountSerial, _worlddb_arrange_char_info *pCharData)
{
  return CRFWorldDatabase::Select_NotArrangeCharacter(this, dwAccountSerial, pCharData);
}



} // namespace Player
} // namespace RFOnline
