/**
 * @file j_SendMsg_AlterRegionInformCPlayerQEAAXHZ_14000E09D_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_SendMsg_AlterRegionInformCPlayerQEAAXHZ_14000E09D
 * @note Address: 0x14000E09D
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?SendMsg_AlterRegionInform@CPlayer@@QEAAXH@Z
 *Address: 0x14000E09D
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::SendMsg_AlterRegionInform(CPlayer *this, int nRegionIndex)
{
  CPlayer::SendMsg_AlterRegionInform(this, nRegionIndex);
}



} // namespace Player
} // namespace RFOnline
