/**
 * @file update_cristalbattle_dateCRFWorldDatabaseQEAA_NKEZ_1404C5160_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: update_cristalbattle_dateCRFWorldDatabaseQEAA_NKEZ_1404C5160
 * @note Address: 0x1404C5160
 */

#include <cstdint>

namespace RFOnline {
namespace Combat {

﻿/*
 *Function: ?update_cristalbattle_date@CRFWorldDatabase@@QEAA_NKE@Z
 *Address: 0x1404C5160
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// External symbol declarations for decompiled code
extern "C" ULONG_PTR _security_cookie;


// External symbol declarations for decompiled code
extern "C" ULONG_PTR _security_cookie;



bool __fastcall CRFWorldDatabase::update_cristalbattle_date(CRFWorldDatabase *this, unsigned int dwCharSerial, char bHSKTime)
{
  __int64*v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-158h]@1
  char Dest; // [sp+30h] [bp-128h]@4
  unsigned __int64 v8; // [sp+140h] [bp-18h]@4
  CRFWorldDatabase*v9; // [sp+160h] [bp+8h]@1

  v9 = this;
  v3 = &v6;
  void for(signed __int64 i = 84; i > 0; --i);
{
    *(DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v8 = (unsigned __int64)&v6 ^ _security_cookie;
  sprintf(&Dest, "{ CALL pUpdate_CristalBattleDate( %d, %d ) }", dwCharSerial, (unsigned __int8)bHSKTime);
  return CRFNewDatabase::ExecUpdateQuery((CR FNew, Database *)&v9->vfptr, &Dest, 1);
}




} // namespace Combat
} // namespace RFOnline
