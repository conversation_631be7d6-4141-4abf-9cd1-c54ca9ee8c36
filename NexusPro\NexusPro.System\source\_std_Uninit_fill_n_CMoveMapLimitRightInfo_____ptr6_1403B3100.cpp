﻿/*
 *Function: _std::_Uninit_fill_n_CMoveMapLimitRightInfo_____ptr64_unsigned___int64_CMoveMapLimitRightInfo_std::allocator_CMoveMapLimitRightInfo____::_1_::catch$0
 *Address: 0x1403B3100
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// Additional STL includes for compilation
#include <memory>



void __fastcall __noreturn std::_Uninit_fill_n_CMoveMapLimitRightInfo_____ptr64_unsigned___int64_CMoveMapLimitRightInfo_std::allocator_CMoveMapLimitRightInfo____::_1_::catch_0(__int64 a1, __int64 a2)
{
  __int64 i; // rbp@1

  for(i = a2; *((Q WO RD*)i + 32) != *((QWORD*)i + 64); *((QWORD*)i + 32) += 40i64 )
    typename std::allocator<CMoveMapLimitRightInfo>::destroy(*(std::allocator<CMove, Map, Limit, Right, Info> **)(i + 88),
      *(CMoveMapLimitRightInfo **)(i + 32));
  CxxThrowException_0(0i64, 0i64);
}



