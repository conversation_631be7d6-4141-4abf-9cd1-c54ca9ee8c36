/**
 * @file j_pc_RevivalCPlayerQEAAX_NZ_1400025FE_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_RevivalCPlayerQEAAX_NZ_1400025FE
 * @note Address: 0x1400025FE
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_Revival@CPlayer@@QEAAX_N@Z
 *Address: 0x1400025FE
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_Revival(CPlayer *this, bool bUseableJade)
{
  CPlayer::pc_Revival(this, bUseableJade);
}



} // namespace Player
} // namespace RFOnline
