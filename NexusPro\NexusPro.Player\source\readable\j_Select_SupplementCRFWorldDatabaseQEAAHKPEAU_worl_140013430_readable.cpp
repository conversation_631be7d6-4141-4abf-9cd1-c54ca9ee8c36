/**
 * @file j_Select_SupplementCRFWorldDatabaseQEAAHKPEAU_worl_140013430_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_Select_SupplementCRFWorldDatabaseQEAAHKPEAU_worl_140013430
 * @note Address: 0x140013430
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?Select_Supplement@CRFWorldDatabase@@QEAAHKPEAU_worlddb_character_supplement_info@@@Z
 *Address: 0x140013430
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


int __fastcall CRFWorldDatabase::Select_Supplement(CRFWorldDatabase *this, unsigned int dwSerial, _worlddb_character_supplement_info *pSupplement)
{
  return CRFWorldDatabase::Select_Supplement(this, dwSerial, pSupplement);
}



} // namespace Player
} // namespace RFOnline
