/**
 * @file j_SendMsg_AnimusInvenChangeCPlayerQEAAXEZ_1400051D2_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_SendMsg_AnimusInvenChangeCPlayerQEAAXEZ_1400051D2
 * @note Address: 0x1400051D2
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?SendMsg_AnimusInvenChange@CPlayer@@QEAAXE@Z
 *Address: 0x1400051D2
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::SendMsg_AnimusInvenChange(CPlayer *this, char byErrCode)
{
  CPlayer::SendMsg_AnimusInvenChange(this, byErrCode);
}



} // namespace Player
} // namespace RFOnline
