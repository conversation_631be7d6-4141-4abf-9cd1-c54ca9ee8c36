/**
 * @file j_ProcessCheatCommandYA_NPEAVCPlayerPEADZ_140008DF0_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_ProcessCheatCommandYA_NPEAVCPlayerPEADZ_140008DF0
 * @note Address: 0x140008DF0
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?ProcessCheatCommand@@YA_NPEAVCPlayer@@PEAD@Z
 *Address: 0x140008DF0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


bool __fastcall ProcessCheatCommand(CPlayer *pOne, char *pwszCommand);
{
  return ProcessCheatCommand(pOne, pwszCommand);
}



} // namespace Player
} // namespace RFOnline
