/**
 * @file j_pc_MineCompleteCPlayerQEAAXXZ_140004F2A_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_MineCompleteCPlayerQEAAXXZ_140004F2A
 * @note Address: 0x140004F2A
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_MineComplete@CPlayer@@QEAAXXZ
 *Address: 0x140004F2A
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_MineComplete(CPlayer *this)
{
  CPlayer::pc_MineComplete(this);
}



} // namespace Player
} // namespace RFOnline
