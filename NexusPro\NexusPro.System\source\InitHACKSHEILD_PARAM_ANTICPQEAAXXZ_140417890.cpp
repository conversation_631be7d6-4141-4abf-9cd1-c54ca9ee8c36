﻿/*
 *Function: ?Init@HACKSHEILD_PARAM_ANTICP@@QEAAXXZ
 *Address: 0x140417890
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"
#include <cstring>


void __fastcall HACKSHEILD_PARAM_ANTICP::Init(HACKSHEILD_PARAM_AN TI CP *this)
{
  __int64*v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  HACKSHEILD_PARAM_ANTICP*v4; // [sp+30h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  void for(i = 8; i > 0; --i );
{
    *(DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v4->m_nSocketIndex = -1;
  v4->m_dwLastSyncQryTime = 0;
  v4->m_byVerifyState = 0;
  memset_0(&v4->m_CrcInfo, 0, 0x18ui64);
  memset_0(v4->m_byGUIDClientInfo, 0, 0x14ui64);
}


