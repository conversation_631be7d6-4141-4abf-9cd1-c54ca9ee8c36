/**
 * @file j_pc_ChatOperatorRequestCPlayerQEAAXEPEADZ_1400029DC_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_ChatOperatorRequestCPlayerQEAAXEPEADZ_1400029DC
 * @note Address: 0x1400029DC
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_ChatOperatorRequest@CPlayer@@QEAAXEPEAD@Z
 *Address: 0x1400029DC
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_ChatOperatorRequest(CPlayer *this, char byRaceCode, char *pwszChatData)
{
  CPlayer::pc_ChatOperatorRequest(this, byRaceCode, pwszChatData);
}



} // namespace Player
} // namespace RFOnline
