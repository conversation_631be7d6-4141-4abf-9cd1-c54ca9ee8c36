/**
 * @file j__GetTempEffectValueYA_NPEAU_skill_fldHAEAMZ_140004AC0_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j__GetTempEffectValueYA_NPEAU_skill_fldHAEAMZ_140004AC0
 * @note Address: 0x140004AC0
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?_GetTempEffectValue@@YA_NPEAU_skill_fld@@HAEAM@Z
 *Address: 0x140004AC0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


bool __fastcall _GetTempEffectValue(_skill_fld *pEffectFld, int nTempEffectType, float *fValue);
{
  return _GetTempEffectValue(pEffectFld, nTempEffectType, fValue);
}



} // namespace Player
} // namespace RFOnline
