/**
 * @file j_SendMsg_Alter_Action_PointCPlayerQEAAXEKZ_14000640B_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_SendMsg_Alter_Action_PointCPlayerQEAAXEKZ_14000640B
 * @note Address: 0x14000640B
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?SendMsg_Alter_Action_Point@CPlayer@@QEAAXEK@Z
 *Address: 0x14000640B
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::SendMsg_Alter_Action_Point(CPlayer *this, char byActCode, unsigned int dwActPoint)
{
  CPlayer::SendMsg_Alter_Action_Point(this, byActCode, dwActPoint);
}



} // namespace Player
} // namespace RFOnline
