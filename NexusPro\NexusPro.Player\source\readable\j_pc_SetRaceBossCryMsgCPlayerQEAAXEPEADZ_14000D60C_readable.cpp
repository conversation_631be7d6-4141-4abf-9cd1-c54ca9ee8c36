/**
 * @file j_pc_SetRaceBossCryMsgCPlayerQEAAXEPEADZ_14000D60C_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_SetRaceBossCryMsgCPlayerQEAAXEPEADZ_14000D60C
 * @note Address: 0x14000D60C
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_SetRaceBossCryMsg@CPlayer@@QEAAXEPEAD@Z
 *Address: 0x14000D60C
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_SetRaceBossCryMsg(CPlayer *this, char bySlot, char *pwszCryMsg)
{
  CPlayer::pc_SetRaceBossCryMsg(this, bySlot, pwszCryMsg);
}



} // namespace Player
} // namespace RFOnline
