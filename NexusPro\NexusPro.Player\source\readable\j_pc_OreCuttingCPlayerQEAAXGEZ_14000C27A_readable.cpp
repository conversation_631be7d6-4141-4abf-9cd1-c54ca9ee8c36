/**
 * @file j_pc_OreCuttingCPlayerQEAAXGEZ_14000C27A_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_OreCuttingCPlayerQEAAXGEZ_14000C27A
 * @note Address: 0x14000C27A
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_OreCutting@CPlayer@@QEAAXGE@Z
 *Address: 0x14000C27A
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_OreCutting(CPlayer *this, unsigned __int16 wOreSerial, char byProcessNum)
{
  CPlayer::pc_OreCutting(this, wOreSerial, byProcessNum);
}



} // namespace Player
} // namespace RFOnline
