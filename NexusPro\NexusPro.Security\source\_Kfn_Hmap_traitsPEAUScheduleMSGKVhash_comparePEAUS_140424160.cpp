﻿/*
 *Function: ?_Kfn@?$_Hmap_traits@PEAUScheduleMSG@@KV?$hash_compare@PEAUScheduleMSG@@U?$less@PEAUScheduleMSG@@@std@@@stdext@@V?$allocator@U?$std::pair@QEAUSchedule<PERSON>G@@K@std@@@std@@$0A@@stdext@@SAAEBQEAUScheduleMSG@@AEBU?$std::pair@QEAUScheduleMSG@@K@std@@@Z
 *Address: 0x140424160
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// Additional STL includes for compilation
#include <memory>
#include <utility>



ScheduleMSG *const *__fastcall stdext::_Hmap_traits<ScheduleMSG *,unsigned long,stdext::hash_compare<ScheduleMSG *,std::less<ScheduleMSG * > >,std::allocator<std::pair<ScheduleMSG *const,unsigned long > >,0>::_Kfn(stdext::_Hmap_traits<ScheduleMSG *,unsigned long,stdext::hash_compare<ScheduleMSG *,std::less<ScheduleMSG *>  >,std::allocator<std::pair<ScheduleMSG *const,unsigned long>  >,0> *this, std::pair<ScheduleMSG *const,unsigned long> *_Val)
{
  return(Schedul eM SG*const *)this;
}


