/**
 * @file j_pc_TrunkCreateCostIsFreeRequestCPlayerQEAAEXZ_14000A9B6_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_TrunkCreateCostIsFreeRequestCPlayerQEAAEXZ_14000A9B6
 * @note Address: 0x14000A9B6
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_TrunkCreateCostIsFreeRequest@CPlayer@@QEAAEXZ
 *Address: 0x14000A9B6
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


char __fastcall CPlayer::pc_TrunkCreateCostIsFreeRequest(CPlayer *this)
{
  return CPlayer::pc_TrunkCreateCostIsFreeRequest(this);
}



} // namespace Player
} // namespace RFOnline
