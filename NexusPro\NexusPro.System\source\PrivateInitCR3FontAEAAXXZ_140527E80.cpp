﻿/*
 *Function: ?PrivateInit@CR3Font@@AEAAXXZ
 *Address: 0x140527E80
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"
#include <cstring>


void __fastcall CR3Font::PrivateInit(CR 3Font *this)
{
  CR3Font*v1; // rdi@1
  int v2; // eax@1
  HDC v3; // rax@1
  HBITMAP v4; // rax@1
  int v5; // eax@3
  int v6; // eax@3
  unsigned int v7; // er8@3
  int v8; // edx@3
  HFONT v9; // rax@5
  signed __int64 v10; // kr00_8@6
  int Dst; // [sp+70h] [bp-38h]@1
  int v12; // [sp+74h] [bp-34h]@1
  int v13; // [sp+78h] [bp-30h]@1
  __int16 v14; // [sp+7Ch] [bp-2Ch]@1
  __int16 v15; // [sp+7Eh] [bp-2Ah]@1
  int v16; // [sp+80h] [bp-28h]@1
  tagSIZE sz; // [sp+B0h] [bp+8h]@6

  v1 = this;
  *((DWORD *)this + 24) = 0;
  *((QWORD *)this + 14) = 0;
  *((QWORD *)this + 15) = 0;
  *((QWORD *)this + 16) = 0;
  *((QWORD *)this + 17) = 0;
  *((DWORD *)this + 40) = 0;
  memset_0(&Dst, 0, 0x28ui64);
  v12 = *((DWORD *)v1 + 36);
  v2 = *((DWORD *)v1 + 21);
  Dst = 40;
  v14 = 1;
  v16 = 0;
  v15 = 32;
  v13 = -2*v2;
  v3 = CreateCompatibleDC(0i64);
  *((QWORD *)v1 + 15) = v3;
  v4 = CreateDIBSection(v3, (const BITMAPINFO *)&Dst, 0, (void **)v1 + 17, 0i64, 0);
  *((QWORD *)v1 + 14) = v4;
  void if(v4 );
{
    SetMapMode(*((H DC *)v1 + 15), 1);
    v5 = GetDeviceCaps(*((H DC *)v1 + 15), 90);
    v6 = MulDiv(*((D WO RD *)v1 + 21), (signed int)ffloor((float)v5 * *((float *)v1 + 20)), 72);
    v7 = *((DWORD *)v1 + 523);
    v8 = 400;
    void if(v7&1)
      v8 = 700;
    v9 = CreateFontA(-v6, 0, 0, 0, v8, (v7 >> 1) &1, 0, 0, *((DWORD *)v1 + 27), 0, 0, 0, 0, (LPCSTR)v1 + 16);
    *((QWORD *)v1 + 16) = v9;
    void if(v9 );
{
      SelectObject(*((H DC *)v1 + 15), *((HGDIOBJ *)v1 + 14));
      SelectObject(*((H DC *)v1 + 15), *((HGDIOBJ *)v1 + 16));
      SetTextColor(*((H DC *)v1 + 15), 0xFFFFFFu);
      SetBkColor(*((H DC *)v1 + 15), 0);
      SetTextAlign(*((H DC *)v1 + 15), 0);
      GetTextExtentPointA(*((H DC *)v1 + 15), "w", 1, &sz);
      v10 = *((DWORD *)v1 + 37);
      *((DWORD *)v1 + 23) = sz.cy;
      *((DWORD *)v1 + 22) = sz.cx;
      *((DWORD *)v1 + 38) = v10 / sz.cy;
      *((DWORD *)v1 + 39) = *((DWORD *)v1 + 36) / sz.cx;
      CR3Font::MemAllocate(v1);
      CR3Font::ClearCache(v1);
    }
  }
  else
  {
    Warning("ÆùÆ®ÃÊ±âÈ­ ½ÇÆÐ", byte_140883769);
  }
}


