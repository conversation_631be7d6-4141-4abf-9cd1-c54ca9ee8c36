/**
 * @file j_mgr_resurrect_playerCPlayerQEAA_NPEADZ_1400124D6_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_mgr_resurrect_playerCPlayerQEAA_NPEADZ_1400124D6
 * @note Address: 0x1400124D6
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?mgr_resurrect_player@CPlayer@@QEAA_NPEAD@Z
 *Address: 0x1400124D6
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


bool __fastcall CPlayer::mgr_resurrect_player(CPlayer *this, char *pwszCharName)
{
  return CPlayer::mgr_resurrect_player(this, pwszCharName);
}



} // namespace Player
} // namespace RFOnline
