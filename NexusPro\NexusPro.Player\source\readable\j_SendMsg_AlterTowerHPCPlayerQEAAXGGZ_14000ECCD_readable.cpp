/**
 * @file j_SendMsg_AlterTowerHPCPlayerQEAAXGGZ_14000ECCD_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_SendMsg_AlterTowerHPCPlayerQEAAXGGZ_14000ECCD
 * @note Address: 0x14000ECCD
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?SendMsg_AlterTowerHP@CPlayer@@QEAAXGG@Z
 *Address: 0x14000ECCD
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::SendMsg_AlterTowerHP(CPlayer *this, unsigned __int16 wItemSerial, unsigned __int16 wLeftHP)
{
  CPlayer::SendMsg_AlterTowerHP(this, wItemSerial, wLeftHP);
}



} // namespace Player
} // namespace RFOnline
