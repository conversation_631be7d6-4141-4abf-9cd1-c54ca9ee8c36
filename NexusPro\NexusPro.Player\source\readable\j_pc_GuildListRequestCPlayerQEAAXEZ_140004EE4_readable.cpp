/**
 * @file j_pc_GuildListRequestCPlayerQEAAXEZ_140004EE4_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_GuildListRequestCPlayerQEAAXEZ_140004EE4
 * @note Address: 0x140004EE4
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_GuildListRequest@CPlayer@@QEAAXE@Z
 *Address: 0x140004EE4
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_GuildListRequest(CPlayer *this, char byPage)
{
  CPlayer::pc_GuildListRequest(this, byPage);
}



} // namespace Player
} // namespace RFOnline
