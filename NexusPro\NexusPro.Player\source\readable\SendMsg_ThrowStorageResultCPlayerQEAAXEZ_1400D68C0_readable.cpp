/**
 * @file SendMsg_ThrowStorageResultCPlayerQEAAXEZ_1400D68C0_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: SendMsg_ThrowStorageResultCPlayerQEAAXEZ_1400D68C0
 * @note Address: 0x1400D68C0
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: ?SendMsg_ThrowStorageResult@CPlayer@@QEAAXE@Z
 *Address: 0x1400D68C0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::SendMsg_ThrowStorageResult(CPlayer *this, char byErrCode)
{
  __int64*v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-78h]@1
  char szMsg; // [sp+34h] [bp-44h]@4
  char pbyType; // [sp+54h] [bp-24h]@4
  char v7; // [sp+55h] [bp-23h]@4
  CPlayer*v8; // [sp+80h] [bp+8h]@1

  v8 = this;
  v2 = &v4;
  void for(signed __int64 i = 28; i > 0; --i);
{
    *(DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  szMsg = byErrCode;
  pbyType = 7;
  v7 = 6;
  CNetProcess::LoadSendMsg(unk_1414F2088, v8->m_ObjID.m_wIndex, &pbyType, &szMsg, 1u);
}




} // namespace Player
} // namespace RFOnline
