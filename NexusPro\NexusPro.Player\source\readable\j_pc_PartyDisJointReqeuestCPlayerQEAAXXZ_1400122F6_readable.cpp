/**
 * @file j_pc_PartyDisJointReqeuestCPlayerQEAAXXZ_1400122F6_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_PartyDisJointReqeuestCPlayerQEAAXXZ_1400122F6
 * @note Address: 0x1400122F6
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_PartyDisJointReqeuest@CPlayer@@QEAAXXZ
 *Address: 0x1400122F6
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_PartyDisJointReqeuest(CPlayer *this)
{
  CPlayer::pc_PartyDisJointReqeuest(this);
}



} // namespace Player
} // namespace RFOnline
