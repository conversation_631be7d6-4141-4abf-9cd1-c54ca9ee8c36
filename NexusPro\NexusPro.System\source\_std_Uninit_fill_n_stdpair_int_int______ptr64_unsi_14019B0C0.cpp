﻿/*
 *Function: _std::_Uninit_fill_n_std::pair_int_int______ptr64_unsigned___int64_std::pair_int_int__std::allocator_std::pair_int_int______::_1_::catch$0
 *Address: 0x14019B0C0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// Additional STL includes for compilation
#include <memory>
#include <utility>



void __fastcall __noreturn std::_Uninit_fill_n_std::pair_int_int______ptr64_unsigned___int64_std::pair_int_int__std::allocator_std::pair_int_int______::_1_::catch_0(__int64 a1, __int64 a2)
{
  __int64 i; // rbp@1

  for(i = a2; *((Q WO RD*)i + 32) != *((QWORD*)i + 64); *((QWORD*)i + 32) += 8i64 )
    std::allocator<std::pair<int,int > >::destroy(
      *(std::allocator<std::pair<int,int>  > **)(i + 88),
      *(std::pair<int,int> **)(i + 32));
  CxxThrowException_0(0i64, 0i64);
}



