/**
 * @file j_pc_PartyLeaveCompulsionReqeuestCPlayerQEAAXKZ_140001DB6_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_PartyLeaveCompulsionReqeuestCPlayerQEAAXKZ_140001DB6
 * @note Address: 0x140001DB6
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_PartyLeaveCompulsionReqeuest@CPlayer@@QEAAXK@Z
 *Address: 0x140001DB6
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_PartyLeaveCompulsionReqeuest(CPlayer *this, unsigned int dwExiterSerial)
{
  CPlayer::pc_PartyLeaveCompulsionReqeuest(this, dwExiterSerial);
}



} // namespace Player
} // namespace RFOnline
