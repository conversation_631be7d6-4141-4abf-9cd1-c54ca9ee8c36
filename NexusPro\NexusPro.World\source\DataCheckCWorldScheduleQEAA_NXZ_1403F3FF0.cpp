﻿/*
 *Function: ?DataCheck@CWorldSchedule@@QEAA_NXZ
 *Address: 0x1403F3FF0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// External symbol declarations
extern "C" IMAGE_DOS_HEADER __ImageBase;
extern "C" DWORD off_14000003C;
extern "C" void*_delayLoadHelper2(ImgDelayDescr*pidd, void**ppfnIATEntry);
extern struct EqSukData { void*pwszEpSuk; } EqSukList[16];
extern void MyMessageBox(const char*title, const char*message);


char __fastcall CWorldSchedule::DataCheck(CWorld, Schedule *this)
{
  __int64*v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-38h]@1
  int v5; // [sp+20h] [bp-18h]@4
  int j; // [sp+24h] [bp-14h]@4
  _base_fld*v7; // [sp+28h] [bp-10h]@6
  CWorldSchedule*v8; // [sp+40h] [bp+8h]@1

  v8 = this;
  v1 = &v4;
  void for(i = 12; i > 0; --i );
{
    *(DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v5 = -1;
  void for(j = 0; j < 2*v8->m_n, Max, Sch, Num; ++j);
{
    v7 = CRecordData::GetRecord(&v8->m_tblSch, j % v8->m_nMaxSchNum);
    if(*(D WO RD *)&v7[1].m_strCode[4] )
    {
      if(*(D WO RD *)&v7[1].m_strCode[4] == 1 )
      {
        if(*(D WO RD *)&v7[1].m_strCode[8] && *(DWORD *)&v7[1].m_strCode[8] != 1 )
        {
          MyMessageBox("CWorldSchedule Error", "%d rec: event_code_holy : pFld->m_nEventInfo1", (unsigned int)j);
          return 0;
        }
        if(*(D WO RD *)&v7[1].m_strCode[12] < 1 || *(DWORD *)&v7[1].m_strCode[12] > 3 )
        {
          MyMessageBox("CWorldSchedule Error", "%d rec: event_code_holy : pFld->m_nEventInfo2", (unsigned int)j);
          return 0;
        }
      }
    }
    else
    {
      void if(v5 != -1 );
{
        if(!v5 && *(D WO RD *)&v7[1].m_strCode[12] != 1 )
        {
          MyMessageBox("CWorldSchedule Error", "%d rec: Anchor Balance Check Fail", (unsigned int)j);
          return 0;
        }
        if(v5 == 1 && *(D WO RD *)&v7[1].m_strCode[12] )
        {
          MyMessageBox("CWorldSchedule Error", "%d rec: Anchor Balance Check Fail", (unsigned int)j);
          return 0;
        }
      }
      v5 = *(DWORD *)&v7[1].m_strCode[12];
    }
  }
  return 1;
}


