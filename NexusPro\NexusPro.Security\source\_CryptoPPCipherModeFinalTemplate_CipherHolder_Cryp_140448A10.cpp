﻿/*
 *Function: _CryptoPP::CipherModeFinalTemplate_CipherHolder_CryptoPP::BlockCipherFinal_1_CryptoPP::Rijndael::Dec__CryptoPP::CBC_Decryption_::_CipherModeFinalTemplate_CipherHolder_CryptoPP::BlockCipherFinal_1_CryptoPP::Rijndael::Dec__CryptoPP::CBC_Decryption__::_1_::dtor$0
 *Address: 0x140448A10
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CryptoPP::CipherModeFinalTemplate_CipherHolder_CryptoPP::BlockCipherFinal_1_CryptoPP::Rijndael::Dec__CryptoPP::CBC_Decryption_::_CipherModeFinalTemplate_CipherHolder_CryptoPP::BlockCipherFinal_1_CryptoPP::Rijndael::Dec__CryptoPP::CBC_Decryption__::_1_::dtor_0(__int64 a1, __int64 a2)
{
  if(*((Q WO RD*)a2 + 64) )
    *((QWORD*)a2 + 40) = *((QWORD*)a2 + 64) + 96i64;
  else
    *((QWORD*)a2 + 40) = 0;
  CryptoPP::ObjectHolder<CryptoPP::BlockCipherFinal<1,CryptoPP::Rijndael::Dec > >::~ObjectHolder<CryptoPP::BlockCipherFinal<1,CryptoPP::Rijndael::Dec > >(*(CryptoPP::ObjectHolder<CryptoPP::BlockCipherFinal<1,CryptoPP::Rijndael::Dec>  > **)(a2 + 40));
}


