/**
 * @file j_SelectDeleteBufCPotionMgrQEAAHPEAVCPlayer_N1Z_14000EEBC_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_SelectDeleteBufCPotionMgrQEAAHPEAVCPlayer_N1Z_14000EEBC
 * @note Address: 0x14000EEBC
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?SelectDeleteBuf@CPotionMgr@@QEAAHPEAVCPlayer@@_N1@Z
 *Address: 0x14000EEBC
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


int __fastcall CPotionMgr::SelectDeleteBuf(CPotionMgr *this, CPlayer *pOne, bool bUse, bool bRemove)
{
  return CPotionMgr::SelectDeleteBuf(this, pOne, bUse, bRemove);
}



} // namespace Player
} // namespace RFOnline
