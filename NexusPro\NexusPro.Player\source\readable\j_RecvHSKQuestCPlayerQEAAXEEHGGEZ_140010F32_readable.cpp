/**
 * @file j_RecvHSKQuestCPlayerQEAAXEEHGGEZ_140010F32_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_RecvHSKQuestCPlayerQEAAXEEHGGEZ_140010F32
 * @note Address: 0x140010F32
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?RecvHSKQuest@CPlayer@@QEAAXEEHGGE@Z
 *Address: 0x140010F32
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::RecvHSKQuest(CPlayer *this, char byHSKQuestCode, char byCristalBattleDBInfo, int nPvpPoint, unsigned __int16 wKillPoint, unsigned __int16 wDieCount, char byHSKTime)
{
  CPlayer::RecvHSKQuest(this, byHSKQuestCode, byCristalBattleDBInfo, nPvpPoint, wKillPoint, wDieCount, byHSKTime);
}



} // namespace Player
} // namespace RFOnline
