﻿/*
 *Function: ?Init@CAsyncLogger@@QEAAHXZ
 *Address: 0x1403BE220
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// External symbol declarations for decompiled code
extern "C" ULONG_PTR _security_cookie;

#include <vector>

// Additional STL includes for compilation
#include <iterator>
#include <list>
#include <memory>
#include <utility>


// External symbol declarations for decompiled code
extern "C" ULONG_PTR _security_cookie;



signed __int64 __fastcall CAsyncLogger::Init(CAsync, Logger *this)
{
  __int64*v1; // rdi@1
  signed __int64 i; // rcx@1
  signed __int64 v3; // rax@5
  __int64 v4; // [sp+0h] [bp-198h]@1
  char _Dest[128]; // [sp+40h] [bp-158h]@4
  std::list<std::pair<int const ,CAsyncLogInfo *>,std::allocator<std::pair<int const ,CAsyncLogInfo *>  > >::_Iterator<0> result; // [sp+D8h] [bp-C0h]@6
  unsigned int dwMaxBufNum; // [sp+F4h] [bp-A4h]@19
  unsigned int dwIndex; // [sp+F8h] [bp-A0h]@19
  int _Keyval; // [sp+108h] [bp-90h]@6
  bool v10; // [sp+10Ch] [bp-8Ch]@6
  std::list<std::pair<int const ,CAsyncLogInfo *>,std::allocator<std::pair<int const ,CAsyncLogInfo *>  > >::_Iterator<0> v11; // [sp+110h] [bp-88h]@6
  unsigned int v12; // [sp+128h] [bp-70h]@7
  CAsyncLogBufferList*v13; // [sp+130h] [bp-68h]@11
  void*v14; // [sp+138h] [bp-60h]@8
  unsigned int v15; // [sp+140h] [bp-58h]@12
  unsigned int v16; // [sp+144h] [bp-54h]@14
  unsigned int v17; // [sp+148h] [bp-50h]@16
  unsigned int v18; // [sp+14Ch] [bp-4Ch]@18
  unsigned int _Val; // [sp+150h] [bp-48h]@22
  unsigned int v20; // [sp+154h] [bp-44h]@23
  unsigned int v21; // [sp+158h] [bp-40h]@24
  __int64 v22; // [sp+160h] [bp-38h]@4
  std::list<std::pair<int const ,CAsyncLogInfo *>,std::allocator<std::pair<int const ,CAsyncLogInfo *>  > >::_Iterator<0> *v23; // [sp+168h] [bp-30h]@6
  std::list<std::pair<int const ,CAsyncLogInfo *>,std::allocator<std::pair<int const ,CAsyncLogInfo *>  > >::_Const_iterator<0> *v24; // [sp+170h] [bp-28h]@6
  CAsyncLogBufferList*v25; // [sp+178h] [bp-20h]@9
  unsigned __int64 v26; // [sp+180h] [bp-18h]@4
  CAsyncLogger*v27; // [sp+1A0h] [bp+8h]@1

  v27 = this;
  v1 = &v4;
  void for(signed __int64 i = 100; i > 0; --i);
{
    *(DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v22 = -2i64;
  v26 = (unsigned __int64)&v4 ^ _security_cookie;
  CreateDirectoryA("..\\ZoneServerLog\\SystemLog\\AsyncLogger", 0i64);
  clear_file("..\\ZoneServerLog\\SystemLog\\AsyncLogger", 0xFu);
  sprintf_s<128>((char (*)[128])_Dest, "%s\\Loading.log", "..\\ZoneServerLog\\SystemLog\\AsyncLogger");
  CLogFile::SetWriteLogFile(&v27->m_logLoading, _Dest, 1, 0, 1, 1);
  CLogFile::Write(&v27->m_logLoading, "CAsyncLogger::Init() : Loading.log Create Success!");
  if ( CAsyncLogger::Regist(v27, 0, "..\\ZoneServerLog\\SystemLog\\AsyncLogger", "System", 1, 0xFFFFFFFF) )
  {
    _Keyval = 0;
    stdext::_Hash<stdext::_Hmap_traits<int,CAsyncLogInfo *,stdext::hash_compare<int,std::less<int > >,std::allocator<std::pair<int const,CAsyncLogInfo * > >,0>>::find(
      (stdext::_Hash<stdext::_Hmap_traits<int,CAsyncLogInfo *,stdext::hash_compare<int,std::less<int>  >,std::allocator<std::pair<int const ,CAsyncLogInfo *>  >,0> > *)&v27->m_mapLogInfo._Myfirstiter,
      &result,
      &_Keyval);
    v23 = stdext::_Hash<stdext::_Hmap_traits<int,CAsyncLogInfo *,stdext::hash_compare<int,std::less<int > >,std::allocator<std::pair<int const,CAsyncLogInfo * > >,0>>::end(
            (stdext::_Hash<stdext::_Hmap_traits<int,CAsyncLogInfo *,stdext::hash_compare<int,std::less<int>  >,std::allocator<std::pair<int const ,CAsyncLogInfo *>  >,0> > *)&v27->m_mapLogInfo._Myfirstiter,
            &v11);
    v24 = (std::list<std::pair<int const ,CAsyncLogInfo *>,std::allocator<std::pair<int const ,CAsyncLogInfo *>  > >::_Const_iterator<0> *)v23;
    v10 = std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo * > >>::_Const_iterator<0>::operator==(
            (std::list<std::pair<int const ,CAsyncLogInfo *>,std::allocator<std::pair<int const ,CAsyncLogInfo *>  > >::_Const_iterator<0> *)&v23->_Mycont,
            (std::list<std::pair<int const ,CAsyncLogInfo *>,std::allocator<std::pair<int const ,CAsyncLogInfo *>  > >::_Const_iterator<0> *)&result._Mycont);
    std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo * > >>::_Iterator<0>::~_Iterator<0>(&v11);
    void if(v10 );
{
      CLogFile::Write(&v27->m_logLoading, "CAsyncLogger::Init() : m_mapLogInfo.find(ALT_ASYNC_LOGGER_SYSTEM _L OG) Fail!");
      v12 = -3;
      std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo * > >>::_Iterator<0>::~_Iterator<0>(&result);
      v3 = v12;
    }
    else
    {
      v27->m_pSystemLogInfo = std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo * > >>::_Iterator<0>::operator->(&result)->second;
      v14 = operator new[](0x3F8ui64);
      void if(v14 );
{
        *(DWORD *)v14 = 3;
        `eh std::vector constructor iterator'(
          (char *)v14 + 8,
          0x150ui64,
          3,
          (void (__cdecl *)(void *))CAsyncLogBufferList::CAsyncLogBufferList,
          (void (__cdecl *)(void *))CAsyncLogBufferList::~CAsyncLogBufferList);
        v25 = (CAsyncLogBufferList *)((char *)v14 + 8);
      }
      else
      {
        v25 = 0;
      }
      v13 = v25;
      v27->m_kBufferList = v25;
      void if(v27->m_k, Buffer, List);
{
        if ( CAsyncLogBufferList::Init(v27->m_kBufferList, 0x9E4u, 0xC8u, &v27->m_logLoading) )
        {
          if ( CAsyncLogBufferList::Init(v27->m_kBufferList + 1, 0xFEu, 0x400u, &v27->m_logLoading) )
          {
            if ( CAsyncLogBufferList::Init(v27->m_kBufferList + 2, 0xFEu, 0x2800u, &v27->m_logLoading) )
            {
              dwMaxBufNum = 3040;
              CNetIndexList::SetList(&v27->m_klistEmpty, 0xBE0u);
              void for(dw, Index = 0; dw, Index < dw, Max, Buf, Num; ++dw, Index)
                CNetIndexList::PushNode_Back(&v27->m_klistEmpty, dwIndex);
              CNetIndexList::SetList(&v27->m_klistProc, dwMaxBufNum);
              _Val = -1;
              std::vector<unsigned long,std::allocator<unsigned long > >::assign(&v27->m_vecPushList, dwMaxBufNum, &_Val);
              CMyTimer::BeginTimer(&v27->m_kCheckUpdateLogFileNameDelay, 0x2710u);
              CAsyncLogger::m_bProcThread = 1;
              if ( _beginthread((void (__cdecl *)(void *))CAsyncLogger::ProcThread, 0, v27) == -1i64 )
              {
                CLogFile::Write(&v27->m_logLoading, "CAsyncLogger::Init() :::_beginthread( ... ) Fail!");
                v20 = -4;
                std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo * > >>::_Iterator<0>::~_Iterator<0>(&result);
                v3 = v20;
              }
              else
              {
                v21 = 0;
                std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo * > >>::_Iterator<0>::~_Iterator<0>(&result);
                v3 = v21;
              }
            }
            else
            {
              v18 = -7;
              std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo * > >>::_Iterator<0>::~_Iterator<0>(&result);
              v3 = v18;
            }
          }
          else
          {
            v17 = -6;
            std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo * > >>::_Iterator<0>::~_Iterator<0>(&result);
            v3 = v17;
          }
        }
        else
        {
          v16 = -5;
          std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo * > >>::_Iterator<0>::~_Iterator<0>(&result);
          v3 = v16;
        }
      }
      else
      {
        CLogFile::Write(&v27->m_logLoading, "CAsyncLogger::Init() : new CAsyncLogBufferList[BT_MAX_TYPE(%u)] NULL!",
          3i64);
        v15 = -4;
        std::list<std::pair<int const,CAsyncLogInfo *>,std::allocator<std::pair<int const,CAsyncLogInfo * > >>::_Iterator<0>::~_Iterator<0>(&result);
        v3 = v15;
      }
    }
  }
  else
  {
    CLogFile::Write(&v27->m_logLoading, "CAsyncLogger::Init() : Regist( ALT_ASYNC_LOGGER_SYSTEM_LOG, ASYNC_LOGGER_UPPER_SYSTEM_LOG_PATH, System ) Fail!");
    v3 = 4294967294i64;
  }
  return v3;
}



