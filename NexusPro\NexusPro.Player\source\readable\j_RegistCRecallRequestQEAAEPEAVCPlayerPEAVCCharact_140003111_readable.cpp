/**
 * @file j_RegistCRecallRequestQEAAEPEAVCPlayerPEAVCCharact_140003111_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_RegistCRecallRequestQEAAEPEAVCPlayerPEAVCCharact_140003111
 * @note Address: 0x140003111
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?Regist@CRecallRequest@@QEAAEPEAVCPlayer@@PEAVCCharacter@@_N22@Z
 *Address: 0x140003111
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


char __fastcall CRecallRequest::Regist(CRecallRequest *this, CPlayer *pkObj, CCharacter *pkDest, bool bRecallParty, bool bStone, bool bBattleModeUse)
{
  return CRecallRequest::Regist(this, pkObj, pkDest, bRecallParty, bStone, bBattleModeUse);
}



} // namespace Player
} // namespace RFOnline
