/**
 * @file j_pc_RequestWatchingWithNPCCPlayerQEAAXPEAVCItemSt_1400079E1_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_RequestWatchingWithNPCCPlayerQEAAXPEAVCItemSt_1400079E1
 * @note Address: 0x1400079E1
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_RequestWatchingWithNPC@CPlayer@@QEAAXPEAVCItemStore@@@Z
 *Address: 0x1400079E1
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_RequestWatchingWithNPC(CPlayer *this, CItemStore *pStore)
{
  CPlayer::pc_RequestWatchingWithNPC(this, pStore);
}



} // namespace Player
} // namespace RFOnline
