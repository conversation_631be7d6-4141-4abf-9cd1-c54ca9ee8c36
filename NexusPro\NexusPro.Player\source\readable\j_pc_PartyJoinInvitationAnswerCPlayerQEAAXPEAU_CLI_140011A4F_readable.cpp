/**
 * @file j_pc_PartyJoinInvitationAnswerCPlayerQEAAXPEAU_CLI_140011A4F_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_PartyJoinInvitationAnswerCPlayerQEAAXPEAU_CLI_140011A4F
 * @note Address: 0x140011A4F
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_PartyJoinInvitationAnswer@CPlayer@@QEAAXPEAU_CLID@@@Z
 *Address: 0x140011A4F
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_PartyJoinInvitationAnswer(CPlayer *this, _CLID *pidBoss)
{
  CPlayer::pc_PartyJoinInvitationAnswer(this, pidBoss);
}



} // namespace Player
} // namespace RFOnline
