/**
 * @file j_SearchCharacterPathDfAIMgrSA_NPEAVCMonsterAIPEAV_14000DDFA_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_SearchCharacterPathDfAIMgrSA_NPEAVCMonsterAIPEAV_14000DDFA
 * @note Address: 0x14000DDFA
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?SearchCharacterPath@DfAIMgr@@SA_NPEAVCMonsterAI@@PEAVCMonster@@PEAVCCharacter@@@Z
 *Address: 0x14000DDFA
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


bool __fastcall DfAIMgr::SearchCharacterPath(CMonsterAI *pAI, CMonster *pMon, CCharacter *pTarget)
{
  return DfAIMgr::SearchCharacterPath(pAI, pMon, pTarget);
}



} // namespace Player
} // namespace RFOnline
