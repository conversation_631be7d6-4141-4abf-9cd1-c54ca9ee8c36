/**
 * @file j_pc_ExchangeGoldForPvPCPlayerQEAAXKZ_14000AE9D_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_ExchangeGoldForPvPCPlayerQEAAXKZ_14000AE9D
 * @note Address: 0x14000AE9D
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_ExchangeGoldForPvP@CPlayer@@QEAAXK@Z
 *Address: 0x14000AE9D
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_ExchangeGoldForPvP(CPlayer *this, unsigned int dwGold)
{
  CPlayer::pc_ExchangeGoldForPvP(this, dwGold);
}



} // namespace Player
} // namespace RFOnline
