/**
 * @file j_pc_MoveToOwnStoneMapRequestCPlayerQEAAXXZ_14000BC76_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_MoveToOwnStoneMapRequestCPlayerQEAAXXZ_14000BC76
 * @note Address: 0x14000BC76
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_MoveToOwnStoneMapRequest@CPlayer@@QEAAXXZ
 *Address: 0x14000BC76
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_MoveToOwnStoneMapRequest(CPlayer *this)
{
  CPlayer::pc_MoveToOwnStoneMapRequest(this);
}



} // namespace Player
} // namespace RFOnline
