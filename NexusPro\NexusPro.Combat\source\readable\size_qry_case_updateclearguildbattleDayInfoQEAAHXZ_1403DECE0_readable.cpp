/**
 * @file size_qry_case_updateclearguildbattleDayInfoQEAAHXZ_1403DECE0_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: size_qry_case_updateclearguildbattleDayInfoQEAAHXZ_1403DECE0
 * @note Address: 0x1403DECE0
 */

#include <cstdint>

namespace RFOnline {
namespace Combat {

﻿/*
 *Function: ?size@_qry_case_updateclearguildbattleDayInfo@@QEAAHXZ
 *Address: 0x1403DECE0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


signed __int64 __fastcall _qry_case_updateclearguildbattleDayInfo::size(_qry_case_updateclearguildbattle, Day, Info *this)
{
  return 16i64;
}



} // namespace Combat
} // namespace RFOnline
