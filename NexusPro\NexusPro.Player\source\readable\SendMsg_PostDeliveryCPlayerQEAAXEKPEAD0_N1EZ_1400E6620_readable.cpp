/**
 * @file SendMsg_PostDeliveryCPlayerQEAAXEKPEAD0_N1EZ_1400E6620_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: SendMsg_PostDeliveryCPlayerQEAAXEKPEAD0_N1EZ_1400E6620
 * @note Address: 0x1400E6620
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: ?SendMsg_PostDelivery@CPlayer@@QEAAXEKPEAD0_N1E@Z
 *Address: 0x1400E6620
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// External symbol declarations for decompiled code
extern "C" ULONG_PTR _security_cookie;


// External symbol declarations for decompiled code
extern "C" ULONG_PTR _security_cookie;



void __fastcall CPlayer::SendMsg_PostDelivery(CPlayer *this, char byIndex, unsigned int dwPostSerial, char *wszSendName, char *wszTitle, bool bItem, bool bGold, char byState)
{
  __int64*v8; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v10; // [sp+0h] [bp-B8h]@1
  char szMsg; // [sp+38h] [bp-80h]@4
  unsigned int v12; // [sp+39h] [bp-7Fh]@4
  char Dst; // [sp+3Dh] [bp-7Bh]@4
  char v14; // [sp+4Eh] [bp-6Ah]@4
  bool v15; // [sp+63h] [bp-55h]@4
  bool v16; // [sp+64h] [bp-54h]@4
  char v17; // [sp+65h] [bp-53h]@4
  char pbyType; // [sp+84h] [bp-34h]@4
  char v19; // [sp+85h] [bp-33h]@4
  unsigned __int64 v20; // [sp+A0h] [bp-18h]@4
  CPlayer*v21; // [sp+C0h] [bp+8h]@1

  v21 = this;
  v8 = &v10;
  void for(signed __int64 i = 44; i > 0; --i);
{
    *(DWORD *)v8 = -858993460;
    v8 = (__int64 *)((char *)v8 + 4);
  }
  v20 = (unsigned __int64)&v10 ^ _security_cookie;
  szMsg = byIndex;
  v12 = dwPostSerial;
  strcpy_s(&Dst, 0x11ui64, wszSendName);
  strcpy_s(&v14, 0x15ui64, wszTitle);
  v15 = bItem;
  v16 = bGold;
  v17 = byState;
  pbyType = 58;
  v19 = 4;
  CNetProcess::LoadSendMsg(unk_1414F2088, v21->m_ObjID.m_wIndex, &pbyType, &szMsg, 0x2Eu);
}




} // namespace Player
} // namespace RFOnline
