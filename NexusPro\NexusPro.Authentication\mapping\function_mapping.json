{
  "mapping_version": "1.0",
  "description": "Function mapping between original decompiled code and human-readable versions",
  "last_updated": "2025-07-14",
  "mappings": {
    "LoginCBillingManagerQEAAXPEAVCUserDBZ_140079030": {
      "original_function": "?Login@CBillingManager@@QEAAXPEAVCUserDB@@@Z",
      "original_address": "0x140079030",
      "original_file": "Decompiled Source Code - IDA Pro/authentication/LoginCBillingManagerQEAAXPEAVCUserDBZ_140079030.c",
      "converted_file": "NexusPro/NexusPro.Authentication/source/LoginCBillingManagerQEAAXPEAVCUserDBZ_140079030.cpp",
      "readable_class": "RFOnline::Authentication::BillingManager",
      "readable_function": "Login",
      "readable_file": "NexusPro/NexusPro.Authentication/source/readable/BillingManager.cpp",
      "readable_header": "NexusPro/NexusPro.Authentication/headers/readable/BillingManager.h",
      "parameters": {
        "original": [
          {
            "name": "this",
            "type": "CBillingManager*",
            "description": "Pointer to billing manager instance"
          },
          {
            "name": "pUserDB",
            "type": "CUserDB*",
            "description": "Pointer to user database object"
          }
        ],
        "readable": [
          {
            "name": "userDatabase",
            "type": "CUserDB*",
            "description": "Pointer to user database object containing login credentials"
          }
        ]
      },
      "variables": {
        "v2": {
          "readable_name": "bufferPointer",
          "type": "__int64*",
          "description": "Pointer to memory buffer for iteration"
        },
        "v4": {
          "readable_name": "memoryBuffer",
          "type": "__int64[8]",
          "description": "Stack-allocated memory buffer for billing data"
        },
        "v5": {
          "readable_name": "this (implicit)",
          "type": "BillingManager*",
          "description": "Current billing manager instance"
        },
        "i": {
          "readable_name": "loopCounter",
          "type": "signed __int64",
          "description": "Loop counter for memory initialization"
        }
      },
      "constants": {
        "-858993460": {
          "readable_value": "0xCCCCCCCC",
          "description": "Debug fill pattern for uninitialized memory"
        }
      },
      "transformation_notes": [
        "Replaced generic variable names with descriptive names",
        "Added comprehensive documentation explaining purpose",
        "Added safety checks for null pointer access",
        "Preserved exact original logic and memory layout",
        "Used modern C++ casting instead of C-style casts",
        "Added namespace organization for better code structure"
      ]
    }
  }
}
��{ } 
 
 