/**
 * @file j_pc_UnitFrameBuyRequestCPlayerQEAAXEHZ_140011932_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_UnitFrameBuyRequestCPlayerQEAAXEHZ_140011932
 * @note Address: 0x140011932
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_UnitFrameBuyRequest@CPlayer@@QEAAXEH@Z
 *Address: 0x140011932
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_UnitFrameBuyRequest(CPlayer *this, char byFrameCode, int bUseNPCLinkIntem)
{
  CPlayer::pc_UnitFrameBuyRequest(this, byFrameCode, bUseNPCLinkIntem);
}



} // namespace Player
} // namespace RFOnline
