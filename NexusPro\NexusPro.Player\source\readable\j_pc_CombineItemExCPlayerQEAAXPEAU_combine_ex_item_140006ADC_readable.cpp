/**
 * @file j_pc_CombineItemExCPlayerQEAAXPEAU_combine_ex_item_140006ADC_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_CombineItemExCPlayerQEAAXPEAU_combine_ex_item_140006ADC
 * @note Address: 0x140006ADC
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_CombineItemEx@CPlayer@@QEAAXPEAU_combine_ex_item_request_clzo@@@Z
 *Address: 0x140006ADC
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_CombineItemEx(CPlayer *this, _combine_ex_item_request_clzo *pRecv)
{
  CPlayer::pc_CombineItemEx(this, pRecv);
}



} // namespace Player
} // namespace RFOnline
