/**
 * @file j_pc_WPActiveAttack_ForceCPlayerQEAA_NPEAU_be_dama_14000B3A7_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_WPActiveAttack_ForceCPlayerQEAA_NPEAU_be_dama_14000B3A7
 * @note Address: 0x14000B3A7
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_WPActiveAttack_Force@CPlayer@@QEAA_NPEAU_be_damaged_char@@PEAHPEAU_force_fld@@@Z
 *Address: 0x14000B3A7
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


bool __fastcall CPlayer::pc_WPActiveAttack_Force(CPlayer *this, _be_damaged_char *pDamList, int *nDamagedObj<PERSON>um, _force_fld *pForceFld)
{
  return CPlayer::pc_WPActiveAttack_Force(this, pDam<PERSON>ist, nDamagedObjNum, pForceFld);
}



} // namespace Player
} // namespace RFOnline
