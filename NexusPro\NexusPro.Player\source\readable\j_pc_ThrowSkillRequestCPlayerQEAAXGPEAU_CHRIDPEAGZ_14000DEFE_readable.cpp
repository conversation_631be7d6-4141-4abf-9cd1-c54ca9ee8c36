/**
 * @file j_pc_ThrowSkillRequestCPlayerQEAAXGPEAU_CHRIDPEAGZ_14000DEFE_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_ThrowSkillRequestCPlayerQEAAXGPEAU_CHRIDPEAGZ_14000DEFE
 * @note Address: 0x14000DEFE
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_ThrowSkillRequest@CPlayer@@QEAAXGPEAU_CHRID@@PEAG@Z
 *Address: 0x14000DEFE
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_ThrowSkillRequest(CPlayer *this, unsigned __int16 wBulletSerial, _CHRID *pidDst, unsigned __int16 *pConsumeSerial)
{
  CPlayer::pc_ThrowSkillRequest(this, wBulletSerial, pidDst, pConsumeSerial);
}



} // namespace Player
} // namespace RFOnline
