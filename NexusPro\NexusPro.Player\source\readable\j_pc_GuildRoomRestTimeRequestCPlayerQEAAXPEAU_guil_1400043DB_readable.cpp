/**
 * @file j_pc_GuildRoomRestTimeRequestCPlayerQEAAXPEAU_guil_1400043DB_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_GuildRoomRestTimeRequestCPlayerQEAAXPEAU_guil_1400043DB
 * @note Address: 0x1400043DB
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_GuildRoomRestTimeRequest@CPlayer@@QEAAXPEAU_guildroom_resttime_request_clzo@@@Z
 *Address: 0x1400043DB
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_GuildRoomRestTimeRequest(CPlayer *this, _guildroom_resttime_request_clzo *pProtocol)
{
  CPlayer::pc_GuildRoomRestTimeRequest(this, pProtocol);
}



} // namespace Player
} // namespace RFOnline
