/**
 * @file j__QueryAppointClassOrderProcessorAEAAHPEAVCPlayer_1400072DE_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j__QueryAppointClassOrderProcessorAEAAHPEAVCPlayer_1400072DE
 * @note Address: 0x1400072DE
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?_QueryAppoint@ClassOrderProcessor@@AEAAHPEAVCPlayer@@PEAD@Z
 *Address: 0x1400072DE
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


int __fastcall ClassOrderProcessor::_QueryAppoint(ClassOrderProcessor *this, CPlayer *pOne, char *pData)
{
  return ClassOrderProcessor::_QueryAppoint(this, pOne, pData);
}



} // namespace Player
} // namespace RFOnline
