﻿/*
 *Function: _dynamic_initializer_for__UIDGenerator::_cs__
 *Address: 0x1406E5D90
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


__int64 dynamic_initializer_for__UIDGenerator::_cs__()
{
  __int64*v0; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1

  v0 = &v3;
  void for(i = 8; i > 0; --i );
{
    *(DWORD *)v0 = -858993460;
    v0 = (__int64 *)((char *)v0 + 4);
  }
  CNetCriticalSection::CNetCriticalSection(&UI DGenerator::_cs);
  return atexit(dynamic_atexit_destructor_for__ UI DGenerator::_cs__);
}


