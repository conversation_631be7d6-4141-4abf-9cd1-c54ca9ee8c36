/**
 * @file j_SetNextGenAttTimeCCharacterQEAAXKZ_1400138B8_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_SetNextGenAttTimeCCharacterQEAAXKZ_1400138B8
 * @note Address: 0x1400138B8
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?SetNextGenAttTime@CCharacter@@QEAAXK@Z
 *Address: 0x1400138B8
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CCharacter::SetNextGenAttTime(CCharacter *this, unsigned int dwNextTime)
{
  CCharacter::SetNextGenAttTime(this, dwNextTime);
}



} // namespace Player
} // namespace RFOnline
