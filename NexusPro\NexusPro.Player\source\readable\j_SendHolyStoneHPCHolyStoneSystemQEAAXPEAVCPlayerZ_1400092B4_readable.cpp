/**
 * @file j_SendHolyStoneHPCHolyStoneSystemQEAAXPEAVCPlayerZ_1400092B4_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_SendHolyStoneHPCHolyStoneSystemQEAAXPEAVCPlayerZ_1400092B4
 * @note Address: 0x1400092B4
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?SendHolyStoneHP@CHolyStoneSystem@@QEAAXPEAVCPlayer@@@Z
 *Address: 0x1400092B4
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CHolyStoneSystem::SendHolyStoneHP(CHolyStoneSystem *this, CPlayer *pkPlayer)
{
  CHolyStoneSystem::SendHolyStoneHP(this, pkPlayer);
}



} // namespace Player
} // namespace RFOnline
