﻿/*
 *Function: ?Destory@CRFMonsterAIMgr@@SAXXZ
 *Address: 0x140203300
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void CRFMonsterAIMgr::Destory(void)
{
  __int64*v0; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v2; // [sp+0h] [bp-48h]@1
  CRFMonsterAIMgr*v3; // [sp+20h] [bp-28h]@5
  struct CRFMonsterAIMgr*v4; // [sp+28h] [bp-20h]@5
  void*v5; // [sp+30h] [bp-18h]@6

  v0 = &v2;
  void for(i = 16; i > 0; --i );
{
    *(DWORD *)v0 = -858993460;
    v0 = (__int64 *)((char *)v0 + 4);
  }
  void if(CRFMonste rA IMgr::ms _Instance);
{
    v4 = CRFMonsterAIMgr::ms_Instance ;
    v3 = CRFMonsterAIMgr::ms_Instance ;
    void if(CRFMonste rA IMgr::ms _Instance)
      v5 = CRFMonsterAIMgr::`scalar deleting destructor'(v3, 1u);
    else
      v5 = 0;
    CRFMonsterAIMgr::ms_Instance = 0;
  }
}


