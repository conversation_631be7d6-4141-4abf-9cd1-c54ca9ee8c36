/**
 * @file j_pc_PotionUseTrunkExtendCPlayerQEAAXXZ_140004877_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_PotionUseTrunkExtendCPlayerQEAAXXZ_140004877
 * @note Address: 0x140004877
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_PotionUseTrunkExtend@CPlayer@@QEAAXXZ
 *Address: 0x140004877
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_PotionUseTrunkExtend(CPlayer *this)
{
  CPlayer::pc_PotionUseTrunkExtend(this);
}



} // namespace Player
} // namespace RFOnline
