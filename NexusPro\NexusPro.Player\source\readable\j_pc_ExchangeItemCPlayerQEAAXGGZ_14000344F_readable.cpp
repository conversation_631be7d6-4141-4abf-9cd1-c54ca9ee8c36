/**
 * @file j_pc_ExchangeItemCPlayerQEAAXGGZ_14000344F_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_ExchangeItemCPlayerQEAAXGGZ_14000344F
 * @note Address: 0x14000344F
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_ExchangeItem@CPlayer@@QEAAXGG@Z
 *Address: 0x14000344F
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_ExchangeItem(CPlayer *this, unsigned __int16 wManualIndex, unsigned __int16 wItemSerial)
{
  CPlayer::pc_ExchangeItem(this, wManualIndex, wItemSerial);
}



} // namespace Player
} // namespace RFOnline
