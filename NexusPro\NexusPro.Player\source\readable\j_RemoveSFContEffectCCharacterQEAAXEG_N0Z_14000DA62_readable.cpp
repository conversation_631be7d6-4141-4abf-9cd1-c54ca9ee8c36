/**
 * @file j_RemoveSFContEffectCCharacterQEAAXEG_N0Z_14000DA62_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_RemoveSFContEffectCCharacterQEAAXEG_N0Z_14000DA62
 * @note Address: 0x14000DA62
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?RemoveSFContEffect@CCharacter@@QEAAXEG_N0@Z
 *Address: 0x14000DA62
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CCharacter::RemoveSFContEffect(CCharacter *this, char byContCode, unsigned __int16 wListIndex, bool bInit, bool bAura)
{
  CCharacter::RemoveSFContEffect(this, byContCode, wListIndex, bInit, bAura);
}



} // namespace Player
} // namespace RFOnline
