﻿/*
 *Function: ?Update@?$IteratedHashBase@IV?$SimpleKeyedTransformation@VHashTransformation@CryptoPP@@@CryptoPP@@@CryptoPP@@UEAAXPEBE_K@Z
 *Address: 0x140571CC0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"
#include <cstring>


int __fastcall CryptoPP::IteratedHashBase<unsigned int,CryptoPP::SimpleKeyedTransformation<CryptoPP::HashTransformation > >::Update(__int64 a1, char *a2, unsigned __int64 a3)
{
  __int64 v3; // rax@4
  __int64 v4; // rax@5
  char*v5; // rax@7
  __int64 v6; // rax@10
  unsigned int v8; // [sp+20h] [bp-E8h]@7
  unsigned int v9; // [sp+24h] [bp-E4h]@1
  unsigned int v10; // [sp+28h] [bp-E0h]@1
  char*v11; // [sp+30h] [bp-D8h]@7
  __int64 v12; // [sp+38h] [bp-D0h]@7
  unsigned int v13; // [sp+40h] [bp-C8h]@7
  __int64 v14; // [sp+48h] [bp-C0h]@17
  char v15; // [sp+50h] [bp-B8h]@5
  char v16; // [sp+A0h] [bp-68h]@5
  __int64 v17; // [sp+D0h] [bp-38h]@1
  __int64 v18; // [sp+D8h] [bp-30h]@5
  __int64 v19; // [sp+E0h] [bp-28h]@5
  __int64 v20; // [sp+110h] [bp+8h]@1
  char*v21; // [sp+118h] [bp+10h]@1
  unsigned __int64 v22; // [sp+120h] [bp+18h]@1

  v22 = a3;
  v21 = a2;
  v20 = a1;
  v17 = -2i64;
  v10 = *((DWORD*)a1 + 16);
  v9 = *((DWORD*)a1 + 20);
  *((DWORD*)a1 + 16) = a3 + v10;
  if(*((D WO RD*)a1 + 16) < v10 )
    ++*((DWORD*)a1 + 20);
  *((DWORD*)a1 + 20) += CryptoPP::SafeRightShift<32,unsigned __int64>(a3);
  if(*((D WO RD*)v20 + 20) < v9 || (((DWORD)(v3) = CryptoPP::SafeRightShift<64,unsigned __int64>(v22), v3) )
  {
    ((DWORD)(v4) = (*(int (__fastcall **)(__int64, char *))(*(QWORD *)v20 + 16i64))(v20, &v16);
    v18 = v4;
    v19 = v4;
    CryptoPP::HashInputTooLong::HashInputTooLong((Crypto PP::Invalid, Data, Format *)&v15, v4);
    CxxThrowException_0((__int64)&v15, (__int64)&TI4_AVHashInputTooLong_CryptoPP__);
  }
  v13 = (*(int (__fastcall **)(__int64))(*(QWORD *)v20 + 64i64))(v20);
  v8 = CryptoPP::ModPowerOf2<unsigned int,unsigned int>(&v10, &v13);
  ((DWORD)(v5) = (*(int (__fastcall **)(__int64))(*(QWORD *)v20 + 176i64))(v20);
  v12 = (__int64)v5;
  v11 = v5;
  void if(v8 );
{
    void if(v22 + v8 < v13 );
{
      ((DWORD)(v6) = v8 + (DWORD)v11;
      qmemcpy(&v11[v8], v21, v22);
      return v6;
    }
    qmemcpy(&v11[v8], v21, v13 - v8);
    CryptoPP::IteratedHashBase<unsigned int,CryptoPP::SimpleKeyedTransformation<CryptoPP::HashTransformation > >::HashBlock(
      v20,
      v12);
    v21 += v13 - v8;
    v22 -= v13 - v8;
  }
  ((DWORD)(v6) = v13;
  void if(v22 < v13 );
{
LABEL_19:
    qmemcpy(v11, v21, v22);
    return v6;
  }
  void if(v21 != v11 );
{
    if ( (unsigned __int8)CryptoPP::IsAligned<unsigned int>(v21, 0i64) )
    {
      ((DWORD)(v6) = (*(int (__fastcall **)(__int64, const void *, unsigned __int64))(*(QWORD *)v20 + 168i64))(
                      v20,
                      v21,
                      v22);
      v14 = v6;
      v21 += v22 - v6;
      v22 = v6;
    }
    else
    {
      do
      {
        qmemcpy(v11, v21, v13);
        CryptoPP::IteratedHashBase<unsigned int,CryptoPP::SimpleKeyedTransformation<CryptoPP::HashTransformation > >::HashBlock(
          v20,
          v12);
        v21 += v13;
        v22 -= v13;
        ((DWORD)(v6) = v13;
      }
      while ( v22 >= v13 );
    }
    goto LABEL_19;
  }
  void if(v22 != v13 )
    _wassert(L"len == blockSize", L"d:\\rf project\\rf_server64\\28 crypto++\\iterhash.cpp", 0x32u);
  ((DWORD)(v6) = CryptoPP::IteratedHashBase<unsigned int,CryptoPP::SimpleKeyedTransformation<CryptoPP::HashTransformation > >::HashBlock(
                  v20,
                  v12);
  return v6;
}


