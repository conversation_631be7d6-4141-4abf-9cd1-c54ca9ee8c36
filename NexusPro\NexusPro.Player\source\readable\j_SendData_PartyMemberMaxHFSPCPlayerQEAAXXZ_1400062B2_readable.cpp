/**
 * @file j_SendData_PartyMemberMaxHFSPCPlayerQEAAXXZ_1400062B2_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_SendData_PartyMemberMaxHFSPCPlayerQEAAXXZ_1400062B2
 * @note Address: 0x1400062B2
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?SendData_PartyMemberMaxHFSP@CPlayer@@QEAAXXZ
 *Address: 0x1400062B2
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::SendData_PartyMemberMaxHFSP(CPlayer *this)
{
  CPlayer::SendData_PartyMemberMaxHFSP(this);
}



} // namespace Player
} // namespace RFOnline
