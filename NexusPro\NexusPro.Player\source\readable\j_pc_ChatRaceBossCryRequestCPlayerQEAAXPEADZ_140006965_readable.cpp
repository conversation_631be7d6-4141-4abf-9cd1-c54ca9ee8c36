/**
 * @file j_pc_ChatRaceBossCryRequestCPlayerQEAAXPEADZ_140006965_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_ChatRaceBossCryRequestCPlayerQEAAXPEADZ_140006965
 * @note Address: 0x140006965
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_ChatRaceBossCryRequest@CPlayer@@QEAAXPEAD@Z
 *Address: 0x140006965
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_ChatRaceBossCryRequest(CPlayer *this, char *pwszChatData)
{
  CPlayer::pc_ChatRaceBossCryRequest(this, pwszChatData);
}



} // namespace Player
} // namespace RFOnline
