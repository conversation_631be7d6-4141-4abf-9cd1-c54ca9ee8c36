﻿/*
 *Function: _std::vector_CMoveMapLimitRightInfo_std::allocator_CMoveMapLimitRightInfo___::erase_::_1_::dtor$2
 *Address: 0x1403B0960
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"
#include <vector>

// Additional STL includes for compilation
#include <iterator>
#include <memory>



void __fastcall std::vector_CMoveMapLimitRightInfo_std::allocator_CMoveMapLimitRightInfo___::erase_::_1_::dtor_2(__int64 a1, __int64 a2)
{
  if(*((D WO RD*)a2 + 40) &1 )
  {
    *((DWORD*)a2 + 40) &= 0xFFFFFFFE;
    std::_Vector_iterator<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo > >::~_Vector_iterator<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo > >(*(std::_Vector_iterator<CMoveMapLimitRightInfo,std::allocator<CMoveMapLimitRightInfo>  > **)(a2 + 88));
  }
}



