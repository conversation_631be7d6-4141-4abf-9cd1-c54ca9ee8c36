﻿/*
 *Function: _dynamic_initializer_for__ATL::CTrace::s_trace__
 *Address: 0x1406E6890
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


__int64 dynamic_initializer_for__ATL::CTrace::s_trace__()
{
  ATL::CTrace::CTrace(&ATL::CTrace::s_trace, CrtDbgReport_0);
  return atexit(dynamic_atexit_destructor_for_ _A TL::CTrace::s_trace__);
}

