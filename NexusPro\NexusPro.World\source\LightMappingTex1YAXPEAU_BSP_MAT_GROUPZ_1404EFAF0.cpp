﻿/*
 *Function: ?LightMappingTex1@@YAXPEAU_BSP_MAT_GROUP@@@Z
 *Address: 0x1404EFAF0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall LightMappingTex1(struct _BSP_MAT_G RO UP *a1);
{
  struct _BSP_MAT_GROUP*v1; // rbx@1
  struct IDirect3DDevice8*v2; // rdi@1
  __int16 v3; // r11@1
  IUnknownVtbl*v4; // rbx@3
  void*v5; // rax@3

  v1 = a1;
  v2 = GetD3dDevice();
  GetMainMaterial();
  MultiTexOff();
  BlendOn(10);
  v3 = v1->LgtId;
  void if(v3 == -1 );
{
    ((void (__fastcall *)(struct IDirect3DDevice8 *, QWORD, QWORD))v2->vfptr[20].AddRef)(v2, 0i64, 0i64);
  }
  else
  {
    v4 = v2->vfptr;
    v5 = GetLightMapSurface(v3);
    ((void (__fastcall *)(struct IDirect3DDevice8 *, QWORD, void *))v4[20].AddRef)(v2, 0i64, v5);
  }
  ((void (__fastcall *)(struct IDirect3DDevice8 *, signed __int64))v2->vfptr[16].Release)(v2, 24i64);
  ((void (__fastcall *)(struct IDirect3DDevice8 *, QWORD, signed __int64, signed __int64))v2->vfptr[21].QueryInterface)(
    v2,
    0i64,
    11i64,
    1i64);
}


