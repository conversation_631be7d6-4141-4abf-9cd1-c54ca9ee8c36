/**
 * @file j_pc_OreIntoBagCPlayerQEAAXGGEZ_1400027A2_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_OreIntoBagCPlayerQEAAXGGEZ_1400027A2
 * @note Address: 0x1400027A2
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_OreIntoBag@CPlayer@@QEAAXGGE@Z
 *Address: 0x1400027A2
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_OreIntoBag(CPlayer *this, unsigned __int16 wResIndex, unsigned __int16 wSerial, char byAddAmount)
{
  CPlayer::pc_OreIntoBag(this, wResIndex, wSerial, byAddAmount);
}



} // namespace Player
} // namespace RFOnline
