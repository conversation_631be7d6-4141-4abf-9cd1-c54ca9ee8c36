/**
 * @file j_pc_PostItemGoldRequestCPlayerQEAAXKZ_140008D69_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_PostItemGoldRequestCPlayerQEAAXKZ_140008D69
 * @note Address: 0x140008D69
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_PostItemGoldRequest@CPlayer@@QEAAXK@Z
 *Address: 0x140008D69
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_PostItemGoldRequest(CPlayer *this, unsigned int dwIndex)
{
  CPlayer::pc_PostItemGoldRequest(this, dwIndex);
}



} // namespace Player
} // namespace RFOnline
