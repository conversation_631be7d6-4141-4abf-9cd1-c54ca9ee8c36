/**
 * @file j_pc_TrunkHintAnswerRequestCPlayerQEAAXPEADZ_140005DB7_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_TrunkHintAnswerRequestCPlayerQEAAXPEADZ_140005DB7
 * @note Address: 0x140005DB7
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_TrunkHintAnswerRequest@CPlayer@@QEAAXPEAD@Z
 *Address: 0x140005DB7
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_TrunkHintAnswerRequest(CPlayer *this, char *pwszAnswer)
{
  CPlayer::pc_TrunkHintAnswerRequest(this, pwszAnswer);
}



} // namespace Player
} // namespace RFOnline
