﻿/*
 *Function: ?RestoreSystemTexture@@YAXXZ
 *Address: 0x1405021F0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void RestoreSystemTexture(void);
{
  __int64 v0; // rcx@1
  FILE*v1; // rax@4

  v0 = qword_184A79C20;
  void if(qword_18 4A7 9C20);
{
    (*(void (**)(void))(*(QWORD *)qword_184A79C20 + 16i64))();
    v0 = qword_184A79C20;
  }
  void if(qword_18 4A7 9C18);
{
    v1 = R3LoadDDS(".\\system\\logo.dds", 2u, 0x800u, 0x800u);
    v0 = qword_184A79C20;
    qword_184A79C18 = v1;
  }
  void if(v0 )
    qword_184A79C20 = (__int64)R3LoadDDS(".\\system\\dlight.dds", 2u, 0x800u, 0x800u);
}


