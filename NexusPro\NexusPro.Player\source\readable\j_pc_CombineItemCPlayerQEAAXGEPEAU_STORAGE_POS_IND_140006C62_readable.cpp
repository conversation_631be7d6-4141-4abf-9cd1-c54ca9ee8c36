/**
 * @file j_pc_CombineItemCPlayerQEAAXGEPEAU_STORAGE_POS_IND_140006C62_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_CombineItemCPlayerQEAAXGEPEAU_STORAGE_POS_IND_140006C62
 * @note Address: 0x140006C62
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_CombineItem@CPlayer@@QEAAXGEPEAU_STORAGE_POS_INDIV@@G@Z
 *Address: 0x140006C62
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_CombineItem(CPlayer *this, unsigned __int16 wManualIndex, char by<PERSON><PERSON><PERSON><PERSON><PERSON>, _STORAGE_POS_INDIV *pipMaterials, unsigned __int16 wOverlapSerial)
{
  CPlayer::pc_CombineItem(this, wManualIndex, byMaterialNum, pipMaterials, wOverlapSerial);
}



} // namespace Player
} // namespace RFOnline
