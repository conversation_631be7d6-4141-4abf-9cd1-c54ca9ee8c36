/**
 * @file j_pc_OffPartCPlayerQEAAXPEAU_STORAGE_POS_INDIVZ_140005051_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_OffPartCPlayerQEAAXPEAU_STORAGE_POS_INDIVZ_140005051
 * @note Address: 0x140005051
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_OffPart@CPlayer@@QEAAXPEAU_STORAGE_POS_INDIV@@@Z
 *Address: 0x140005051
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_OffPart(CPlayer *this, _STORAGE_POS_INDIV *pItem)
{
  CPlayer::pc_OffPart(this, pItem);
}



} // namespace Player
} // namespace RFOnline
