/**
 * @file j_mgr_recall_playerCPlayerQEAA_NPEADZ_1400118D8_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_mgr_recall_playerCPlayerQEAA_NPEADZ_1400118D8
 * @note Address: 0x1400118D8
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?mgr_recall_player@CPlayer@@QEAA_NPEAD@Z
 *Address: 0x1400118D8
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


bool __fastcall CPlayer::mgr_recall_player(CPlayer *this, char *pwszCharName)
{
  return CPlayer::mgr_recall_player(this, pwszCharName);
}



} // namespace Player
} // namespace RFOnline
