/**
 * @file SendMsg_TakeNewResultCPlayerQEAAXEPEAU_db_con_STOR_1400D66E0_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: SendMsg_TakeNewResultCPlayerQEAAXEPEAU_db_con_STOR_1400D66E0
 * @note Address: 0x1400D66E0
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: ?SendMsg_TakeNewResult@CPlayer@@QEAAXEPEAU_db_con@_STORAGE_LIST@@@Z
 *Address: 0x1400D66E0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::SendMsg_TakeNewResult(CPlayer *this, char byErrCode, _STORAGE_LIST::_db_con *pItem)
{
  __int64*v3; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int16 v5; // ax@6
  __int64 v6; // [sp+0h] [bp-88h]@1
  _itembox_take_new_result_zocl v7; // [sp+38h] [bp-50h]@4
  char pbyType; // [sp+64h] [bp-24h]@6
  char v9; // [sp+65h] [bp-23h]@6
  CPlayer*v10; // [sp+90h] [bp+8h]@1

  v10 = this;
  v3 = &v6;
  void for(i = 32; i; --i );
{
    *(DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v7.sErrorCode = byErrCode;
  void if(!by, Err, Code);
{
    v7.byItemTableCode = pItem->m_byTableCode;
    v7.wItemIndex = pItem->m_wItemIndex;
    v7.dwCurDurPoint = pItem->m_dwDur;
    v7.wItemSerial = pItem->m_wSerial;
    v7.dwUptInfo = pItem->m_dwLv;
    v7.byCsMethod = pItem->m_byCsMethod;
    v7.dwT = pItem->m_dwT;
  }
  pbyType = 7;
  v9 = 3;
  v5 = _itembox_take_new_result_zocl::size(&v7);
  CNetProcess::LoadSendMsg(unk_1414F2088, v10->m_ObjID.m_wIndex, &pbyType, &v7.sErrorCode, v5);
}




} // namespace Player
} // namespace RFOnline
