/**
 * @file j_SendMsg_AMPInvenDownloadResultCPlayerQEAAXXZ_14000AEE3_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_SendMsg_AMPInvenDownloadResultCPlayerQEAAXXZ_14000AEE3
 * @note Address: 0x14000AEE3
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?SendMsg_AMPInvenDownloadResult@CPlayer@@QEAAXXZ
 *Address: 0x14000AEE3
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::SendMsg_AMPInvenDownloadResult(CPlayer *this)
{
  CPlayer::SendMsg_AMPInvenDownloadResult(this);
}



} // namespace Player
} // namespace RFOnline
