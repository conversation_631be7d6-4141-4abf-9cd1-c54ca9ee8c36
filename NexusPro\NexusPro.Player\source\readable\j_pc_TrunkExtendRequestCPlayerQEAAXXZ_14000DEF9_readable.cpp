/**
 * @file j_pc_TrunkExtendRequestCPlayerQEAAXXZ_14000DEF9_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_TrunkExtendRequestCPlayerQEAAXXZ_14000DEF9
 * @note Address: 0x14000DEF9
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_TrunkExtendRequest@CPlayer@@QEAAXXZ
 *Address: 0x14000DEF9
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_TrunkExtendRequest(CPlayer *this)
{
  CPlayer::pc_TrunkExtendRequest(this);
}



} // namespace Player
} // namespace RFOnline
