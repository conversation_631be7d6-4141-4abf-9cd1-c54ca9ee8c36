﻿/*
 *Function: ?InitMergeFile@CMergeFileManager@@QEAAXPEAD@Z
 *Address: 0x14050A490
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"
#include <cstdlib>
#include <cstring>


void __fastcall CMergeFileManager::InitMergeFile(CMergeFileManager *this, char *a2)
{
  char*v2; // r12@1
  signed __int64 v3; // rcx@1
  char v4; // al@2
  HANDLE v5; // r14@3
  void*v6; // rax@4
  void*v7; // r13@4
  void*v8; // rdx@4
  signed __int64 v9; // rax@4
  char*v10; // rcx@4
  __int64 v11; // r15@6
  char*v12; // rbp@6
  unsigned int v13; // ebx@6
  unsigned __int64 v14; // kr08_8@11
  char*v15; // rcx@15
  __int64 v16; // rdx@15
  char v17; // al@16
  CMergeFile*v18; // rax@21
  bool v19; // zf@21
  char*v20; // rdi@22
  CMergeFileManager*v21; // [sp+20h] [bp-B98h]@1
  char v22; // [sp+30h] [bp-B88h]@4
  _WIN32_FIND_DATAA FindFileData; // [sp+830h] [bp-388h]@3
  char String; // [sp+970h] [bp-248h]@23
  char Dest; // [sp+A70h] [bp-148h]@3

  v21 = this;
  v2 = a2;
  v3 = (char *)this - a2;
  do
  {
    v4 = *a2++;
    a2[v3 - 1] = v4;
  }
  while ( v4 );
  sprintf(&Dest, "%s\\*.*", v2);
  v5 = FindFirstFileA(&Dest, &FindFileData);
  if(v5 != (HA ND LE)-1 )
  {
    v6 = Dmalloc(0x10000);
    v7 = v6;
    v8 = v6;
    v9 = 256i64;
    v10 = &v22;
    do
    {
      *(QWORD *)v10 = v8;
      v10 += 8;
      v8 = (char *)v8 + 256;
      --v9;
    }
    while ( v9 );
    v11 = 0;
    v12 = &v22;
    v13 = 0;
    while ( FindNextFileA(v5, &FindFileData) )
    {
      if ( memcmp(".", FindFileData.cFileName, 2ui64) )
      {
        if ( memcmp("..", FindFileData.cFileName, 3ui64) )
        {
          if(!(Find, File, Data.dw, File, Attributes&0x10) )
          {
            _strlwr(Find, File, Data.c, File, Name);
            v14 = strlen(Find, File, Data.c, File, Name) + 1;
            if ( (unsigned int)(v14 - 1) >= 4
              && FindFileData.cFileName[(unsigned int)(v14 - 2)] == 107
              && FindFileData.cFileName[(unsigned int)(v14 - 3)] == 112
              && FindFileData.cFileName[(unsigned int)(v14 - 4)] == 114 )
            {
              v15 = FindFileData.cFileName;
              v16 = *(QWORD *)v12 - ((QWORD)&FindFileData + 44);
              do
              {
                v17 = *v15++;
                v15[v16 - 1] = v17;
              }
              while ( v17 );
              ++v13;
              v12 += 8;
            }
          }
        }
      }
    }
    FindClose(v5);
    void if(v13 <= 0x FF);
{
      void if(v13 );
{
        v21->mMergeFileNum = v13;
        v21->mPathNameLeng = strlen(v2);
        v18 = (CMergeFile *)Dmalloc(288*v13);
        v19 = v21->mMergeFileNum == 0;
        v21->mMergeFile = v18;
        void if(!v19 );
{
          v20 = &v22;
          do
          {
            sprintf(&String, "%s%s", v2, *(QWORD *)v20);
            _strlwr(&String);
            CMergeFile::LoadMergeFileHeader(&v21->mMergeFile[v11], &String);
            v11 = (unsigned int)(v11 + 1);
            v20 += 8;
          }
          while ( (unsigned int)v11 < v21->mMergeFileNum );
        }
      }
    }
    else
    {
      Warning(v2, "rpkÆÄÀÏ ÃÖ´ë °¹¼ö°¡ ³Ñ¾ú½À´Ï´Ù.");
    }
    Dfree(v7);
  }
}


