/**
 * @file j_pc_TrunkIoMoveRequestCPlayerQEAAXEEGEZ_1400025D1_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_TrunkIoMoveRequestCPlayerQEAAXEEGEZ_1400025D1
 * @note Address: 0x1400025D1
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_TrunkIoMoveRequest@CPlayer@@QEAAXEEGE@Z
 *Address: 0x1400025D1
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_TrunkIoMoveRequest(CPlayer *this, char byStartStorageIndex, char byTarStorageIndex, unsigned __int16 wItemSerial, char byClientSlotIndex)
{
  CPlayer::pc_TrunkIoMoveRequest(this, byStartStorageIndex, byTarStorageIndex, wItemSerial, byClientSlotIndex);
}



} // namespace Player
} // namespace RFOnline
