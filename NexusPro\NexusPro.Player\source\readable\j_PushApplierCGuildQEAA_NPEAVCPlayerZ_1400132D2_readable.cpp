/**
 * @file j_PushApplierCGuildQEAA_NPEAVCPlayerZ_1400132D2_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_PushApplierCGuildQEAA_NPEAVCPlayerZ_1400132D2
 * @note Address: 0x1400132D2
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?PushApplier@CGuild@@QEAA_NPEAVCPlayer@@@Z
 *Address: 0x1400132D2
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


bool __fastcall CGuild::PushApplier(CGuild *this, CPlayer *pApplier)
{
  return CGuild::PushApplier(this, pApplier);
}



} // namespace Player
} // namespace RFOnline
