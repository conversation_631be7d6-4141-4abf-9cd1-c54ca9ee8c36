/**
 * @file j_pc_GuildCancelSuggestRequestCPlayerQEAAXKZ_1400073B0_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_GuildCancelSuggestRequestCPlayerQEAAXKZ_1400073B0
 * @note Address: 0x1400073B0
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_GuildCancelSuggestRequest@CPlayer@@QEAAXK@Z
 *Address: 0x1400073B0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_GuildCancelSuggestRequest(CPlayer *this, unsigned int dwMatterVoteSynKey)
{
  CPlayer::pc_GuildCancelSuggestRequest(this, dwMatterVoteSynKey);
}



} // namespace Player
} // namespace RFOnline
