/**
 * @file j_pc_UseRadarItemCPlayerQEAA_NPEAU_STORAGE_POS_IND_1400046FB_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_UseRadarItemCPlayerQEAA_NPEAU_STORAGE_POS_IND_1400046FB
 * @note Address: 0x1400046FB
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_UseRadarItem@CPlayer@@QEAA_NPEAU_STORAGE_POS_INDIV@@PEAG@Z
 *Address: 0x1400046FB
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


bool __fastcall CPlayer::pc_UseRadarItem(CPlayer *this, _STORAGE_POS_INDIV *pItem, unsigned __int16 *pConsumeSerial)
{
  return CPlayer::pc_UseRadarItem(this, pItem, pConsumeSerial);
}



} // namespace Player
} // namespace RFOnline
