/**
 * @file j_RenewOldMemberCTransportShipQEAA_NPEAVCPlayerZ_140009F7A_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_RenewOldMemberCTransportShipQEAA_NPEAVCPlayerZ_140009F7A
 * @note Address: 0x140009F7A
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?RenewOldMember@CTransportShip@@QEAA_NPEAVCPlayer@@@Z
 *Address: 0x140009F7A
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


bool __fastcall CTransportShip::RenewOldMember(CTransportShip *this, CPlayer *pMember)
{
  return CTransportShip::RenewOldMember(this, pMember);
}



} // namespace Player
} // namespace RFOnline
