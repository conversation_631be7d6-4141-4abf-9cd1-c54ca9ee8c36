/**
 * @file j_RobbedHPCMonsterUEAA_NPEAVCCharacterHZ_14000662C_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_RobbedHPCMonsterUEAA_NPEAVCCharacterHZ_14000662C
 * @note Address: 0x14000662C
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?<PERSON><PERSON><PERSON>@CMonster@@UEAA_NPEAVCCharacter@@H@Z
 *Address: 0x14000662C
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


bool __fastcall CMonster::RobbedHP(CMonster *this, CCharacter *pDst, int nDecHP)
{
  return CMonster::RobbedHP(this, pDst, nDecHP);
}



} // namespace Player
} // namespace RFOnline
