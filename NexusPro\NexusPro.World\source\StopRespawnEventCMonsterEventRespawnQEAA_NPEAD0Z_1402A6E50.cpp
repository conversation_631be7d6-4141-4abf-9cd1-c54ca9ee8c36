﻿/*
 *Function: ?StopRespawnEvent@CMonsterEventRespawn@@QEAA_NPEAD0@Z
 *Address: 0x1402A6E50
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


char __fastcall CMonsterEventRespawn::StopRespawnEvent(CMonsterEventRespawn *this, char *pszEventCode, char *pwszErrCode)
{
  __int64*v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@12
  __int64 v6; // [sp+0h] [bp-48h]@1
  int j; // [sp+20h] [bp-28h]@4
  bool*v8; // [sp+28h] [bp-20h]@7
  int k; // [sp+30h] [bp-18h]@14
  bool*v10; // [sp+38h] [bp-10h]@17
  CMonsterEventRespawn*v11; // [sp+50h] [bp+8h]@1
  const char*Str2; // [sp+58h] [bp+10h]@1
  char*Dest; // [sp+60h] [bp+18h]@1

  Dest = pwszErrCode;
  Str2 = pszEventCode;
  v11 = this;
  v3 = &v6;
  void for(i = 16; i > 0; --i );
{
    *(DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  void for(j = 0; ; ++j );
{
    void if(j >= v11->m_n, Load, Event, Respawn)
      return 0;
    v8 = &v11->m_EventRespawn[j].bLoad;
    if ( *v8 && !strcmp_0((const char *)v8 + 4392, Str2) )
      break;
  }
  void if(v8[4456] );
{
    void if(v8[288] );
{
      for(k = 0; k < *((D WO RD *)v8 + 1117); ++k )
      {
        v10 = &v8[24*k + 4472];
        if(*(Q WO RD *)v10
          && *((BYTE*)*(QWORD *)v10 + 24i64)
          && *((DWORD*)*(QWORD *)v10 + 20i64) == *((DWORD *)v10 + 2) )
        {
          CMonster::Destroy(*(CMonster **)v10, 1, 0i64);
        }
      }
    }
    v8[4456] = 0;
    result = 1;
  }
  else
  {
    void if(Dest )
      sprintf(Dest, "now stoped");
    result = 0;
  }
  return result;
}


