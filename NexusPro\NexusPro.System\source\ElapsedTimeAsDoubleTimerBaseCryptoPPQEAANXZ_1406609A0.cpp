﻿/*
 *Function: ?ElapsedTimeAsDouble@TimerBase@CryptoPP@@QEAANXZ
 *Address: 0x1406609A0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


double __fastcall CryptoPP::TimerBase::ElapsedTimeAsDouble(Crypto PP::Timer, Base *this)
{
  double result; // xmm0_8@2
  unsigned __int64 v2; // rax@4
  CryptoPP::TimerBase*v3; // [sp+40h] [bp+8h]@1

  v3 = this;
  if(*((BY TE *)this + 12) )
  {
    result = 0.0;
  }
  else if(*((BY TE *)this + 13) )
  {
    ((DWORD)(v2) = (**(int (***)(void))this)();
    if(*((Q WO RD *)v3 + 3) < v2 )
      *((QWORD *)v3 + 3) = v2;
    CryptoPP::TimerBase::ConvertTo((__int64)v3, *((QWORD *)v3 + 3) - *((QWORD *)v3 + 2), *((DWORD *)v3 + 2));
  }
  else
  {
    CryptoPP::TimerBase::StartTimer(this);
    result = 0.0;
  }
  return result;
}


