/**
 * @file j_pc_RequestUILockFindPWCPlayerQEAAXPEAVCUserDBPEA_14000B6DB_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_RequestUILockFindPWCPlayerQEAAXPEAVCUserDBPEA_14000B6DB
 * @note Address: 0x14000B6DB
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_RequestUILockFindPW@CPlayer@@QEAAXPEAVCUserDB@@PEAD@Z
 *Address: 0x14000B6DB
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_RequestUILockFindPW(CPlayer *this, CUserDB *pUserDB, char *uszHintAnswer)
{
  CPlayer::pc_RequestUILockFindPW(this, pUserDB, uszHintAnswer);
}



} // namespace Player
} // namespace RFOnline
