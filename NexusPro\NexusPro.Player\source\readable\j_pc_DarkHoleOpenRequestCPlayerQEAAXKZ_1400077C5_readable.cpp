/**
 * @file j_pc_DarkHoleOpenRequestCPlayerQEAAXKZ_1400077C5_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_DarkHoleOpenRequestCPlayerQEAAXKZ_1400077C5
 * @note Address: 0x1400077C5
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_DarkHoleOpenRequest@CPlayer@@QEAAXK@Z
 *Address: 0x1400077C5
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_DarkHoleOpenRequest(CPlayer *this, unsigned int dwItemSerial)
{
  CPlayer::pc_DarkHoleOpenRequest(this, dwItemSerial);
}



} // namespace Player
} // namespace RFOnline
