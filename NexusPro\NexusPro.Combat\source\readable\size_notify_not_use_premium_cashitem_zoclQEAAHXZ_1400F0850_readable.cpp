/**
 * @file size_notify_not_use_premium_cashitem_zoclQEAAHXZ_1400F0850_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: size_notify_not_use_premium_cashitem_zoclQEAAHXZ_1400F0850
 * @note Address: 0x1400F0850
 */

#include <cstdint>

namespace RFOnline {
namespace Combat {

﻿/*
 *Function: ?size@_notify_not_use_premium_cashitem_zocl@@QEAAHXZ
 *Address: 0x1400F0850
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


signed __int64 __fastcall _notify_not_use_premium_cashitem_zocl::size(_notify_not_use_premium_cashitem_zocl *this)
{
  return 2i64;
}



} // namespace Combat
} // namespace RFOnline
