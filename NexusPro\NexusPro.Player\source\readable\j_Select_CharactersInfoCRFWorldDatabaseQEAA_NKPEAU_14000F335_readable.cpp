/**
 * @file j_Select_CharactersInfoCRFWorldDatabaseQEAA_NKPEAU_14000F335_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_Select_CharactersInfoCRFWorldDatabaseQEAA_NKPEAU_14000F335
 * @note Address: 0x14000F335
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?Select_CharactersInfo@CRFWorldDatabase@@QEAA_NKPEAU_worlddb_character_array_info@@@Z
 *Address: 0x14000F335
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


bool __fastcall CRFWorldDatabase::Select_CharactersInfo(CRFWorldDatabase *this, unsigned int dwAccountSerial, _worlddb_character_array_info *pCharacterData)
{
  return CRFWorldDatabase::Select_CharactersInfo(this, dwAccountSerial, pCharacterData);
}



} // namespace Player
} // namespace RFOnline
