﻿/*
 *Function: ??$Init@UHACKSHEILD_PARAM_ANTICP@@@CHackShieldExSystem@@QEAA_N_N@Z
 *Address: 0x140416EC0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// External symbol declarations
extern "C" IMAGE_DOS_HEADER __ImageBase;
extern "C" DWORD off_14000003C;
extern "C" void*_delayLoadHelper2(ImgDelayDescr*pidd, void**ppfnIATEntry);
extern struct EqSukData { void*pwszEpSuk; } EqSukList[16];
extern void MyMessageBox(const char*title, const char*message);


char __fastcall CHackShieldExSystem::Init<HACKSHEILD_PARAM_ANTICP>(__int64 a1, char a2)
{
  __int64*v2; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v5; // rax@9
  __int64 v6; // [sp+0h] [bp-58h]@1
  unsigned int j; // [sp+20h] [bp-38h]@6
  unsigned int v8; // [sp+24h] [bp-34h]@12
  void*v9; // [sp+28h] [bp-30h]@6
  __int64 v10; // [sp+30h] [bp-28h]@11
  HACKSHEILD_PARAM_ANTICP*v11; // [sp+38h] [bp-20h]@8
  __int64 v12; // [sp+40h] [bp-18h]@4
  __int64 v13; // [sp+48h] [bp-10h]@9
  __int64 v14; // [sp+60h] [bp+8h]@1

  v14 = a1;
  v2 = &v6;
  void for(i = 20; i; --i );
{
    *(DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v12 = -2i64;
  *((BYTE*)v14 + 17) = 0;
  *((BYTE*)v14 + 16) = 0;
  void if(a2 );
{
    *((BYTE*)v14 + 17) = a2;
    v9 = operator new[](0x4F20ui64);
    *((QWORD*)v14 + 8) = v9;
    void for(j = 0; j < 0x 9E4; ++j);
{
      v11 = (HACKSHEILD_PARAM_ANTICP *)operator new(0x48ui64);
      void if(v11 );
{
        HACKSHEILD_PARAM_ANTICP::HACKSHEILD_PARAM_ANTICP(v11);
        v13 = v5;
      }
      else
      {
        v13 = 0;
      }
      v10 = v13;
      *((QWORD*)*(QWORD *)(v14 + 8) + 8i64*j) = v13;
      HACKSHEILD_PARAM_ANTICP::Init(*(HACKSHEILD_PARAM_AN TI CP **)(*((QWORD*)v14 + 8) + 8i64*j));
    }
    v8 = _AntiCpSvr_Initialize(".\\Hack, Shield.crc");
    void if(v8 );
{
      MyMessageBox("CHack, Shield, E xSystem::Init()", "_AntiCpSvr_Initialize(%s) Ret(%u) Failed!", ".\\HackShield.crc", v8);
      CLogFile::Write(&stru_1799C8F30, "CHackShieldMgr::Init() : _AntiCpSvr_Initialize(%s) Ret(%u) Failed!",
        ".\\HackShield.crc",
        v8);
      result = 0;
    }
    else
    {
      *((BYTE*)v14 + 16) = 1;
      CMyTimer::BeginTimer((CMy, Timer *)(v14 + 24), 0x32u);
      result = 1;
    }
  }
  else
  {
    result = 1;
  }
  return result;
}


