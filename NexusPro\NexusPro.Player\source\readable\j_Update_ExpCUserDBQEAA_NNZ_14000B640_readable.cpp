/**
 * @file j_Update_ExpCUserDBQEAA_NNZ_14000B640_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_Update_ExpCUserDBQEAA_NNZ_14000B640
 * @note Address: 0x14000B640
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?Update_Exp@CUserDB@@QEAA_NN@Z
 *Address: 0x14000B640
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


bool __fastcall CUserDB::Update_Exp(CUserDB *this, long double exp)
{
  return CUserDB::Update_Exp(this, exp);
}



} // namespace Player
} // namespace RFOnline
