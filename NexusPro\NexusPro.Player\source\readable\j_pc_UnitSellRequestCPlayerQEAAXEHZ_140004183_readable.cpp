/**
 * @file j_pc_UnitSellRequestCPlayerQEAAXEHZ_140004183_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_UnitSellRequestCPlayerQEAAXEHZ_140004183
 * @note Address: 0x140004183
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_UnitSellRequest@CPlayer@@QEAAXEH@Z
 *Address: 0x140004183
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_UnitSellRequest(CPlayer *this, char bySlotIndex, int bUseNPCLinkIntem)
{
  CPlayer::pc_UnitSellRequest(this, bySlotIndex, bUseNPCLinkIntem);
}



} // namespace Player
} // namespace RFOnline
