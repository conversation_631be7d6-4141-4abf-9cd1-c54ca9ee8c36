﻿/*
 *Function: ??0CRFMonsterAIMgr@@QEAA@XZ
 *Address: 0x14014C1E0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"
#include <vector>


void __fastcall CRFMonsterAIMgr::CRFMonsterAIMgr(CRFMonste rA IMgr *this)
{
  __int64*v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  CRFMonsterAIMgr*ptr; // [sp+40h] [bp+8h]@1

  ptr = this;
  v1 = &v3;
  void for(i = 12; i > 0; --i );
{
    *(DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  `eh std::vector constructor iterator'(
    ptr,
    8ui64,
    1,
    (void (__cdecl *)(void *))UsPoint<UsStateTBL>::`default constructor closure',
    (void (__cdecl *)(void *))UsPoint<UsStateTBL>::~UsPoint<UsStateTBL>);
}


