﻿/*
 *Function: _CryptoPP::CipherModeFinalTemplate_CipherHolder_CryptoPP::BlockCipherFinal_0_CryptoPP::Rijndael::Enc__CryptoPP::CBC_Encryption_::_CipherModeFinalTemplate_CipherHolder_CryptoPP::BlockCipherFinal_0_CryptoPP::Rijndael::Enc__CryptoPP::CBC_Encryption__::_1_::dtor$0
 *Address: 0x14044E570
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CryptoPP::CipherModeFinalTemplate_CipherHolder_CryptoPP::BlockCipherFinal_0_CryptoPP::Rijndael::Enc__CryptoPP::CBC_Encryption_::_CipherModeFinalTemplate_CipherHolder_CryptoPP::BlockCipherFinal_0_CryptoPP::Rijndael::Enc__CryptoPP::CBC_Encryption__::_1_::dtor_0(__int64 a1, __int64 a2)
{
  if(*((Q WO RD*)a2 + 64) )
    *((QWORD*)a2 + 40) = *((QWORD*)a2 + 64) + 72i64;
  else
    *((QWORD*)a2 + 40) = 0;
  CryptoPP::ObjectHolder<CryptoPP::BlockCipherFinal<0,CryptoPP::Rijndael::Enc > >::~ObjectHolder<CryptoPP::BlockCipherFinal<0,CryptoPP::Rijndael::Enc > >(*(CryptoPP::ObjectHolder<CryptoPP::BlockCipherFinal<0,CryptoPP::Rijndael::Enc>  > **)(a2 + 40));
}


