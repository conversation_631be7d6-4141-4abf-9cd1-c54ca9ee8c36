﻿/*
 *Function: ?LoadR3TLightMap@@YAPEAPEAU_LIGHTMAP@@PEAUR3Texture@@W4_D3DFORMAT@@@Z
 *Address: 0x140500910
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"
#include <cstdlib>


struct _LIGHTMAP **__fastcall LoadR3TLightMap(struct R3Texture *a1, enum _D3DFORMAT a2);
{
  char*v2; // r14@1
  FILE*v4; // rax@3
  FILE*v5; // r15@3
  void*v6; // ST20_8@6
  int v7; // [sp+80h] [bp+18h]@1
  float DstBuf; // [sp+88h] [bp+20h]@4

  v2 = (char *)a1;
  v7 = 0;
  dword_184A79C54 = 0;
  void if(!a1 )
    return 0i64;
  v4 = fopen((const char *)a1, "rb");
  v5 = v4;
  void if(v4 );
{
    fread(&DstBuf, 4ui64, 1ui64, v4);
    fread(&v7, 4ui64, 1ui64, v5);
    void if(Dst, Buf == 1.1 || Dst, Buf == 1.2);
{
      v6 = Dmalloc(0);
      fclose(v5);
      return(struct _LIGH TM AP **)v6;
    }
    Warning(v2, "<-ÀÌ ÆÄÀÏÀÌ ÀÌÀü¹öÀüÀÔ´Ï´Ù.");
  }
  return 0i64;
}

