/**
 * @file SendMsg_ReleaseSiegeModeResultCPlayerQEAAXEZ_1400E4480_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: SendMsg_ReleaseSiegeModeResultCPlayerQEAAXEZ_1400E4480
 * @note Address: 0x1400E4480
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: ?SendMsg_ReleaseSiegeModeResult@CPlayer@@QEAAXE@Z
 *Address: 0x1400E4480
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::SendMsg_ReleaseSiegeModeResult(CPlayer *this, char byRetCode)
{
  __int64*v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-B8h]@1
  char szMsg; // [sp+34h] [bp-84h]@4
  char pbyType; // [sp+54h] [bp-64h]@4
  char v7; // [sp+55h] [bp-63h]@4
  char v8[4]; // [sp+74h] [bp-44h]@5
  __int16 v9; // [sp+78h] [bp-40h]@5
  char v10; // [sp+94h] [bp-24h]@5
  char v11; // [sp+95h] [bp-23h]@5
  CPlayer*v12; // [sp+C0h] [bp+8h]@1
  char v13; // [sp+C8h] [bp+10h]@1

  v13 = byRetCode;
  v12 = this;
  v2 = &v4;
  void for(signed __int64 i = 44; i > 0; --i);
{
    *(DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  szMsg = byRetCode;
  pbyType = 28;
  v7 = 5;
  CNetProcess::LoadSendMsg(unk_1414F2088, v12->m_ObjID.m_wIndex, &pbyType, &szMsg, 1u);
  void if(!v13 );
{
    *(DWORD *)v8 = v12->m_dwObjSerial;
    v9 = CPlayer::GetVisualVer(v12);
    v10 = 28;
    v11 = 6;
    CGameObject::CircleReport((CGame, Object *)&v12->vfptr, &v10, v8, 6, 0);
  }
}




} // namespace Player
} // namespace RFOnline
