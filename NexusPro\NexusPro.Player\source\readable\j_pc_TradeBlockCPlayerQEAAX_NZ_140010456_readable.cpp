/**
 * @file j_pc_TradeBlockCPlayerQEAAX_NZ_140010456_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_TradeBlockCPlayerQEAAX_NZ_140010456
 * @note Address: 0x140010456
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_TradeBlock@CPlayer@@QEAAX_N@Z
 *Address: 0x140010456
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_TradeBlock(CPlayer *this, bool bBlock)
{
  CPlayer::pc_TradeBlock(this, bBlock);
}



} // namespace Player
} // namespace RFOnline
