/**
 * @file j_pc_RequestChangeTaxRateCPlayerQEAAXEZ_1400092E6_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_RequestChangeTaxRateCPlayerQEAAXEZ_1400092E6
 * @note Address: 0x1400092E6
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_RequestChangeTaxRate@CPlayer@@QEAAXE@Z
 *Address: 0x1400092E6
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_RequestChangeTaxRate(CPlayer *this, char byTaxRate)
{
  CPlayer::pc_RequestChangeTaxRate(this, byTaxRate);
}



} // namespace Player
} // namespace RFOnline
