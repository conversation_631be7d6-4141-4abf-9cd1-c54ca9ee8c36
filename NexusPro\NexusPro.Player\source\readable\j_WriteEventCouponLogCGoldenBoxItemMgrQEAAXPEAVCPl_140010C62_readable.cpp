/**
 * @file j_WriteEventCouponLogCGoldenBoxItemMgrQEAAXPEAVCPl_140010C62_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_WriteEventCouponLogCGoldenBoxItemMgrQEAAXPEAVCPl_140010C62
 * @note Address: 0x140010C62
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?WriteEventCouponLog@CGoldenBoxItemMgr@@QEAAXPEAVCPlayer@@PEAU_db_con@_STORAGE_LIST@@@Z
 *Address: 0x140010C62
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CGoldenBoxItemMgr::WriteEventCouponLog(CGoldenBoxItemMgr *this, CPlayer *pOne, _STORAGE_LIST::_db_con *pItem)
{
  CGoldenBoxItemMgr::WriteEventCouponLog(this, pOne, pItem);
}



} // namespace Player
} // namespace RFOnline
