/**
 * @file SendMsg_UnitFrameRepairResultCPlayerQEAAXEEKKZ_1400DAFB0_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: SendMsg_UnitFrameRepairResultCPlayerQEAAXEEKKZ_1400DAFB0
 * @note Address: 0x1400DAFB0
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: ?SendMsg_UnitFrameRepairResult@CPlayer@@QEAAXEEKK@Z
 *Address: 0x1400DAFB0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::SendMsg_UnitFrameRepairResult(CPlayer *this, char byRetCode, char bySlotIndex, unsigned int dwNewGauge, unsigned int dwConsumDalant)
{
  __int64*v5; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // [sp+0h] [bp-88h]@1
  char szMsg; // [sp+38h] [bp-50h]@4
  char v9; // [sp+39h] [bp-4Fh]@4
  unsigned int v10; // [sp+3Ah] [bp-4Eh]@4
  unsigned int v11; // [sp+3Eh] [bp-4Ah]@4
  unsigned int v12; // [sp+42h] [bp-46h]@4
  char pbyType; // [sp+64h] [bp-24h]@4
  char v14; // [sp+65h] [bp-23h]@4
  CPlayer*v15; // [sp+90h] [bp+8h]@1

  v15 = this;
  v5 = &v7;
  void for(i = 32; i; --i );
{
    *(DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  szMsg = byRetCode;
  v9 = bySlotIndex;
  v10 = dwNewGauge;
  v11 = dwConsumDalant;
  v12 = CPlayerDB::GetDalant(&v15->m _Param);
  pbyType = 23;
  v14 = 8;
  CNetProcess::LoadSendMsg(unk_1414F2088, v15->m_ObjID.m_wIndex, &pbyType, &szMsg, 0xEu);
}




} // namespace Player
} // namespace RFOnline
