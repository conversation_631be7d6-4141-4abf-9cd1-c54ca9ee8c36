/**
 * @file j_Select_CharacterReNameCRFWorldDatabaseQEAA_NPEAD_14000BAF5_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_Select_CharacterReNameCRFWorldDatabaseQEAA_NPEAD_14000BAF5
 * @note Address: 0x14000BAF5
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?Select_CharacterReName@CRFWorldDatabase@@QEAA_NPEADPEAK@Z
 *Address: 0x14000BAF5
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


bool __fastcall CRFWorldDatabase::Select_CharacterReName(CRFWorldDatabase *this, char *pwszName, unsigned int *pSerial)
{
  return CRFWorldDatabase::Select_CharacterReName(this, pwszName, pSerial);
}



} // namespace Player
} // namespace RFOnline
