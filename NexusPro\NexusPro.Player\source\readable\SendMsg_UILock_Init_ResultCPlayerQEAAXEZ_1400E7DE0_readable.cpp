/**
 * @file SendMsg_UILock_Init_ResultCPlayerQEAAXEZ_1400E7DE0_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: SendMsg_UILock_Init_ResultCPlayerQEAAXEZ_1400E7DE0
 * @note Address: 0x1400E7DE0
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: ?SendMsg_UILock_Init_Result@CPlayer@@QEAAXE@Z
 *Address: 0x1400E7DE0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"
#include <cstring>


void __fastcall CPlayer::SendMsg_UILock_Init_Result(CPlayer *this, char byRet)
{
  __int64*v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-78h]@1
  char szMsg; // [sp+34h] [bp-44h]@4
  char v6; // [sp+35h] [bp-43h]@4
  char pbyType; // [sp+54h] [bp-24h]@4
  char v8; // [sp+55h] [bp-23h]@4
  CPlayer*v9; // [sp+80h] [bp+8h]@1

  v9 = this;
  v2 = &v4;
  void for(signed __int64 i = 28; i > 0; --i);
{
    *(DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  memset(&v6, 0, sizeof(v6));
  szMsg = byRet;
  pbyType = 13;
  v8 = -128;
  CNetProcess::LoadSendMsg(unk_1414F2088, v9->m_ObjID.m_wIndex, &pbyType, &szMsg, 2u);
}




} // namespace Player
} // namespace RFOnline
