/**
 * @file j_SendMsg_AlterMaxDPCPlayerQEAAXXZ_14000B2C1_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_SendMsg_AlterMaxDPCPlayerQEAAXXZ_14000B2C1
 * @note Address: 0x14000B2C1
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?SendMsg_AlterMaxDP@CPlayer@@QEAAXXZ
 *Address: 0x14000B2C1
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::SendMsg_AlterMaxDP(CPlayer *this)
{
  CPlayer::SendMsg_AlterMaxDP(this);
}



} // namespace Player
} // namespace RFOnline
