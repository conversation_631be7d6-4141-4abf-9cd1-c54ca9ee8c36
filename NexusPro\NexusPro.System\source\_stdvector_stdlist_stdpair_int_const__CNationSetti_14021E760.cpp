﻿/*
 *Function: _std::vector_std::list_std::pair_int_const__CNationSettingFactory_____ptr64__std::allocator_std::pair_int_const__CNationSettingFactory_____ptr64_____::_Iterator_0__std::allocator_std::list_std::pair_int_const__CNationSettingFactory_____ptr64__std::allocator_std::pair_int_const__CNationSettingFactory_____ptr64_____::_Iterator_0_____::erase_::_1_::dtor$2
 *Address: 0x14021E760
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"
#include <vector>

// Additional STL includes for compilation
#include <iterator>
#include <list>
#include <memory>
#include <utility>



void __fastcall std::vector_std::list_std::pair_int_const__CNationSettingFactory_____ptr64__std::allocator_std::pair_int_const__CNationSettingFactory_____ptr64_____::_Iterator_0__std::allocator_std::list_std::pair_int_const__CNationSettingFactory_____ptr64__std::allocator_std::pair_int_const__CNationSettingFactory_____ptr64_____::_Iterator_0_____::erase_::_1_::dtor_2(__int64 a1, __int64 a2)
{
  if(*((D WO RD*)a2 + 40) &1 )
  {
    *((DWORD*)a2 + 40) &= 0xFFFFFFFE;
    std::_Vector_iterator<std::list<std::pair<int const,CNationSettingFactory *>,std::allocator<std::pair<int const,CNationSettingFactory * > >>::_Iterator<0>,std::allocator<std::list<std::pair<int const,CNationSettingFactory *>,std::allocator<std::pair<int const,CNationSettingFactory * > >>::_Iterator<0>>>::~_Vector_iterator<std::list<std::pair<int const,CNationSettingFactory *>,std::allocator<std::pair<int const,CNationSettingFactory * > >>::_Iterator<0>,std::allocator<std::list<std::pair<int const,CNationSettingFactory *>,std::allocator<std::pair<int const,CNationSettingFactory * > >>::_Iterator<0>>>(*(std::_Vector_iterator<std::list<std::pair<int const ,CNationSettingFactory *>,std::allocator<std::pair<int const ,CNationSettingFactory *>  > >::_Iterator<0>,std::allocator<std::list<std::pair<int const ,CNationSettingFactory *>,std::allocator<std::pair<int const ,CNationSettingFactory *>  > >::_Iterator<0> > > **)(a2 + 88));
  }
}



