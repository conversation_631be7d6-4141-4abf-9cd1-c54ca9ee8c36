﻿/*
 *Function: _std::list_std::pair_int_const__CNationSettingFactory_____ptr64__std::allocator_std::pair_int_const__CNationSettingFactory_____ptr64_____::erase_::_1_::dtor$4
 *Address: 0x14021E270
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// Additional STL includes for compilation
#include <iterator>
#include <list>
#include <memory>
#include <utility>



void __fastcall std::list_std::pair_int_const__CNationSettingFactory_____ptr64__std::allocator_std::pair_int_const__CNationSettingFactory_____ptr64_____::erase_::_1_::dtor_4(__int64 a1, __int64 a2)
{
  if(*((D WO RD*)a2 + 144) &4 )
  {
    *((DWORD*)a2 + 144) &= 0xFFFFFFFB;
    std::list<std::pair<int const,CNationSettingFactory *>,std::allocator<std::pair<int const,CNationSettingFactory * > >>::_Iterator<0>::~_Iterator<0>(*(std::list<std::pair<int const ,CNationSettingFactory *>,std::allocator<std::pair<int const ,CNationSettingFactory *>  > >::_Iterator<0> **)(a2 + 248));
  }
}



