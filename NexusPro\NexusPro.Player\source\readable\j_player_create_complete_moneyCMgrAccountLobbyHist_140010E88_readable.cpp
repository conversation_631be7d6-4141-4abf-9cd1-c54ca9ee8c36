/**
 * @file j_player_create_complete_moneyCMgrAccountLobbyHist_140010E88_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_player_create_complete_moneyCMgrAccountLobbyHist_140010E88
 * @note Address: 0x140010E88
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?player_create_complete_money@CMgrAccountLobbyHistory@@QEAAXPEAU_AVATOR_DATA@@PEAD@Z
 *Address: 0x140010E88
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CMgrAccountLobbyHistory::player_create_complete_money(CMgrAccountLobbyHistory *this, _AVATOR_DATA *pAvator, char *pszFileName)
{
  CMgrAccountLobbyHistory::player_create_complete_money(this, pAvator, pszFileName);
}



} // namespace Player
} // namespace RFOnline
