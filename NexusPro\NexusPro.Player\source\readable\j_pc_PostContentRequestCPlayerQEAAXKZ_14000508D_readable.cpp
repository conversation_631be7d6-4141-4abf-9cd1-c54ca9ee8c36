/**
 * @file j_pc_PostContentRequestCPlayerQEAAXKZ_14000508D_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_PostContentRequestCPlayerQEAAXKZ_14000508D
 * @note Address: 0x14000508D
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_PostContentRequest@CPlayer@@QEAAXK@Z
 *Address: 0x14000508D
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_PostContentRequest(CPlayer *this, unsigned int dwIndex)
{
  CPlayer::pc_PostContentRequest(this, dwIndex);
}



} // namespace Player
} // namespace RFOnline
