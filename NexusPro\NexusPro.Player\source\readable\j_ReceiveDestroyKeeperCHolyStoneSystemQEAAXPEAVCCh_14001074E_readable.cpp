/**
 * @file j_ReceiveDestroy<PERSON>eeperCHolyStoneSystemQEAAXPEAVCCh_14001074E_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_ReceiveDestroy<PERSON>eeperCHolyStoneSystemQEAAXPEAVCCh_14001074E
 * @note Address: 0x14001074E
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?ReceiveDestroyKeeper@CHolyStoneSystem@@QEAAXPEAVC<PERSON>haracter@@@Z
 *Address: 0x14001074E
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CHolyStoneSystem::ReceiveDestroyKeeper(CHolyStoneSystem *this, <PERSON>haracter *pCharacter)
{
  CHolyStoneSystem::ReceiveDestroy<PERSON>eeper(this, <PERSON><PERSON><PERSON><PERSON>);
}



} // namespace Player
} // namespace RFOnline
