/**
 * @file j_pc_UnitPartTuningRequestCPlayerQEAAXEEPEAU_tunin_140006488_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_UnitPartTuningRequestCPlayerQEAAXEEPEAU_tunin_140006488
 * @note Address: 0x140006488
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_UnitPartTuningRequest@CPlayer@@QEAAXEEPEAU_tuning_data@@H@Z
 *Address: 0x140006488
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_UnitPartTuningRequest(CPlayer *this, char bySlotIndex, char byTuningNum, _tuning_data *pTuningData, int bUseNPCLinkIntem)
{
  CPlayer::pc_UnitPartTuningRequest(this, bySlotIndex, byTuningNum, pTuningData, bUseNPCLinkIntem);
}



} // namespace Player
} // namespace RFOnline
