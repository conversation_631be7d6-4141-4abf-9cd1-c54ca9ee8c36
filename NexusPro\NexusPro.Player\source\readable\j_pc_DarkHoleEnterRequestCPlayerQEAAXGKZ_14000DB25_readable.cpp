/**
 * @file j_pc_DarkHoleEnterRequestCPlayerQEAAXGKZ_14000DB25_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_DarkHoleEnterRequestCPlayerQEAAXGKZ_14000DB25
 * @note Address: 0x14000DB25
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_DarkHoleEnterRequest@CPlayer@@QEAAXGK@Z
 *Address: 0x14000DB25
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_DarkHoleEnterRequest(CPlayer *this, unsigned __int16 wHoleIndex, unsigned int dwHoleSerial)
{
  CPlayer::pc_DarkHoleEnterRequest(this, wHoleIndex, dwHoleSerial);
}



} // namespace Player
} // namespace RFOnline
