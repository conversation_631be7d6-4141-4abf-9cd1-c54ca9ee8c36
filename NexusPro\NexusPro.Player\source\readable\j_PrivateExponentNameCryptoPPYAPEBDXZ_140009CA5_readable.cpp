/**
 * @file j_PrivateExponentNameCryptoPPYAPEBDXZ_140009CA5_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_PrivateExponentNameCryptoPPYAPEBDXZ_140009CA5
 * @note Address: 0x140009CA5
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?PrivateExponent@Name@CryptoPP@@YAPEBDXZ
 *Address: 0x140009CA5
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


const char *__cdecl CryptoPP::Name::PrivateExponent()
{
  return CryptoPP::Name::PrivateExponent();
}



} // namespace Player
} // namespace RFOnline
