/**
 * @file SendMsg_ResSeparationCPlayerQEAAXEPEAU_db_con_STOR_1400DC140_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: SendMsg_ResSeparationCPlayerQEAAXEPEAU_db_con_STOR_1400DC140
 * @note Address: 0x1400DC140
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: ?SendMsg_ResSeparation@CPlayer@@QEAAXEPEAU_db_con@_STORAGE_LIST@@0@Z
 *Address: 0x1400DC140
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::SendMsg_ResSeparation(CPlayer *this, char byErrCode, _STORAGE_LIST::_db_con *pStartOre, _STORAGE_LIST::_db_con *pNewOre)
{
  __int64*v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-78h]@1
  char szMsg; // [sp+34h] [bp-44h]@4
  unsigned __int16 v8; // [sp+35h] [bp-43h]@5
  char v9; // [sp+37h] [bp-41h]@5
  unsigned __int16 v10; // [sp+38h] [bp-40h]@5
  char v11; // [sp+3Ah] [bp-3Eh]@5
  char pbyType; // [sp+54h] [bp-24h]@6
  char v13; // [sp+55h] [bp-23h]@6
  CPlayer*v14; // [sp+80h] [bp+8h]@1

  v14 = this;
  v4 = &v6;
  void for(signed __int64 i = 28; i > 0; --i);
{
    *(DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  szMsg = byErrCode;
  void if(!by, Err, Code);
{
    v8 = pStartOre->m_wSerial;
    v9 = pStartOre->m_dwDur;
    v10 = pNewOre->m_wSerial;
    v11 = pNewOre->m_dwDur;
  }
  pbyType = 13;
  v13 = 7;
  CNetProcess::LoadSendMsg(unk_1414F2088, v14->m_ObjID.m_wIndex, &pbyType, &szMsg, 7u);
}




} // namespace Player
} // namespace RFOnline
