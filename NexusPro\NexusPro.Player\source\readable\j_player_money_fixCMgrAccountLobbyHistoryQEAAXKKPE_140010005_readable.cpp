/**
 * @file j_player_money_fixCMgrAccountLobbyHistoryQEAAXKKPE_140010005_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_player_money_fixCMgrAccountLobbyHistoryQEAAXKKPE_140010005
 * @note Address: 0x140010005
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?player_money_fix@CMgrAccountLobbyHistory@@QEAAXKKPEAU_AVATOR_DATA@@PEAD@Z
 *Address: 0x140010005
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CMgrAccountLobbyHistory::player_money_fix(CMgrAccountLobbyHistory *this, unsigned int dwOldDalant, unsigned int dwOldGold, _AVATOR_DATA *pAvator, char *pszFileName)
{
  CMgrAccountLobbyHistory::player_money_fix(this, dwOldDalant, dwOldGold, pAvator, pszFileName);
}



} // namespace Player
} // namespace RFOnline
