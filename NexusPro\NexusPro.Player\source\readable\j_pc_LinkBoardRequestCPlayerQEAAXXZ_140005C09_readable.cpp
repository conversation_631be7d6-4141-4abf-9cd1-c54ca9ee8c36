/**
 * @file j_pc_LinkBoardRequestCPlayerQEAAXXZ_140005C09_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_LinkBoardRequestCPlayerQEAAXXZ_140005C09
 * @note Address: 0x140005C09
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_LinkBoardRequest@CPlayer@@QEAAXXZ
 *Address: 0x140005C09
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_LinkBoardRequest(CPlayer *this)
{
  CPlayer::pc_LinkBoardRequest(this);
}



} // namespace Player
} // namespace RFOnline
