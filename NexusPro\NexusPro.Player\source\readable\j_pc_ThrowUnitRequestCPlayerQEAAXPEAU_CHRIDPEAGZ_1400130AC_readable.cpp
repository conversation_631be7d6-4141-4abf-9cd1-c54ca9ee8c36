/**
 * @file j_pc_ThrowUnitRequestCPlayerQEAAXPEAU_CHRIDPEAGZ_1400130AC_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_ThrowUnitRequestCPlayerQEAAXPEAU_CHRIDPEAGZ_1400130AC
 * @note Address: 0x1400130AC
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_ThrowUnitRequest@CPlayer@@QEAAXPEAU_CHRID@@PEAG@Z
 *Address: 0x1400130AC
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_ThrowUnitRequest(CPlayer *this, _CHRID *pidDst, unsigned __int16 *pConsumeSerial)
{
  CPlayer::pc_ThrowUnitRequest(this, pidDst, pConsumeSerial);
}



} // namespace Player
} // namespace RFOnline
