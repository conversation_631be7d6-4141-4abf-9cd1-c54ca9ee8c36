/**
 * @file start_cdeCashItemRemoteStoreQEAA_NHHHHZ_1402F7590_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: start_cdeCashItemRemoteStoreQEAA_NHHHHZ_1402F7590
 * @note Address: 0x1402F7590
 */

#include <cstdint>

namespace RFOnline {
namespace Combat {

﻿/*
 *Function: ?start_cde@CashItemRemoteStore@@QEAA_NHHHH@Z
 *Address: 0x1402F7590
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"
#include <cstring>


char __fastcall CashItemRemoteStore::start_cde(CashItemRemoteStore *this, int iBegin_TT, int iB30_TT, int iB5_TT, int iEnd_TT)
{
  __int64*v5; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@6
  __int64 v8; // [sp+0h] [bp-108h]@1
  __time32_t Time; // [sp+24h] [bp-E4h]@7
  __time32_t v10; // [sp+44h] [bp-C4h]@7
  __time32_t v11; // [sp+64h] [bp-A4h]@7
  char Dst; // [sp+88h] [bp-80h]@7
  int v13; // [sp+8Ch] [bp-7Ch]@7
  int v14; // [sp+90h] [bp-78h]@7
  int v15; // [sp+94h] [bp-74h]@7
  int v16; // [sp+98h] [bp-70h]@7
  int v17; // [sp+9Ch] [bp-6Ch]@7
  struct tm Tm; // [sp+C8h] [bp-40h]@7
  CashItemRemoteStore*v19; // [sp+110h] [bp+8h]@1
  int v20; // [sp+118h] [bp+10h]@1
  int v21; // [sp+120h] [bp+18h]@1
  int v22; // [sp+128h] [bp+20h]@1

  v22 = iB5_TT;
  v21 = iB30_TT;
  v20 = iBegin_TT;
  v19 = this;
  v5 = &v8;
  void for(i = 64; i; --i );
{
    *(DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  if(v19->m_cde.m_ini.m_b, Use, Cash, Discount && Cash, Item, Remote, Store::is_cde_time(v19) )
  {
    CLogFile::Write(&v19->m_cde.m_cde_log, "Cheat-Command was disregarded. Other Event is on");
    result = 0;
  }
  else
  {
    _time32(&Time);
    v10 = v20 + Time;
    v11 = iEnd_TT + v22 + v21 + v20 + Time;
    v19->m_cde.m_ini.m_bUseCashDiscount = 1;
    v19->m_cde.m_ini.m_cdeTime[0] = v10;
    v19->m_cde.m_ini.m_cdeTime[1] = v11;
    v19->m_cde.m_cde_inform_before[0] = v22 + v21;
    v19->m_cde.m_cde_inform_before[1] = v21;
    memset_0(&Dst, 0, 0x24ui64);
    memset_0(&Tm, 0, 0x24ui64);
    _localtime32_s((struct tm *)&Dst, &v10);
    _localtime32_s(&Tm, &v11);
    v19->m_cde.m_ini.m_wYear[0] = v17 + 1900;
    v19->m_cde.m_ini.m_byMonth[0] = v16 + 1;
    v19->m_cde.m_ini.m_byDay[0] = v15;
    v19->m_cde.m_ini.m_byHour[0] = v14;
    v19->m_cde.m_ini.m_byMinute[0] = v13;
    v19->m_cde.m_ini.m_wYear[1] = Tm.tm_year + 1900;
    v19->m_cde.m_ini.m_byMonth[1] = Tm.tm_mon + 1;
    v19->m_cde.m_ini.m_byDay[1] = Tm.tm_mday;
    v19->m_cde.m_ini.m_byHour[1] = Tm.tm_hour;
    v19->m_cde.m_ini.m_byMinute[1] = Tm.tm_min;
    CashItemRemoteStore::set_cde_status(v19, 1);
    CashItemRemoteStore::log_about_cash_event(v19, "Loaded From Cheat-Args <When Server Running>", &v19->m_cde.m_ini);
    result = 1;
  }
  return result;
}




} // namespace Combat
} // namespace RFOnline
