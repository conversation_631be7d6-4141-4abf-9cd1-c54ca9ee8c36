/**
 * @file j_pc_ChatGuildRequestCPlayerQEAAXKPEADZ_14000474B_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_ChatGuildRequestCPlayerQEAAXKPEADZ_14000474B
 * @note Address: 0x14000474B
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_ChatGuildRequest@CPlayer@@QEAAXKPEAD@Z
 *Address: 0x14000474B
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_ChatGuildRequest(CPlayer *this, unsigned int dwDstSerial, char *pwszChatData)
{
  CPlayer::pc_ChatGuildRequest(this, dwDstSerial, pwszChatData);
}



} // namespace Player
} // namespace RFOnline
