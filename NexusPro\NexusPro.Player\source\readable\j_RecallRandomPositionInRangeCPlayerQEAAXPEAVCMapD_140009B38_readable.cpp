/**
 * @file j_RecallRandomPositionInRangeCPlayerQEAAXPEAVCMapD_140009B38_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_RecallRandomPositionInRangeCPlayerQEAAXPEAVCMapD_140009B38
 * @note Address: 0x140009B38
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?RecallRandomPositionInRange@CPlayer@@QEAAXPEAVCMapData@@GPEAMH@Z
 *Address: 0x140009B38
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::RecallRandomPositionInRange(CPlayer *this, CMapData *pIntoMap, unsigned __int16 wMapLayerIndex, float *pStartPos, int iRange)
{
  CPlayer::RecallRandomPositionInRange(this, pIntoMap, wMapLayerIndex, pStartPos, iRange);
}



} // namespace Player
} // namespace RFOnline
