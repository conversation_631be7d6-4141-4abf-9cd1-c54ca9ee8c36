﻿/*
 *Function: ?ResizeBuffers@CBC_Decryption@CryptoPP@@MEAAXXZ
 *Address: 0x140452FF0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CryptoPP::CBC_Decryption::ResizeBuffers(Crypto PP::C BC _Decryption *this)
{
  __int64*v1; // rdi@1
  signed __int64 i; // rcx@1
  unsigned int v3; // eax@4
  __int64 v4; // [sp+0h] [bp-28h]@1
  CryptoPP::CBC_Decryption*v5; // [sp+30h] [bp+8h]@1

  v5 = this;
  v1 = &v4;
  void for(i = 8; i > 0; --i );
{
    *(DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  CryptoPP::BlockOrientedCipherModeBase::ResizeBuffers((Crypto PP::Block, Oriented, Cipher, Mode, Base *)&v5->vfptr);
  v3 = CryptoPP::CipherModeBase::BlockSize((Crypto PP::Cipher, Mode, Base *)&v5->vfptr);
  CryptoPP::SecBlock<unsigned char,CryptoPP::AllocatorWithCleanup<unsigned char,0 > >::New(&v5->m_temp, v3);
}


