/**
 * @file j_pc_PartyLeaveSelfReqeuestCPlayerQEAAXXZ_1400014CE_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_PartyLeaveSelfReqeuestCPlayerQEAAXXZ_1400014CE
 * @note Address: 0x1400014CE
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_PartyLeaveSelfReqeuest@CPlayer@@QEAAXXZ
 *Address: 0x1400014CE
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_PartyLeaveSelfReqeuest(CPlayer *this)
{
  CPlayer::pc_PartyLeaveSelfReqeuest(this);
}



} // namespace Player
} // namespace RFOnline
