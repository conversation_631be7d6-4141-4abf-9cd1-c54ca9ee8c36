/**
 * @file j_pc_QuestGiveupRequestCPlayerQEAAXEZ_14000C1F3_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_QuestGiveupRequestCPlayerQEAAXEZ_14000C1F3
 * @note Address: 0x14000C1F3
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_QuestGiveupRequest@CPlayer@@QEAAXE@Z
 *Address: 0x14000C1F3
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_QuestGiveupRequest(CPlayer *this, char byQuestDBSlot)
{
  CPlayer::pc_QuestGiveupRequest(this, byQuestDBSlot);
}



} // namespace Player
} // namespace RFOnline
