/**
 * @file j_RewardRaceWarPvpCashCPlayerQEAAXXZ_1400060D7_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_RewardRaceWarPvpCashCPlayerQEAAXXZ_1400060D7
 * @note Address: 0x1400060D7
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?RewardRaceWarPvpCash@CPlayer@@QEAAXXZ
 *Address: 0x1400060D7
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::RewardRaceWarPvpCash(CPlayer *this)
{
  CPlayer::RewardRaceWarPvpCash(this);
}



} // namespace Player
} // namespace RFOnline
