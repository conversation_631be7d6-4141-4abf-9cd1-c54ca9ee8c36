/**
 * @file j_pc_TrunkPwHintIndexRequestCPlayerQEAAXXZ_14000615E_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_TrunkPwHintIndexRequestCPlayerQEAAXXZ_14000615E
 * @note Address: 0x14000615E
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_TrunkPwHintIndexRequest@CPlayer@@QEAAXXZ
 *Address: 0x14000615E
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_TrunkPwHintIndexRequest(CPlayer *this)
{
  CPlayer::pc_TrunkPwHintIndexRequest(this);
}



} // namespace Player
} // namespace RFOnline
