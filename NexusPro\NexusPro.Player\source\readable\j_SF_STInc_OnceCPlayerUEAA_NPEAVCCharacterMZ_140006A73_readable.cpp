/**
 * @file j_SF_STInc_OnceCPlayerUEAA_NPEAVCCharacterMZ_140006A73_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_SF_STInc_OnceCPlayerUEAA_NPEAVCCharacterMZ_140006A73
 * @note Address: 0x140006A73
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?SF_STInc_Once@CPlayer@@UEAA_NPEAVCCharacter@@M@Z
 *Address: 0x140006A73
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


bool __fastcall CPlayer::SF_STInc_Once(CPlayer *this, CCharacter *pDstObj, float fEffectValue)
{
  return CPlayer::SF_STInc_Once(this, pDstObj, fEffectValue);
}



} // namespace Player
} // namespace RFOnline
