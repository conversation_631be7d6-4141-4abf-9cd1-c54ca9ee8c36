/**
 * @file j_pc_TakeGroundingItemCPlayerQEAAXPEAVCItemBoxGZ_140007CF7_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_TakeGroundingItemCPlayerQEAAXPEAVCItemBoxGZ_140007CF7
 * @note Address: 0x140007CF7
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_TakeGroundingItem@CPlayer@@QEAAXPEAVCItemBox@@G@Z
 *Address: 0x140007CF7
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_TakeGroundingItem(CPlayer *this, CItemBox *pBox, unsigned __int16 wAddSerial)
{
  CPlayer::pc_TakeGroundingItem(this, pBox, wAddSerial);
}



} // namespace Player
} // namespace RFOnline
