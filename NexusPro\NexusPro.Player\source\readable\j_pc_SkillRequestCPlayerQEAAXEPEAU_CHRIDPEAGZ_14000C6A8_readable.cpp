/**
 * @file j_pc_SkillRequestCPlayerQEAAXEPEAU_CHRIDPEAGZ_14000C6A8_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_SkillRequestCPlayerQEAAXEPEAU_CHRIDPEAGZ_14000C6A8
 * @note Address: 0x14000C6A8
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_SkillRequest@CPlayer@@QEAAXEPEAU_CHRID@@PEAG@Z
 *Address: 0x14000C6A8
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_SkillRequest(CPlayer *this, char bySkillIndex, _CHRID *pidDst, unsigned __int16 *pConsumeSerial)
{
  CPlayer::pc_SkillRequest(this, bySkillIndex, pidDst, pConsumeSerial);
}



} // namespace Player
} // namespace RFOnline
