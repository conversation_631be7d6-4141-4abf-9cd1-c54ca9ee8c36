/**
 * @file j_pc_GuildPushMoneyRequestCPlayerQEAAXKKZ_14000F15F_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_GuildPushMoneyRequestCPlayerQEAAXKKZ_14000F15F
 * @note Address: 0x14000F15F
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_GuildPushMoneyRequest@CPlayer@@QEAAXKK@Z
 *Address: 0x14000F15F
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_GuildPushMoneyRequest(CPlayer *this, unsigned int dwPushDalant, unsigned int dwPushGold)
{
  CPlayer::pc_GuildPushMoneyRequest(this, dwPushDalant, dwPushGold);
}



} // namespace Player
} // namespace RFOnline
