/**
 * @file j_pc_GuildJoinAcceptRequestCPlayerQEAAXK_NZ_1400116E4_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_GuildJoinAcceptRequestCPlayerQEAAXK_NZ_1400116E4
 * @note Address: 0x1400116E4
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_GuildJoinAcceptRequest@CPlayer@@QEAAXK_N@Z
 *Address: 0x1400116E4
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_GuildJoinAcceptRequest(CPlayer *this, unsigned int dwApplierSerial, bool bAccept)
{
  CPlayer::pc_GuildJoinAcceptRequest(this, dwApplierSerial, bAccept);
}



} // namespace Player
} // namespace RFOnline
