/**
 * @file j_SendMsg_AdjustAmountInformCPlayerQEAAXEGKZ_1400122D8_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_SendMsg_AdjustAmountInformCPlayerQEAAXEGKZ_1400122D8
 * @note Address: 0x1400122D8
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?SendMsg_AdjustAmountInform@CPlayer@@QEAAXEGK@Z
 *Address: 0x1400122D8
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::SendMsg_AdjustAmountInform(CPlayer *this, char byStorageCode, unsigned __int16 wSerial, unsigned int dwDur)
{
  CPlayer::SendMsg_AdjustAmountInform(this, byStorageCode, wSerial, dwDur);
}



} // namespace Player
} // namespace RFOnline
