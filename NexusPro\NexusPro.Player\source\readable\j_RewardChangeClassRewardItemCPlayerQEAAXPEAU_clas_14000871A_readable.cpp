/**
 * @file j_RewardChangeClassRewardItemCPlayerQEAAXPEAU_clas_14000871A_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_RewardChangeClassRewardItemCPlayerQEAAXPEAU_clas_14000871A
 * @note Address: 0x14000871A
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?RewardChangeClassRewardItem@CPlayer@@QEAAXPEAU_class_fld@@E@Z
 *Address: 0x14000871A
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::RewardChangeClassRewardItem(CPlayer *this, _class_fld *pClassFld, char bySelectRewardItem)
{
  CPlayer::RewardChangeClassRewardItem(this, pClassFld, bySelectRewardItem);
}



} // namespace Player
} // namespace RFOnline
