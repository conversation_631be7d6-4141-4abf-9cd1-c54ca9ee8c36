﻿/*
 *Function: _std::_Uninit_copy_std::_Vector_const_iterator_unsigned_long_std::allocator_unsigned_long____unsigned_long_____ptr64_std::allocator_unsigned_long____::_1_::catch$0
 *Address: 0x14039C470
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// Additional STL includes for compilation
#include <iterator>
#include <memory>



void __fastcall __noreturn std::_Uninit_copy_std::_Vector_const_iterator_unsigned_long_std::allocator_unsigned_long____unsigned_long_____ptr64_std::allocator_unsigned_long____::_1_::catch_0(__int64 a1, __int64 a2)
{
  __int64 i; // rbp@1

  for(i = a2; *((Q WO RD*)i + 32) != *((QWORD*)i + 112); *((QWORD*)i + 32) += 4i64 )
    typename std::allocator<unsigned long>::destroy(*(std::allocator<unsigned long> **)(i + 120), *(unsigned int **)(i + 32));
  CxxThrowException_0(0i64, 0i64);
}



