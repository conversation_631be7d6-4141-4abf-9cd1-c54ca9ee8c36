/**
 * @file j_pc_UnitPackFillRequestCPlayerQEAAXEEPEAU__list_u_14000D39B_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_UnitPackFillRequestCPlayerQEAAXEEPEAU__list_u_14000D39B
 * @note Address: 0x14000D39B
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_UnitPackFillRequest@CPlayer@@QEAAXEEPEAU__list@_unit_pack_fill_request_clzo@@H@Z
 *Address: 0x14000D39B
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_UnitPackFillRequest(CPlayer *this, char bySlotIndex, char by<PERSON><PERSON><PERSON><PERSON>, _unit_pack_fill_request_clzo::__list *pList, int bUseNPCLinkIntem)
{
  CPlayer::pc_UnitPackFillRequest(this, bySlotIndex, by<PERSON>ill<PERSON><PERSON>, pList, bUseNPCLinkIntem);
}



} // namespace Player
} // namespace RFOnline
