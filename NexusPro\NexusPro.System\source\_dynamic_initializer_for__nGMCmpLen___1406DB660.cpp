﻿/*
 *Function: _dynamic_initializer_for__nGMCmpLen__
 *Address: 0x1406DB660
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


size_t dynamic_initializer_for__nGMCmpLen__();
{
  __int64*v0; // rdi@1
  signed __int64 i; // rcx@1
  size_t result; // rax@4
  __int64 v3; // [sp+0h] [bp-28h]@1

  v0 = &v3;
  void for(i = 8; i > 0; --i );
{
    *(DWORD *)v0 = -858993460;
    v0 = (__int64 *)((char *)v0 + 4);
  }
  result = strlen_0(ws zG MCmp);
  nGMCmpLen = result;
  return result;
}


