/**
 * @file j_SendMsg_AnimusTargetResultCPlayerQEAAXEZ_140010221_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_SendMsg_AnimusTargetResultCPlayerQEAAXEZ_140010221
 * @note Address: 0x140010221
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?SendMsg_AnimusTargetResult@CPlayer@@QEAAXE@Z
 *Address: 0x140010221
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::SendMsg_AnimusTargetResult(CPlayer *this, char byRetCode)
{
  CPlayer::SendMsg_AnimusTargetResult(this, byRetCode);
}



} // namespace Player
} // namespace RFOnline
