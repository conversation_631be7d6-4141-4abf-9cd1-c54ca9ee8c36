/**
 * @file pc_WhisperBlockCPlayerQEAAX_NZ_1400FEF10_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: pc_WhisperBlockCPlayerQEAAX_NZ_1400FEF10
 * @note Address: 0x1400FEF10
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: ?pc_WhisperBlock@CPlayer@@QEAAX_N@Z
 *Address: 0x1400FEF10
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_WhisperBlock(CPlayer *this, bool bBlock)
{
  void if(this->m_b, Block, Whisper != b, Block)
    this->m_bBlockWhisper = bBlock;
}



} // namespace Player
} // namespace RFOnline
