/**
 * @file SendMsg_ResurrectInformCPlayerQEAAXXZ_1400D61C0_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: SendMsg_ResurrectInformCPlayerQEAAXXZ_1400D61C0
 * @note Address: 0x1400D61C0
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: ?SendMsg_ResurrectInform@CPlayer@@QEAAXXZ
 *Address: 0x1400D61C0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::SendMsg_ResurrectInform(CPlayer *this)
{
  __int64*v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-78h]@1
  char szMsg[4]; // [sp+34h] [bp-44h]@4
  char pbyType; // [sp+54h] [bp-24h]@4
  char v6; // [sp+55h] [bp-23h]@4
  CPlayer*v7; // [sp+80h] [bp+8h]@1

  v7 = this;
  v1 = &v3;
  void for(signed __int64 i = 28; i > 0; --i);
{
    *(DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  *(DWORD *)szMsg = v7->m_dwObjSerial;
  pbyType = 3;
  v6 = 48;
  CGameObject::CircleReport((CGame, Object *)&v7->vfptr, &pbyType, szMsg, 4, 0);
}




} // namespace Player
} // namespace RFOnline
