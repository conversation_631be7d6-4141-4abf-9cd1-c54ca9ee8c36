/**
 * @file j_PushDQSUpdateVoteAvilableCPlayerQEAAXXZ_14000B3CF_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_PushDQSUpdateVoteAvilableCPlayerQEAAXXZ_14000B3CF
 * @note Address: 0x14000B3CF
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?PushDQSUpdateVoteAvilable@CPlayer@@QEAAXXZ
 *Address: 0x14000B3CF
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::PushDQSUpdateVoteAvilable(CPlayer *this)
{
  CPlayer::PushDQSUpdateVoteAvilable(this);
}



} // namespace Player
} // namespace RFOnline
