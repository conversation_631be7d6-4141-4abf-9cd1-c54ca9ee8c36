/**
 * @file j_pc_ChatRePresentationRequestCPlayerQEAAXPEADZ_140004D68_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_ChatRePresentationRequestCPlayerQEAAXPEADZ_140004D68
 * @note Address: 0x140004D68
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_ChatRePresentationRequest@CPlayer@@QEAAXPEAD@Z
 *Address: 0x140004D68
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_ChatRePresentationRequest(CPlayer *this, char *pwszChatData)
{
  CPlayer::pc_ChatRePresentationRequest(this, pwszChatData);
}



} // namespace Player
} // namespace RFOnline
