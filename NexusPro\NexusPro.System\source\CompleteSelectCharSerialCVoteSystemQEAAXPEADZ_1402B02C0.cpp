﻿/*
 *Function: ?CompleteSelectCharSerial@CVoteSystem@@QEAAXPEAD@Z
 *Address: 0x1402B02C0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CVoteSystem::CompleteSelectCharSerial(CVoteSystem *this, char *pData)
{
  __int64*v2; // rdi@1
  signed __int64 i; // rcx@1
  CPvpUserAndGuildRankingSystem*v4; // rax@4
  unsigned int v5; // eax@4
  __int64 v6; // [sp+0h] [bp-48h]@1
  char*pwszName; // [sp+20h] [bp-28h]@5
  unsigned int dwSerial; // [sp+28h] [bp-20h]@5
  char*v9; // [sp+30h] [bp-18h]@4
  CPlayer*v10; // [sp+38h] [bp-10h]@4
  CVoteSystem*v11; // [sp+50h] [bp+8h]@1

  v11 = this;
  v2 = &v6;
  void for(i = 16; i > 0; --i );
{
    *(DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v9 = pData;
  v4 = CPvpUserAndGuildRankingSystem::Instance();
  v5 = CPvpUserAndGuildRankingSystem::GetCurrentRaceBossSerial(v4, *v9, 0);
  v10 = GetPtrPlayerFromSerial(&g_Player, 2532, v5);
  if(*((D WO RD *)v9 + 1) == -1 )
  {
    void if(v10 )
      CPlayer::SendMsg_ProposeVoteResult(v10, 8);
  }
  else
  {
    dwSerial = *((DWORD *)v9 + 1);
    pwszName = v9 + 8;
    if ( !CVoteSystem::StartVote(v11, *v9, v9[1], v9 + 25, v9 + 8, dwSerial) )
    {
      void if(v10 )
        CPlayer::SendMsg_ProposeVoteResult(v10, 7);
    }
  }
}


