/**
 * @file j_SearchMoveTargetCHolyKeeperQEAAPEAVCPlayerXZ_140008788_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_SearchMoveTargetCHolyKeeperQEAAPEAVCPlayerXZ_140008788
 * @note Address: 0x140008788
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?SearchMoveTarget@CHolyKeeper@@QEAAPEAVCPlayer@@XZ
 *Address: 0x140008788
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


CPlayer *__fastcall CHolyKeeper::SearchMoveTarget(CHoly, Keeper *this)
{
  return CHolyKeeper::SearchMoveTarget(this);
}



} // namespace Player
} // namespace RFOnline
