/**
 * @file j_pc_GuildJoinApplyCancelRequestCPlayerQEAAXXZ_14000C9F5_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_GuildJoinApplyCancelRequestCPlayerQEAAXXZ_14000C9F5
 * @note Address: 0x14000C9F5
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_GuildJoinApplyCancelRequest@CPlayer@@QEAAXXZ
 *Address: 0x14000C9F5
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_GuildJoinApplyCancelRequest(CPlayer *this)
{
  CPlayer::pc_GuildJoinApplyCancelRequest(this);
}



} // namespace Player
} // namespace RFOnline
