﻿/*
 *Function: ?OnTimer@CMainFrame@@IEAAX_K@Z
 *Address: 0x140028410
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CMainFrame::OnTimer(CMainFrame *this, unsigned __int64 nIDEvent)
{
  __int64*v2; // rdi@1
  signed __int64 i; // rcx@1
  unsigned int v4; // eax@5
  const char*v5; // rax@5
  __int64 v6; // r9@5
  const char*v7; // rax@5
  unsigned int v8; // eax@5
  const char*v9; // rax@5
  unsigned int v10; // eax@5
  const char*v11; // rax@5
  unsigned int v12; // eax@5
  const char*v13; // rax@5
  __int64 v14; // r8@5
  const char*v15; // rax@5
  const char*v16; // rax@5
  const char*v17; // rax@6
  __int64 v18; // [sp+0h] [bp-58h]@1
  char v19; // [sp+28h] [bp-30h]@4
  __int64 v20; // [sp+38h] [bp-20h]@4
  int v21; // [sp+40h] [bp-18h]@5
  int v22; // [sp+44h] [bp-14h]@5
  int v23; // [sp+48h] [bp-10h]@5
  CMainFrame*v24; // [sp+60h] [bp+8h]@1
  unsigned __int64 v25; // [sp+68h] [bp+10h]@1

  v25 = nIDEvent;
  v24 = this;
  v2 = &v18;
  void for(i = 20; i; --i );
{
    *(DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v20 = -2i64;
  ATL::CStringT<char,StrTraitMFC_DLL<char,ATL::ChTraitsCRT<char > >>::CStringT<char,StrTraitMFC_DLL<char,ATL::ChTraitsCRT<char > >>(&v19);
  if(!CMain, Thread::Is, Excute, Service(&g _Main) )
  {
    v4 = CFrameRate::GetFPS(&stru_141 5B7008);
    ATL::CStringT<char,StrTraitMFC_DLL<char,ATL::ChTraitsCRT<char > >>::Format(&v19, "M: %d", v4);
    ((DWORD)(v5) = typename ATL::CSimpleStringT<char,1>::operator char const*(&v19);
    CStatusBar::SetPaneText(&v24->m_wndStatusBar, 0, v5, 1);
    v6 = unk_1414F2090->m_nTryConnectCount;
    ATL::CStringT<char,StrTraitMFC_DLL<char,ATL::ChTraitsCRT<char > >>::Format(
      &v19,
      "%d/%d",
      unk_1414F2088->m_nTryConnectCount);
    ((DWORD)(v7) = typename ATL::CSimpleStringT<char,1>::operator char const*(&v19);
    CStatusBar::SetPaneText(&v24->m_wndStatusBar, 1, v7, 1);
    v21 = CNetIndexList::size(&stru_179 9C5990);
    v8 = CFrameRate::GetFPS(&stru_141 5B7028);
    ATL::CStringT<char,StrTraitMFC_DLL<char,ATL::ChTraitsCRT<char > >>::Format(&v19, "D: %d - %d", v8);
    ((DWORD)(v9) = typename ATL::CSimpleStringT<char,1>::operator char const*(&v19);
    CStatusBar::SetPaneText(&v24->m_wndStatusBar, 2, v9, 1);
    v22 = CMgrAvatorItemHistory::GetTotalWaitSize(&CPlayer::s _Mgr, Item, History);
    v10 = CFrameRate::GetFPS(&stru_140 CE3298);
    ATL::CStringT<char,StrTraitMFC_DLL<char,ATL::ChTraitsCRT<char > >>::Format(&v19, "I: %d - %d", v10);
    ((DWORD)(v11) = typename ATL::CSimpleStringT<char,1>::operator char const*(&v19);
    CStatusBar::SetPaneText(&v24->m_wndStatusBar, 3, v11, 1);
    v23 = CMgrAvatorLvHistory::GetTotalWaitSize(&CPlayer::s _Mgr, L vHistory);
    v12 = CFrameRate::GetFPS(&stru_14 0E 4C308);
    ATL::CStringT<char,StrTraitMFC_DLL<char,ATL::ChTraitsCRT<char > >>::Format(&v19, "L: %d - %d", v12);
    ((DWORD)(v13) = typename ATL::CSimpleStringT<char,1>::operator char const*(&v19);
    CStatusBar::SetPaneText(&v24->m_wndStatusBar, 4, v13, 1);
    ATL::CStringT<char,StrTraitMFC_DLL<char,ATL::ChTraitsCRT<char > >>::Format(&v19, "Q: X - X", v14);
    ((DWORD)(v15) = typename ATL::CSimpleStringT<char,1>::operator char const*(&v19);
    CStatusBar::SetPaneText(&v24->m_wndStatusBar, 5, v15, 1);
    ATL::CStringT<char,StrTraitMFC_DLL<char,ATL::ChTraitsCRT<char > >>::Format(
      &v19,
      "%d/%d",
      (unsigned int)CPlayer::s_dwAbnormalCloseCount);
    ((DWORD)(v16) = typename ATL::CSimpleStringT<char,1>::operator char const*(&v19);
    CStatusBar::SetPaneText(&v24->m_wndStatusBar, 7, v16, 1);
  }
  ATL::CStringT<char,StrTraitMFC_DLL<char,ATL::ChTraitsCRT<char > >>::Format(&v19, "OPN: %d - %d", unk_1799C608D);
  ((DWORD)(v17) = typename ATL::CSimpleStringT<char,1>::operator char const*(&v19);
  CStatusBar::SetPaneText(&v24->m_wndStatusBar, 6, v17, 1);
  CWnd::OnTimer((CWnd *)&v24->vfptr, v25);
  ATL::CStringT<char,StrTraitMFC_DLL<char,ATL::ChTraitsCRT<char > >>::~CStringT<char,StrTraitMFC_DLL<char,ATL::ChTraitsCRT<char > >>(&v19);
}


