/**
 * @file j_pc_PostDeleteRequestCPlayerQEAAXKZ_14000F3B2_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_PostDeleteRequestCPlayerQEAAXKZ_14000F3B2
 * @note Address: 0x14000F3B2
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_PostDeleteRequest@CPlayer@@QEAAXK@Z
 *Address: 0x14000F3B2
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_PostDeleteRequest(CPlayer *this, unsigned int dwIndex)
{
  CPlayer::pc_PostDeleteRequest(this, dwIndex);
}



} // namespace Player
} // namespace RFOnline
