/**
 * @file j_pc_PartyJoinInvitationCPlayerQEAAXGZ_140011775_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_PartyJoinInvitationCPlayerQEAAXGZ_140011775
 * @note Address: 0x140011775
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_PartyJoinInvitation@CPlayer@@QEAAXG@Z
 *Address: 0x140011775
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_PartyJoinInvitation(CPlayer *this, unsigned __int16 wDstIndex)
{
  CPlayer::pc_PartyJoinInvitation(this, wDstIndex);
}



} // namespace Player
} // namespace RFOnline
