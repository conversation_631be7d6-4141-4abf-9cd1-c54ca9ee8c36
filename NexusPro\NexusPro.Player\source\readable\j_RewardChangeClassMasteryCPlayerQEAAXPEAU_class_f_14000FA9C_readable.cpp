/**
 * @file j_RewardChangeClassMasteryCPlayerQEAAXPEAU_class_f_14000FA9C_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_RewardChangeClassMasteryCPlayerQEAAXPEAU_class_f_14000FA9C
 * @note Address: 0x14000FA9C
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?RewardChangeClassMastery@CPlayer@@QEAAXPEAU_class_fld@@@Z
 *Address: 0x14000FA9C
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::RewardChangeClassMastery(CPlayer *this, _class_fld *pClassFld)
{
  CPlayer::RewardChangeClassMastery(this, pClassFld);
}



} // namespace Player
} // namespace RFOnline
