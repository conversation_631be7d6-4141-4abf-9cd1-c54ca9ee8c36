/**
 * @file j_SendMsg_AnimusRecallWaitTimeFreeCPlayerQEAAX_NZ_140006069_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_SendMsg_AnimusRecallWaitTimeFreeCPlayerQEAAX_NZ_140006069
 * @note Address: 0x140006069
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?SendMsg_AnimusRecallWaitTimeFree@CPlayer@@QEAAX_N@Z
 *Address: 0x140006069
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::SendMsg_AnimusRecallWaitTimeFree(CPlayer *this, bool bFree)
{
  CPlayer::SendMsg_AnimusRecallWaitTimeFree(this, bFree);
}



} // namespace Player
} // namespace RFOnline
