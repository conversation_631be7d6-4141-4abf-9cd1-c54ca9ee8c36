/**
 * @file j_pc_TrunkIoMergeRequestCPlayerQEAAXEEGGGZ_14000C603_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_TrunkIoMergeRequestCPlayerQEAAXEEGGGZ_14000C603
 * @note Address: 0x14000C603
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_TrunkIoMergeRequest@CPlayer@@QEAAXEEGGG@Z
 *Address: 0x14000C603
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_TrunkIoMergeRequest(CPlayer *this, char byStartStorageIndex, char byTarStorageIndex, unsigned __int16 wStartItemSerial, unsigned __int16 wTarItemSerial, unsigned __int16 wMoveAmount)
{
  CPlayer::pc_TrunkIoMergeRequest(
    this,
    byStartStorageIndex,
    byTarStorageIndex,
    wStartItemSerial,
    wTarItemSerial,
    wMoveAmount);
}



} // namespace Player
} // namespace RFOnline
