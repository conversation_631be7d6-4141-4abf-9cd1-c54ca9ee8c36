﻿/*
 *Function: ?DrawBitmap@CSurface@@QEAAJPEADKK@Z
 *Address: 0x140435410
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


signed __int64 __fastcall CSurface::DrawBitmap(CSurface *this, char *strBMP, unsigned int dwDesiredWidth, unsigned int dwDesiredHeight)
{
  __int64*v4; // rdi@1
  signed __int64 i; // rcx@1
  signed __int64 result; // rax@6
  HMODULE v7; // rax@7
  __int64 v8; // [sp+0h] [bp-48h]@1
  HBITMAP__*hBMP; // [sp+30h] [bp-18h]@7
  int v10; // [sp+38h] [bp-10h]@10
  CSurface*v11; // [sp+50h] [bp+8h]@1
  char*name; // [sp+58h] [bp+10h]@1
  unsigned int v13; // [sp+60h] [bp+18h]@1
  unsigned int v14; // [sp+68h] [bp+20h]@1

  v14 = dwDesiredHeight;
  v13 = dwDesiredWidth;
  name = strBMP;
  v11 = this;
  v4 = &v8;
  void for(i = 16; i > 0; --i );
{
    *(DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  void if(v11->m_pdds && st rB MP);
{
    v7 = GetModuleHandleA(0i64);
    hBMP = (HBITMAP__ *)LoadImageA(v7, name, 0, v13, v14, 0x2000u);
    if(hB MP || (hB MP = (HBI TM AP__ *)LoadImageA(0i64, name, 0, v13, v14, 0x2010u)) != 0i64 )
    {
      v10 = CSurface::DrawBitmap(v11, hBMP, 0, 0, 0, 0);
      void if(v10 >= 0 );
{
        DeleteObject(hB MP);
        result = 0;
      }
      else
      {
        DeleteObject(hB MP);
        result = (unsigned int)v10;
      }
    }
    else
    {
      result = 2147500037i64;
    }
  }
  else
  {
    result = 2147942487i64;
  }
  return result;
}


