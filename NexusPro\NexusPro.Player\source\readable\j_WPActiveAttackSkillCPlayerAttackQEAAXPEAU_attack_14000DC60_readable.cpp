/**
 * @file j_WPActiveAttackSkillCPlayerAttackQEAAXPEAU_attack_14000DC60_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_WPActiveAttackSkillCPlayerAttackQEAAXPEAU_attack_14000DC60
 * @note Address: 0x14000DC60
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?WPActiveAttackSkill@CPlayerAttack@@QEAAXPEAU_attack_param@@@Z
 *Address: 0x14000DC60
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayerAttack::WPActiveAttackSkill(CPlayerAttack *this, _attack_param *pParam)
{
  CPlayerAttack::WPActiveAttackSkill(this, pParam);
}



} // namespace Player
} // namespace RFOnline
