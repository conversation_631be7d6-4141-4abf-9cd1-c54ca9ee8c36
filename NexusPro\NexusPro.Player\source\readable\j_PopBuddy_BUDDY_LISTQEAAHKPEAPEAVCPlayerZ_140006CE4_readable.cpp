/**
 * @file j_PopBuddy_BUDDY_LISTQEAAHKPEAPEAVCPlayerZ_140006CE4_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_PopBuddy_BUDDY_LISTQEAAHKPEAPEAVCPlayerZ_140006CE4
 * @note Address: 0x140006CE4
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?PopBuddy@_BUDDY_LIST@@QEAAHKPEAPEAVCPlayer@@@Z
 *Address: 0x140006CE4
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


int __fastcall _BUDDY_LIST::PopBuddy(_BUDDY_LIST *this, unsigned int dwSerial, CPlayer **ppPoper)
{
  return _BUDDY_LIST::PopBuddy(this, dwSerial, ppPoper);
}



} // namespace Player
} // namespace RFOnline
