/**
 * @file j_pc_MoveNextCPlayerQEAAXEPEAM0EZ_14000782E_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_MoveNextCPlayerQEAAXEPEAM0EZ_14000782E
 * @note Address: 0x14000782E
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_MoveNext@CPlayer@@QEAAXEPEAM0E@Z
 *Address: 0x14000782E
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_MoveNext(CPlayer *this, char byMoveType, float *pfCur, float *pfTar, char byDirect)
{
  CPlayer::pc_MoveNext(this, byMoveType, pfCur, pfTar, byDirect);
}



} // namespace Player
} // namespace RFOnline
