/**
 * @file j_RobbedHPCGameObjectUEAA_NPEAVCCharacterHZ_140004C96_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_RobbedHPCGameObjectUEAA_NPEAVCCharacterHZ_140004C96
 * @note Address: 0x140004C96
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?RobbedHP@CGameObject@@UEAA_NPEAVCCharacter@@H@Z
 *Address: 0x140004C96
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


bool __fastcall CGameObject::RobbedHP(CGameObject *this, CCharacter *pDst, int nDecHP)
{
  return CGameObject::RobbedHP(this, pDst, nDecHP);
}



} // namespace Player
} // namespace RFOnline
