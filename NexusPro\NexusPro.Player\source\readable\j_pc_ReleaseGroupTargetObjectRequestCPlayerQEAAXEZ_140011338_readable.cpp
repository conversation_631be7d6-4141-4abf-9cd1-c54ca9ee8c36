/**
 * @file j_pc_ReleaseGroupTargetObjectRequestCPlayerQEAAXEZ_140011338_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_ReleaseGroupTargetObjectRequestCPlayerQEAAXEZ_140011338
 * @note Address: 0x140011338
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_ReleaseGroupTargetObjectRequest@CPlayer@@QEAAXE@Z
 *Address: 0x140011338
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_ReleaseGroupTargetObjectRequest(CPlayer *this, char byGroupType)
{
  CPlayer::pc_ReleaseGroupTargetObjectRequest(this, byGroupType);
}



} // namespace Player
} // namespace RFOnline
