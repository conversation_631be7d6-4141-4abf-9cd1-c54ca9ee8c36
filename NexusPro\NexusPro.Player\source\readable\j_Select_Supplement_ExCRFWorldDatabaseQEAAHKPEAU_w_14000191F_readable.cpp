/**
 * @file j_Select_Supplement_ExCRFWorldDatabaseQEAAHKPEAU_w_14000191F_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_Select_Supplement_ExCRFWorldDatabaseQEAAHKPEAU_w_14000191F
 * @note Address: 0x14000191F
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?Select_Supplement_Ex@CRFWorldDatabase@@QEAAHKPEAU_worlddb_character_supplement_info@@@Z
 *Address: 0x14000191F
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


int __fastcall CRFWorldDatabase::Select_Supplement_Ex(CRFWorldDatabase *this, unsigned int dwSerial, _worlddb_character_supplement_info *pSupplement)
{
  return CRFWorldDatabase::Select_Supplement_Ex(this, dwSerial, pSupplement);
}



} // namespace Player
} // namespace RFOnline
