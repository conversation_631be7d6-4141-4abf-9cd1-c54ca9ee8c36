/**
 * @file j_SendEnterResultCReturnGateControllerIEAAXHPEAVCP_140006FCD_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_SendEnterResultCReturnGateControllerIEAAXHPEAVCP_140006FCD
 * @note Address: 0x140006FCD
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?SendEnterResult@CReturnGateController@@IEAAXHPEAVCPlayer@@@Z
 *Address: 0x140006FCD
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CReturnGateController::SendEnterResult(CReturnGateController *this, int iResult, CPlayer *pkObj)
{
  CReturnGateController::SendEnterResult(this, iResult, pkObj);
}



} // namespace Player
} // namespace RFOnline
