/**
 * @file size_qry_case_load_guildbattle_totalrecordQEAAHXZ_140207590_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: size_qry_case_load_guildbattle_totalrecordQEAAHXZ_140207590
 * @note Address: 0x140207590
 */

#include <cstdint>

namespace RFOnline {
namespace Combat {

﻿/*
 *Function: ?size@_qry_case_load_guildbattle_totalrecord@@QEAAHXZ
 *Address: 0x140207590
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


signed __int64 __fastcall _qry_case_load_guildbattle_totalrecord::size(_qry_case_load_guildbattle_totalrecord *this)
{
  return 16i64;
}



} // namespace Combat
} // namespace RFOnline
