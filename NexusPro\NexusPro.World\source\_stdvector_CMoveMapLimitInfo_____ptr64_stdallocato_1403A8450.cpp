﻿/*
 *Function: _std::vector_CMoveMapLimitInfo_____ptr64_std::allocator_CMoveMapLimitInfo_____ptr64___::erase_::_1_::dtor$2
 *Address: 0x1403A8450
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"
#include <vector>

// Additional STL includes for compilation
#include <iterator>
#include <memory>



void __fastcall std::vector_CMoveMapLimitInfo_____ptr64_std::allocator_CMoveMapLimitInfo_____ptr64___::erase_::_1_::dtor_2(__int64 a1, __int64 a2)
{
  if(*((D WO RD*)a2 + 40) &1 )
  {
    *((DWORD*)a2 + 40) &= 0xFFFFFFFE;
    std::_Vector_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo * > >::~_Vector_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo * > >(*(std::_Vector_iterator<CMoveMapLimitInfo *,std::allocator<CMoveMapLimitInfo *>  > **)(a2 + 88));
  }
}



