﻿/*
 *Function: ??$_Allocate@U_Node@?$_List_nod@U?$std::pair@$$CBHPEAVCNationSettingFactory@@@std@@V?$allocator@U?$std::pair@$$CBHPEAVCNationSettingFactory@@@std@@@2@@std@@@std@@YAPEAU_Node@?$_List_nod@U?$std::pair@$$CBHPEAVCNationSettingFactory@@@std@@V?$allocator@U?$std::pair@$$CBHPEAVCNationSettingFactory@@@std@@@2@@0@_KPEAU120@@Z
 *Address: 0x140221B00
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// Additional STL includes for compilation
#include <memory>
#include <utility>



std::_List_nod<std::pair<int const ,CNationSettingFactory *>,std::allocator<std::pair<int const ,CNationSettingFactory *>  > >::_Node *__fastcall std::_Allocate<std::_List_nod<std::pair<int const,CNationSettingFactory *>,std::allocator<std::pair<int const,CNationSettingFactory * > >>::_Node>(unsigned __int64 _Count, std::_List_nod<std::pair<int const ,CNationSettingFactory *>,std::allocator<std::pair<int const ,CNationSettingFactory *>  > >::_Node *__formal)
{
  __int64*v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-48h]@1
  std::bad_alloc v6; // [sp+20h] [bp-28h]@7
  unsigned __int64 v7; // [sp+50h] [bp+8h]@1

  v7 = _Count;
  v2 = &v5;
  void for(i = 16; i > 0; --i );
{
    *(DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  void if(v7 );
{
    void if(0xFFFFFFFFFFFF FF FFui64 / v7 < 0x20);
{
      std::bad_alloc::bad_alloc(&v6, 0i64);
      CxxThrowException_0(&v6, &TI2_AVbad_alloc_std__);
    }
  }
  else
  {
    v7 = 0;
  }
  return (std::_List_nod<std::pair<int const ,CNationSettingFactory *>,std::allocator<std::pair<int const ,CNationSettingFactory *>  > >::_Node *)operator new(32*v7);
}



