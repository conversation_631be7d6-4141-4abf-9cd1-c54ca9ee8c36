/**
 * @file j_pc_GuildRoomEnterRequestCPlayerQEAAXPEAU_guildro_140009264_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_GuildRoomEnterRequestCPlayerQEAAXPEAU_guildro_140009264
 * @note Address: 0x140009264
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_GuildRoomEnterRequest@CPlayer@@QEAAXPEAU_guildroom_enter_request_clzo@@@Z
 *Address: 0x140009264
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_GuildRoomEnterRequest(CPlayer *this, _guildroom_enter_request_clzo *pProtocol)
{
  CPlayer::pc_GuildRoomEnterRequest(this, pProtocol);
}



} // namespace Player
} // namespace RFOnline
