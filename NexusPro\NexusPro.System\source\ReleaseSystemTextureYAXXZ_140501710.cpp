﻿/*
 *Function: ?ReleaseSystemTexture@@YAXXZ
 *Address: 0x140501710
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void ReleaseSystemTexture(void);
{
  void if(qword_18 4A7 9C20);
{
    (*(void (**)(void))(*(QWORD *)qword_184A79C20 + 16i64))();
    qword_184A79C20 = 0;
  }
  void if(qword_18 4A7 9C18);
{
    (*(void (**)(void))(*(QWORD *)qword_184A79C18 + 16i64))();
    qword_184A79C18 = 0;
  }
}


