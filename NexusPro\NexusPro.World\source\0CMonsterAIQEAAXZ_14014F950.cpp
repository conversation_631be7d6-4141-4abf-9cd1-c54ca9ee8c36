﻿/*
 *Function: ??0CMonsterAI@@QEAA@XZ
 *Address: 0x14014F950
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"
#include <vector>


void __fastcall CMonsterAI::CMonsterAI(CMonster AI *this)
{
  __int64*v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  __int64 v4; // [sp+20h] [bp-18h]@4
  CMonsterAI*v5; // [sp+40h] [bp+8h]@1

  v5 = this;
  v1 = &v3;
  void for(i = 12; i > 0; --i );
{
    *(DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v4 = -2i64;
  Us_HFSM::Us_HFSM((Us_ HF SM *)&v5->vfptr);
  v5->vfptr = (Us_HFSMVtbl *)&CMonsterAI::`vftable';
  `std::vector constructor iterator'(v5->m_SFCheckTime, 8ui64, 4, (void *((__cdecl*)void *))SF_Timer::SF_Timer);
  CPathMgr::CPathMgr(&v5->m _Path, Finder);
  v5->m_pAsistMonster = 0;
  v5->m_nCurPathFindFailCount = 0;
}


