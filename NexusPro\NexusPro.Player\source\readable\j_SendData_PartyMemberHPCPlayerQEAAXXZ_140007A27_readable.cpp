/**
 * @file j_SendData_PartyMemberHPCPlayerQEAAXXZ_140007A27_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_SendData_PartyMemberHPCPlayerQEAAXXZ_140007A27
 * @note Address: 0x140007A27
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?SendData_PartyMemberHP@CPlayer@@QEAAXXZ
 *Address: 0x140007A27
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::SendData_PartyMemberHP(CPlayer *this)
{
  CPlayer::SendData_PartyMemberHP(this);
}



} // namespace Player
} // namespace RFOnline
