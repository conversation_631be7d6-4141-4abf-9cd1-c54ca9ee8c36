/**
 * @file j_mgr_tracingCPlayerQEAA_N_NZ_14000CC2F_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_mgr_tracingCPlayerQEAA_N_NZ_14000CC2F
 * @note Address: 0x14000CC2F
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?mgr_tracing@CPlayer@@QEAA_N_N@Z
 *Address: 0x14000CC2F
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


bool __fastcall CPlayer::mgr_tracing(CPlayer *this, bool bOn)
{
  return CPlayer::mgr_tracing(this, bOn);
}



} // namespace Player
} // namespace RFOnline
