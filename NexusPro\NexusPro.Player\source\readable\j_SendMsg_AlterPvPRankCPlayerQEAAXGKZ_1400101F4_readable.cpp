/**
 * @file j_SendMsg_AlterPvPRankCPlayerQEAAXGKZ_1400101F4_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_SendMsg_AlterPvPRankCPlayerQEAAXGKZ_1400101F4
 * @note Address: 0x1400101F4
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?SendMsg_AlterPvPRank@CPlayer@@QEAAXGK@Z
 *Address: 0x1400101F4
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::SendMsg_AlterPvPRank(CPlayer *this, unsigned __int16 wPvpRate, unsigned int dwPvpRank)
{
  CPlayer::SendMsg_AlterPvPRank(this, wPvpRate, dwPvpRank);
}



} // namespace Player
} // namespace RFOnline
