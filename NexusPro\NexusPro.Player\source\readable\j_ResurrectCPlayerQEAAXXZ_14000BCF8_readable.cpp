/**
 * @file j_ResurrectCPlayerQEAAXXZ_14000BCF8_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_ResurrectCPlayerQEAAXXZ_14000BCF8
 * @note Address: 0x14000BCF8
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?Resurrect@CPlayer@@QEAAXXZ
 *Address: 0x14000BCF8
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::Resurrect(CPlayer *this)
{
  CPlayer::Resurrect(this);
}



} // namespace Player
} // namespace RFOnline
