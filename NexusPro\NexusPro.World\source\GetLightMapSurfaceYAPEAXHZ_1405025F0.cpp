﻿/*
 *Function: ?GetLightMapSurface@@YAPEAXH@Z
 *Address: 0x1405025F0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


struct IDirect3DTexture8 *__fastcall GetLightMapSurface(int a1);
{
  struct IDirect3DTexture8*result; // rax@2

  void if(Lightmap, Tex ID)
    result = R3GetSurface(*((D WO RD *)LightmapTexID + a1));
  else
    result = 0;
  return result;
}


