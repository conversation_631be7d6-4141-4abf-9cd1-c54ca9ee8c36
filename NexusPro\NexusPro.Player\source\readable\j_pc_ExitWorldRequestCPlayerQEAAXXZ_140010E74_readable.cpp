/**
 * @file j_pc_ExitWorldRequestCPlayerQEAAXXZ_140010E74_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_ExitWorldRequestCPlayerQEAAXXZ_140010E74
 * @note Address: 0x140010E74
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_ExitWorldRequest@CPlayer@@QEAAXXZ
 *Address: 0x140010E74
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_ExitWorldRequest(CPlayer *this)
{
  CPlayer::pc_ExitWorldRequest(this);
}



} // namespace Player
} // namespace RFOnline
