/**
 * @file j_ReCalcMaxHFSPCPlayerQEAAX_N0Z_140009002_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_ReCalcMaxHFSPCPlayerQEAAX_N0Z_140009002
 * @note Address: 0x140009002
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?ReCalcMaxHFSP@CPlayer@@QEAAX_N0@Z
 *Address: 0x140009002
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::ReCalcMaxHFSP(CPlayer *this, bool bSend, bool bRatio)
{
  CPlayer::ReCalcMaxHFSP(this, bSend, bRatio);
}



} // namespace Player
} // namespace RFOnline
