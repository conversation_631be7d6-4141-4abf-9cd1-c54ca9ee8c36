/**
 * @file j_SF_SPDecCPlayerUEAA_NPEAVCCharacterMZ_140009511_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_SF_SPDecCPlayerUEAA_NPEAVCCharacterMZ_140009511
 * @note Address: 0x140009511
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?SF_SPDec@CPlayer@@UEAA_NPEAVCCharacter@@M@Z
 *Address: 0x140009511
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


bool __fastcall CPlayer::SF_SPDec(CPlayer *this, CCharacter *pDstObj, float fEffectValue)
{
  return CPlayer::SF_SPDec(this, pDstObj, fEffectValue);
}



} // namespace Player
} // namespace RFOnline
