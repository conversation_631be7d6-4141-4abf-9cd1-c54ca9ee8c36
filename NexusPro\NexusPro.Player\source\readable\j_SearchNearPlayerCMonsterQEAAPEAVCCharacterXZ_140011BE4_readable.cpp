/**
 * @file j_SearchNearPlayerCMonsterQEAAPEAVCCharacterXZ_140011BE4_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_SearchNearPlayerCMonsterQEAAPEAVCCharacterXZ_140011BE4
 * @note Address: 0x140011BE4
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?SearchNearPlayer@CMonster@@QEAAPEAVCCharacter@@XZ
 *Address: 0x140011BE4
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


CCharacter *__fastcall CMonster::SearchNearPlayer(CMonster *this)
{
  return CMonster::SearchNearPlayer(this);
}



} // namespace Player
} // namespace RFOnline
