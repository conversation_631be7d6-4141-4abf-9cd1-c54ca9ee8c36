/**
 * @file j_pc_PvpCashRecorverCPlayerQEAAXKEZ_1400078BF_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_PvpCashRecorverCPlayerQEAAXKEZ_1400078BF
 * @note Address: 0x1400078BF
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_PvpCashRecorver@CPlayer@@QEAAXKE@Z
 *Address: 0x1400078BF
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_PvpCashRecorver(CPlayer *this, unsigned int dwItemSerial, char byItemCnt)
{
  CPlayer::pc_PvpCashRecorver(this, dwItemSerial, byItemCnt);
}



} // namespace Player
} // namespace RFOnline
