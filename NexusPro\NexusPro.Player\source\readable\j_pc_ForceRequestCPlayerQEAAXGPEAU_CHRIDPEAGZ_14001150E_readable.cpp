/**
 * @file j_pc_ForceRequestCPlayerQEAAXGPEAU_CHRIDPEAGZ_14001150E_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_ForceRequestCPlayerQEAAXGPEAU_CHRIDPEAGZ_14001150E
 * @note Address: 0x14001150E
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_ForceRequest@CPlayer@@QEAAXGPEAU_CHRID@@PEAG@Z
 *Address: 0x14001150E
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_ForceRequest(CPlayer *this, unsigned __int16 wForceSerial, _CHRID *pidDst, unsigned __int16 *pConsumeSerial)
{
  CPlayer::pc_ForceRequest(this, wForceSerial, pidDst, pConsumeSerial);
}



} // namespace Player
} // namespace RFOnline
