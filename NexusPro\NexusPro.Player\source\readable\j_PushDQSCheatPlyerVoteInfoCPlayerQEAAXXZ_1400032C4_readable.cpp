/**
 * @file j_PushDQSCheatPlyerVoteInfoCPlayerQEAAXXZ_1400032C4_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_PushDQSCheatPlyerVoteInfoCPlayerQEAAXXZ_1400032C4
 * @note Address: 0x1400032C4
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?PushDQSCheatPlyerVoteInfo@CPlayer@@QEAAXXZ
 *Address: 0x1400032C4
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::PushDQSCheatPlyerVoteInfo(CPlayer *this)
{
  CPlayer::PushDQSCheatPlyerVoteInfo(this);
}



} // namespace Player
} // namespace RFOnline
