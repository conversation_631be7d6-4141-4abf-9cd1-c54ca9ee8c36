/**
 * @file j_RobbedHPCPlayerUEAA_NPEAVCCharacterHZ_1400053CB_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_RobbedHPCPlayerUEAA_NPEAVCCharacterHZ_1400053CB
 * @note Address: 0x1400053CB
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?Rob<PERSON><PERSON>@CPlayer@@UEAA_NPEAVCCharacter@@H@Z
 *Address: 0x1400053CB
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


bool __fastcall CPlayer::RobbedHP(CPlayer *this, CCharacter *pDst, int nDecHP)
{
  return CPlayer::RobbedHP(this, pDst, nDecHP);
}



} // namespace Player
} // namespace RFOnline
