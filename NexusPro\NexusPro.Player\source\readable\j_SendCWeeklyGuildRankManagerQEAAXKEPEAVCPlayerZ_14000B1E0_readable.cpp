/**
 * @file j_SendCWeeklyGuildRankManagerQEAAXKEPEAVCPlayerZ_14000B1E0_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_SendCWeeklyGuildRankManagerQEAAXKEPEAVCPlayerZ_14000B1E0
 * @note Address: 0x14000B1E0
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?Send@CWeeklyGuildRankManager@@QEAAXKEPEAVCPlayer@@@Z
 *Address: 0x14000B1E0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CWeeklyGuildRankManager::Send(CWeeklyGuildRankManager *this, unsigned int dwVer, char byTabRace, CPlayer *pkPlayer)
{
  CWeeklyGuildRankManager::Send(this, dwVer, byTabRace, pkPlayer);
}



} // namespace Player
} // namespace RFOnline
