/**
 * @file j_pc_RequestUILockCertifyCPlayerQEAAXPEAVCUserDBPE_1400056F5_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_RequestUILockCertifyCPlayerQEAAXPEAVCUserDBPE_1400056F5
 * @note Address: 0x1400056F5
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_RequestUILockCertify@CPlayer@@QEAAXPEAVCUserDB@@PEAD@Z
 *Address: 0x1400056F5
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_RequestUILockCertify(CPlayer *this, CUserDB *pUserDB, char *uszUILockPW)
{
  CPlayer::pc_RequestUILockCertify(this, pUser<PERSON><PERSON>, uszUILockPW);
}



} // namespace Player
} // namespace RFOnline
