/**
 * @file j_SearchNearEnemyCAnimusQEAAPEAVCCharacterXZ_14000F1C3_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_SearchNearEnemyCAnimusQEAAPEAVCCharacterXZ_14000F1C3
 * @note Address: 0x14000F1C3
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?SearchNearEnemy@CAnimus@@QEAAPEAVCCharacter@@XZ
 *Address: 0x14000F1C3
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


CCharacter *__fastcall CAnimus::SearchNearEnemy(CAnimus *this)
{
  return CAnimus::SearchNearEnemy(this);
}



} // namespace Player
} // namespace RFOnline
