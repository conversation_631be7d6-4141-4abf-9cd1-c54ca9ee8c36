/**
 * @file SendMsg_UnitSellResultCPlayerQEAAXEEGHKKKZ_1400DAD40_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: SendMsg_UnitSellResultCPlayerQEAAXEEGHKKKZ_1400DAD40
 * @note Address: 0x1400DAD40
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: ?SendMsg_UnitSellResult@CPlayer@@QEAAXEEGHKKK@Z
 *Address: 0x1400DAD40
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::SendMsg_UnitSellResult(CPlayer *this, char byRetCode, char bySlotIndex, unsigned __int16 wKeySerial, int nAddMoney, unsigned int dwTotalNonpay, unsigned int dwSumDalant, unsigned int dwSumGold)
{
  __int64*v8; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v10; // [sp+0h] [bp-B8h]@1
  char szMsg; // [sp+40h] [bp-78h]@4
  char v12; // [sp+41h] [bp-77h]@4
  unsigned __int16 v13; // [sp+42h] [bp-76h]@4
  unsigned int v14; // [sp+44h] [bp-74h]@4
  int v15; // [sp+48h] [bp-70h]@4
  int v16; // [sp+4Ch] [bp-6Ch]@4
  unsigned int v17; // [sp+64h] [bp-54h]@4
  unsigned int v18; // [sp+68h] [bp-50h]@4
  char pbyType; // [sp+94h] [bp-24h]@4
  char v20; // [sp+95h] [bp-23h]@4
  CPlayer*v21; // [sp+C0h] [bp+8h]@1

  v21 = this;
  v8 = &v10;
  void for(signed __int64 i = 44; i > 0; --i);
{
    *(DWORD *)v8 = -858993460;
    v8 = (__int64 *)((char *)v8 + 4);
  }
  szMsg = byRetCode;
  v12 = bySlotIndex;
  v13 = wKeySerial;
  v14 = dwTotalNonpay;
  v15 = nAddMoney;
  v16 = 0;
  v17 = dwSumDalant;
  v18 = dwSumGold;
  pbyType = 23;
  v20 = 4;
  CNetProcess::LoadSendMsg(unk_1414F2088, v21->m_ObjID.m_wIndex, &pbyType, &szMsg, 0x40u);
}




} // namespace Player
} // namespace RFOnline
