/**
 * @file j_pc_UnitTakeRequestCPlayerQEAAXXZ_14000B019_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_UnitTakeRequestCPlayerQEAAXXZ_14000B019
 * @note Address: 0x14000B019
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_UnitTakeRequest@CPlayer@@QEAAXXZ
 *Address: 0x14000B019
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_UnitTakeRequest(CPlayer *this)
{
  CPlayer::pc_UnitTakeRequest(this);
}



} // namespace Player
} // namespace RFOnline
