/**
 * @file j_RemoveSlotCCharacterQEAAHPEAV1Z_140003EA4_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_RemoveSlotCCharacterQEAAHPEAV1Z_140003EA4
 * @note Address: 0x140003EA4
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?RemoveSlot@CCharacter@@QEAAHPEAV1@@Z
 *Address: 0x140003EA4
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


int __fastcall CCharacter::RemoveSlot(CCharacter *this, CCharacter *p)
{
  return CCharacter::RemoveSlot(this, p);
}



} // namespace Player
} // namespace RFOnline
