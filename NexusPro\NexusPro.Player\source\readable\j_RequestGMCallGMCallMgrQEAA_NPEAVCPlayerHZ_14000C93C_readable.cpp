/**
 * @file j_RequestGMCallGMCallMgrQEAA_NPEAVCPlayerHZ_14000C93C_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_RequestGMCallGMCallMgrQEAA_NPEAVCPlayerHZ_14000C93C
 * @note Address: 0x14000C93C
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?RequestGMCall@GMCallMgr@@QEAA_NPEAVCPlayer@@H@Z
 *Address: 0x14000C93C
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


bool __fastcall GMCallMgr::RequestGMCall(GMCallMgr *this, CPlayer *pOne, int bCall)
{
  return GMCallMgr::RequestGMCall(this, pOne, bCall);
}



} // namespace Player
} // namespace RFOnline
