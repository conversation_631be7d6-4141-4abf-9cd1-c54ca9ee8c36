/**
 * @file j_pc_DowngradeItemCPlayerQEAAXPEAU_STORAGE_POS_IND_140009016_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_DowngradeItemCPlayerQEAAXPEAU_STORAGE_POS_IND_140009016
 * @note Address: 0x140009016
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_DowngradeItem@CPlayer@@QEAAXPEAU_STORAGE_POS_INDIV@@00@Z
 *Address: 0x140009016
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_DowngradeItem(CPlayer *this, _STORAGE_POS_INDIV *pposTalik, _STORAGE_POS_INDIV *pposToolItem, _STORAGE_POS_INDIV *pposUpgItem)
{
  CPlayer::pc_DowngradeItem(this, pposTalik, pposToolItem, pposUpgItem);
}



} // namespace Player
} // namespace RFOnline
