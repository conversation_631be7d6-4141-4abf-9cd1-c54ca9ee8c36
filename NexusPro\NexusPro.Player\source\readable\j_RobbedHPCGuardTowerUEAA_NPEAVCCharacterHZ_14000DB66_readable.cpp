/**
 * @file j_RobbedHPCGuardTowerUEAA_NPEAVCCharacterHZ_14000DB66_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_RobbedHPCGuardTowerUEAA_NPEAVCCharacterHZ_14000DB66
 * @note Address: 0x14000DB66
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?RobbedHP@CGuardTower@@UEAA_NPEAVCCharacter@@H@Z
 *Address: 0x14000DB66
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


bool __fastcall CGuardTower::RobbedHP(CGuardTower *this, CCharacter *pDst, int nDecHP)
{
  return CGuardTower::RobbedHP(this, pDst, nDecHP);
}



} // namespace Player
} // namespace RFOnline
