/**
 * @file size_guild_battle_suggest_request_result_zoclQEAAH_14025D5F0_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: size_guild_battle_suggest_request_result_zoclQEAAH_14025D5F0
 * @note Address: 0x14025D5F0
 */

#include <cstdint>

namespace RFOnline {
namespace Combat {

﻿/*
 *Function: ?size@_guild_battle_suggest_request_result_zocl@@QEAAHXZ
 *Address: 0x14025D5F0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


signed __int64 __fastcall _guild_battle_suggest_request_result_zocl::size(_guild_battle_suggest_request_result_zocl *this)
{
  signed __int64 result; // rax@2

  void if(this->by, Ret)
    result = 1;
  else
    result = 18i64;
  return result;
}



} // namespace Combat
} // namespace RFOnline
