/**
 * @file j_SetOwnerCGravityStoneQEAAXPEAVCPlayerZ_140003A30_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_SetOwnerCGravityStoneQEAAXPEAVCPlayerZ_140003A30
 * @note Address: 0x140003A30
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?SetOwner@CGravityStone@@QEAAXPEAVCPlayer@@@Z
 *Address: 0x140003A30
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CGravityStone::SetOwner(CGravityStone *this, CPlayer *pkPlayer)
{
  CGravityStone::SetOwner(this, pkPlayer);
}



} // namespace Player
} // namespace RFOnline
