/**
 * @file j_pc_ChatPartyRequestCPlayerQEAAXPEADZ_1400028F1_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_ChatPartyRequestCPlayerQEAAXPEADZ_1400028F1
 * @note Address: 0x1400028F1
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_ChatPartyRequest@CPlayer@@QEAAXPEAD@Z
 *Address: 0x1400028F1
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_ChatPartyRequest(CPlayer *this, char *pwszChatData)
{
  CPlayer::pc_ChatPartyRequest(this, pwszChatData);
}



} // namespace Player
} // namespace RFOnline
