/**
 * @file j_pc_RefreshGroupTargetPositionCPlayerQEAAXEPEAVCG_140005C86_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_RefreshGroupTargetPositionCPlayerQEAAXEPEAVCG_140005C86
 * @note Address: 0x140005C86
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_RefreshGroupTargetPosition@CPlayer@@QEAAXEPEAVCGameObject@@@Z
 *Address: 0x140005C86
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_RefreshGroupTargetPosition(CPlayer *this, char byGroupType, CGameObject *pObject)
{
  CPlayer::pc_RefreshGroupTargetPosition(this, byGroupType, pObject);
}



} // namespace Player
} // namespace RFOnline
