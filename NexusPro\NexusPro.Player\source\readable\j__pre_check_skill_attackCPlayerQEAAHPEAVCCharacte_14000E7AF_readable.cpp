/**
 * @file j__pre_check_skill_attackCPlayerQEAAHPEAVCCharacte_14000E7AF_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j__pre_check_skill_attackCPlayerQEAAHPEAVCCharacte_14000E7AF
 * @note Address: 0x14000E7AF
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?_pre_check_skill_attack@CPlayer@@QEAAHPEAVCCharacter@@PEAMEPEAU_skill_fld@@GPEAPEAU_db_con@_STORAGE_LIST@@PEAPEAU_BulletItem_fld@@HPEAGG34@Z
 *Address: 0x14000E7AF
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


int __fastcall CPlayer::_pre_check_skill_attack(CPlayer *this, <PERSON>haracter *pDst, float *pfAttackPos, char byEffectCode, _skill_fld *pSkillFld, unsigned __int16 wBulletSerial, _STORAGE_LIST::_db_con **ppBulletProp, _BulletItem_fld **ppfldBullet, int nEffectGroup, unsigned __int16 *pdwDecPoint, unsigned __int16 wEffBtSerial, _STORAGE_LIST::_db_con **ppEffBtProp, _BulletItem_fld **ppfldEffBt)
{
  return CPlayer::_pre_check_skill_attack(
           this,
           pDst,
           pfAttackPos,
           byEffectCode,
           pSkillFld,
           wBulletSerial,
           ppBulletProp,
           ppfldBullet,
           nEffectGroup,
           pdwDecPoint,
           wEffBtSerial,
           ppEffBtProp,
           ppfldEffBt);
}



} // namespace Player
} // namespace RFOnline
