/**
 * @file j_pc_MoveModeChangeRequestCPlayerQEAAXEZ_14000CA09_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_MoveModeChangeRequestCPlayerQEAAXEZ_14000CA09
 * @note Address: 0x14000CA09
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_MoveModeChangeRequest@CPlayer@@QEAAXE@Z
 *Address: 0x14000CA09
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_MoveModeChangeRequest(CPlayer *this, char byMoveType)
{
  CPlayer::pc_MoveModeChangeRequest(this, byMoveType);
}



} // namespace Player
} // namespace RFOnline
