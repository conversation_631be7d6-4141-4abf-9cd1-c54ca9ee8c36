/**
 * @file j_pc_UserSoccerBallCPlayerQEAAEGAEAGZ_14000C941_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_UserSoccerBallCPlayerQEAAEGAEAGZ_14000C941
 * @note Address: 0x14000C941
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_UserSoccerBall@CPlayer@@QEAAEGAEAG@Z
 *Address: 0x14000C941
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


char __fastcall CPlayer::pc_UserSoccerBall(CPlayer *this, unsigned __int16 wItemSerial, unsigned __int16 *wItemIndex)
{
  return CPlayer::pc_UserSoccerBall(this, wItemSerial, wItemIndex);
}



} // namespace Player
} // namespace RFOnline
