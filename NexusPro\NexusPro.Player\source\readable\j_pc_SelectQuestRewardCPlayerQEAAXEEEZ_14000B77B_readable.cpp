/**
 * @file j_pc_SelectQuestRewardCPlayerQEAAXEEEZ_14000B77B_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_SelectQuestRewardCPlayerQEAAXEEEZ_14000B77B
 * @note Address: 0x14000B77B
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_SelectQuestReward@CPlayer@@QEAAXEEE@Z
 *Address: 0x14000B77B
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_SelectQuestReward(CPlayer *this, char byQuestDBSlot, char bySelectItemSlotIndex, char bySelectLinkQuestIndex)
{
  CPlayer::pc_SelectQuestReward(this, byQuestDBSlot, bySelectItemSlotIndex, bySelectLinkQuestIndex);
}



} // namespace Player
} // namespace RFOnline
