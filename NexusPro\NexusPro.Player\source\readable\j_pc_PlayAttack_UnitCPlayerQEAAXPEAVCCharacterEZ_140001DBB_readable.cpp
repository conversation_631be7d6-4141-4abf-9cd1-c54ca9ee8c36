/**
 * @file j_pc_PlayAttack_UnitCPlayerQEAAXPEAVCCharacterEZ_140001DBB_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_PlayAttack_UnitCPlayerQEAAXPEAVCCharacterEZ_140001DBB
 * @note Address: 0x140001DBB
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_PlayAttack_Unit@CPlayer@@QEAAXPEAVCCharacter@@E@Z
 *Address: 0x140001DBB
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_PlayAttack_Unit(CPlayer *this, CCharacter *pDst, char byWeaponPart)
{
  CPlayer::pc_PlayAttack_Unit(this, pDst, byWeaponPart);
}



} // namespace Player
} // namespace RFOnline
