/**
 * @file j_SendMsg_AlterTolCPlayerQEAAXXZ_140010131_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_SendMsg_AlterTolCPlayerQEAAXXZ_140010131
 * @note Address: 0x140010131
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?SendMsg_AlterTol@CPlayer@@QEAAXXZ
 *Address: 0x140010131
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::SendMsg_AlterTol(CPlayer *this)
{
  CPlayer::SendMsg_AlterTol(this);
}



} // namespace Player
} // namespace RFOnline
