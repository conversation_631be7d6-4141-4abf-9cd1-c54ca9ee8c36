/**
 * @file j_PushBuddy_BUDDY_LISTQEAAHKPEADPEAVCPlayerZ_1400046A1_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_PushBuddy_BUDDY_LISTQEAAHKPEADPEAVCPlayerZ_1400046A1
 * @note Address: 0x1400046A1
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?PushBuddy@_BUDDY_LIST@@QEAAHKPEADPEAVCPlayer@@@Z
 *Address: 0x1400046A1
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


int __fastcall _BUDDY_LIST::PushBuddy(_BUDDY_LIST *this, unsigned int dwSerial, char *pwszName, CPlayer *pPtr)
{
  return _BUDDY_LIST::PushBuddy(this, dwSerial, pwszName, pPtr);
}



} // namespace Player
} // namespace RFOnline
