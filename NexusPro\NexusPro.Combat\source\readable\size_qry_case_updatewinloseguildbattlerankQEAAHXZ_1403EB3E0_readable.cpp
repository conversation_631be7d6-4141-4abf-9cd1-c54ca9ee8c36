/**
 * @file size_qry_case_updatewinloseguildbattlerankQEAAHXZ_1403EB3E0_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: size_qry_case_updatewinloseguildbattlerankQEAAHXZ_1403EB3E0
 * @note Address: 0x1403EB3E0
 */

#include <cstdint>

namespace RFOnline {
namespace Combat {

﻿/*
 *Function: ?size@_qry_case_updatewinloseguildbattlerank@@QEAAHXZ
 *Address: 0x1403EB3E0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


signed __int64 __fastcall _qry_case_updatewinloseguildbattlerank::size(_qry_case_updatewinloseguildbattlerank *this)
{
  return 16i64;
}



} // namespace Combat
} // namespace RFOnline
