/**
 * @file j_pc_RadarCharInfoCPlayerQEAA_NXZ_1400089F9_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_RadarCharInfoCPlayerQEAA_NXZ_1400089F9
 * @note Address: 0x1400089F9
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_RadarCharInfo@CPlayer@@QEAA_NXZ
 *Address: 0x1400089F9
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


bool __fastcall CPlayer::pc_RadarCharInfo(CPlayer *this)
{
  return CPlayer::pc_RadarCharInfo(this);
}



} // namespace Player
} // namespace RFOnline
