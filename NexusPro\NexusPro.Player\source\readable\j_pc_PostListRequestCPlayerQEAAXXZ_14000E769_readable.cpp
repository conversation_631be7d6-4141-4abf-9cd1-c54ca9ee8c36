/**
 * @file j_pc_PostListRequestCPlayerQEAAXXZ_14000E769_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_PostListRequestCPlayerQEAAXXZ_14000E769
 * @note Address: 0x14000E769
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_PostListRequest@CPlayer@@QEAAXXZ
 *Address: 0x14000E769
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_PostListRequest(CPlayer *this)
{
  CPlayer::pc_PostListRequest(this);
}



} // namespace Player
} // namespace RFOnline
