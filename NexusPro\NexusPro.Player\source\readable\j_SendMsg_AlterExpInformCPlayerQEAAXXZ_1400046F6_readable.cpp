/**
 * @file j_SendMsg_AlterExpInformCPlayerQEAAXXZ_1400046F6_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_SendMsg_AlterExpInformCPlayerQEAAXXZ_1400046F6
 * @note Address: 0x1400046F6
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?SendMsg_AlterExpInform@CPlayer@@QEAAXXZ
 *Address: 0x1400046F6
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::SendMsg_AlterExpInform(CPlayer *this)
{
  CPlayer::SendMsg_AlterExpInform(this);
}



} // namespace Player
} // namespace RFOnline
