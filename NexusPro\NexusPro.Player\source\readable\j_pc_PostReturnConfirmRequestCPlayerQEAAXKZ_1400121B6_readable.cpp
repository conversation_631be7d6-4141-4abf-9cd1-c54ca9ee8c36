/**
 * @file j_pc_PostReturnConfirmRequestCPlayerQEAAXKZ_1400121B6_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_PostReturnConfirmRequestCPlayerQEAAXKZ_1400121B6
 * @note Address: 0x1400121B6
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_PostReturnConfirmRequest@CPlayer@@QEAAXK@Z
 *Address: 0x1400121B6
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_PostReturnConfirmRequest(CPlayer *this, unsigned int dwPostSerial)
{
  CPlayer::pc_PostReturnConfirmRequest(this, dwPostSerial);
}



} // namespace Player
} // namespace RFOnline
