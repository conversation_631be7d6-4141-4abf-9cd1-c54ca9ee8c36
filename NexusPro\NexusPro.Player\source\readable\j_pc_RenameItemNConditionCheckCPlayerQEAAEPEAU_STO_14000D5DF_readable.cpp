/**
 * @file j_pc_RenameItemNConditionCheckCPlayerQEAAEPEAU_STO_14000D5DF_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_RenameItemNConditionCheckCPlayerQEAAEPEAU_STO_14000D5DF
 * @note Address: 0x14000D5DF
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_RenameItemNConditionCheck@CPlayer@@QEAAEPEAU_STORAGE_POS_INDIV@@PEAPEAU_db_con@_STORAGE_LIST@@@Z
 *Address: 0x14000D5DF
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


char __fastcall CPlayer::pc_RenameItemNConditionCheck(CPlayer *this, _STORAGE_POS_INDIV *pItemInfo, _STORAGE_LIST::_db_con **ppItem)
{
  return CPlayer::pc_RenameItemNConditionCheck(this, pItemInfo, ppItem);
}



} // namespace Player
} // namespace RFOnline
