/**
 * @file j_pc_DTradeAddRequestCPlayerQEAAXEEKEZ_140004E3F_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_DTradeAddRequestCPlayerQEAAXEEKEZ_140004E3F
 * @note Address: 0x140004E3F
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_DTradeAddRequest@CPlayer@@QEAAXEEKE@Z
 *Address: 0x140004E3F
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_DTradeAddRequest(CPlayer *this, char bySlotIndex, char byStorageCode, unsigned int dwSerial, char byAmount)
{
  CPlayer::pc_DTradeAddRequest(this, bySlotIndex, byStorageCode, dwSerial, byAmount);
}



} // namespace Player
} // namespace RFOnline
