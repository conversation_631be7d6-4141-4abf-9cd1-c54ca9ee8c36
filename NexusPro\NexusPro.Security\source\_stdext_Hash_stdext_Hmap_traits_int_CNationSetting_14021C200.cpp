﻿/*
 *Function: _stdext::_Hash_stdext::_Hmap_traits_int_CNationSettingFactory_____ptr64_stdext::hash_compare_int_std::less_int____std::allocator_std::pair_int_const__CNationSettingFactory_____ptr64____0___::insert_::_1_::dtor$14
 *Address: 0x14021C200
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// Additional STL includes for compilation
#include <iterator>
#include <list>
#include <memory>
#include <utility>



void __fastcall stdext::_Hash_stdext::_Hmap_traits_int_CNationSettingFactory_____ptr64_stdext::hash_compare_int_std::less_int____std::allocator_std::pair_int_const__CNationSettingFactory_____ptr64____0___::insert_::_1_::dtor_14(__int64 a1, __int64 a2)
{
  if(*((D WO RD*)a2 + 612) &1 )
  {
    *((DWORD*)a2 + 612) &= 0xFFFFFFFE;
    std::pair<std::list<std::pair<int const,CNationSettingFactory *>,std::allocator<std::pair<int const,CNationSettingFactory * > >>::_Iterator<0>,bool>::~std::pair<std::list<std::pair<int const,CNationSettingFactory *>,std::allocator<std::pair<int const,CNationSettingFactory * > >>::_Iterator<0>,bool>(*(std::pair<std::list<std::pair<int const ,CNationSettingFactory *>,std::allocator<std::pair<int const ,CNationSettingFactory *>  > >::_Iterator<0>,bool> **)(a2 + 856));
  }
}



