/**
 * @file j_SendMsg_AddEffectCPlayerQEAAXGEGKPEADZ_140003D64_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_SendMsg_AddEffectCPlayerQEAAXGEGKPEADZ_140003D64
 * @note Address: 0x140003D64
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?SendMsg_AddEffect@CPlayer@@QEAAXGEGKPEAD@Z
 *Address: 0x140003D64
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::SendMsg_AddEffect(CPlayer *this, unsigned __int16 wEffectCode, char byLv, unsigned __int16 wDurSec, unsigned int dwPlayerSerial, char *wszPlayerName)
{
  CPlayer::SendMsg_AddEffect(this, wEffectCode, byLv, wDurSec, dwPlayerSerial, wszPlayerName);
}



} // namespace Player
} // namespace RFOnline
