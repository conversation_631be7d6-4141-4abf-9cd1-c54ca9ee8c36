/**
 * @file j_RemoveAllContinousEffectGroupCCharacterQEAA_NIZ_140010B54_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_RemoveAllContinousEffectGroupCCharacterQEAA_NIZ_140010B54
 * @note Address: 0x140010B54
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?RemoveAllContinousEffectGroup@CCharacter@@QEAA_NI@Z
 *Address: 0x140010B54
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


bool __fastcall CCharacter::RemoveAllContinousEffectGroup(CCharacter *this, unsigned int uiEffectCodeType)
{
  return CCharacter::RemoveAllContinousEffectGroup(this, uiEffectCodeType);
}



} // namespace Player
} // namespace RFOnline
