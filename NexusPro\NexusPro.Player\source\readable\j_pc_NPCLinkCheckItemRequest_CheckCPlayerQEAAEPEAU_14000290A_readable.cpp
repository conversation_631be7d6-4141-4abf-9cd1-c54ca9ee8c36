/**
 * @file j_pc_NPCLinkCheckItemRequest_CheckCPlayerQEAAEPEAU_14000290A_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_NPCLinkCheckItemRequest_CheckCPlayerQEAAEPEAU_14000290A
 * @note Address: 0x14000290A
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_NPCLinkCheckItemRequest_Check@CPlayer@@QEAAEPEAU_STORAGE_POS_INDIV@@@Z
 *Address: 0x14000290A
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


char __fastcall CPlayer::pc_NPCLinkCheckItemRequest_Check(CPlayer *this, _STORAGE_POS_INDIV *pStorage)
{
  return CPlayer::pc_NPCLinkCheckItemRequest_Check(this, pStorage);
}



} // namespace Player
} // namespace RFOnline
