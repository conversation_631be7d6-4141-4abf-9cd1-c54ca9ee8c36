/**
 * @file size_qry_case_addguildbattlescheduleQEAAHXZ_1403D93C0_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: size_qry_case_addguildbattlescheduleQEAAHXZ_1403D93C0
 * @note Address: 0x1403D93C0
 */

#include <cstdint>

namespace RFOnline {
namespace Combat {

﻿/*
 *Function: ?size@_qry_case_addguildbattleschedule@@QEAAHXZ
 *Address: 0x1403D93C0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


signed __int64 __fastcall _qry_case_addguildbattleschedule::size(_qry_case_addguildbattleschedule *this)
{
  return 48i64;
}



} // namespace Combat
} // namespace RFOnline
