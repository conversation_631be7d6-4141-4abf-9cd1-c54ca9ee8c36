/**
 * @file SendMsg_PostReturnCPlayerQEAAXEKPEAD00EG_KKKZ_1400E6740_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: SendMsg_PostReturnCPlayerQEAAXEKPEAD00EG_KKKZ_1400E6740
 * @note Address: 0x1400E6740
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: ?SendMsg_PostReturn@CPlayer@@QEAAXEKPEAD00EG_KKK@Z
 *Address: 0x1400E6740
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// External symbol declarations for decompiled code
extern "C" ULONG_PTR _security_cookie;


// External symbol declarations for decompiled code
extern "C" ULONG_PTR _security_cookie;



void __fastcall CPlayer::SendMsg_PostReturn(CPlayer *this, char byErrCode, unsigned int dwPostSerial, char *wszRecvName, char *wszTitle, char *wszContent, char byTableCode, unsigned __int16 wItemIndex, unsigned __int64 dwDur, unsigned int dwLv, unsigned int dwGold)
{
  __int64*v11; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v13; // [sp+0h] [bp-198h]@1
  char szMsg; // [sp+40h] [bp-158h]@4
  unsigned int v15; // [sp+41h] [bp-157h]@4
  char Dst; // [sp+45h] [bp-153h]@4
  char v17; // [sp+56h] [bp-142h]@4
  char v18; // [sp+6Bh] [bp-12Dh]@4
  char v19; // [sp+134h] [bp-64h]@4
  unsigned __int16 v20; // [sp+135h] [bp-63h]@4
  unsigned __int64 v21; // [sp+137h] [bp-61h]@4
  unsigned int v22; // [sp+13Fh] [bp-59h]@4
  unsigned int v23; // [sp+143h] [bp-55h]@4
  char pbyType; // [sp+164h] [bp-34h]@4
  char v25; // [sp+165h] [bp-33h]@4
  unsigned __int64 v26; // [sp+180h] [bp-18h]@4
  CPlayer*v27; // [sp+1A0h] [bp+8h]@1

  v27 = this;
  v11 = &v13;
  void for(signed __int64 i = 100; i > 0; --i);
{
    *(DWORD *)v11 = -858993460;
    v11 = (__int64 *)((char *)v11 + 4);
  }
  v26 = (unsigned __int64)&v13 ^ _security_cookie;
  szMsg = byErrCode;
  v15 = dwPostSerial;
  strcpy_s(&Dst, 0x11ui64, wszRecvName);
  strcpy_s(&v17, 0x15ui64, wszTitle);
  strcpy_s(&v18, 0xC9ui64, wszContent);
  v19 = byTableCode;
  v20 = wItemIndex;
  v21 = dwDur;
  v22 = dwLv;
  v23 = dwGold;
  pbyType = 58;
  v25 = 13;
  CNetProcess::LoadSendMsg(unk_1414F2088, v27->m_ObjID.m_wIndex, &pbyType, &szMsg, 0x107u);
}




} // namespace Player
} // namespace RFOnline
