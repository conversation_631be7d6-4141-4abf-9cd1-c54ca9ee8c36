/**
 * @file j_pc_DarkHoleClearOutRequestCPlayerQEAAXXZ_140003F30_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_DarkHoleClearOutRequestCPlayerQEAAXXZ_140003F30
 * @note Address: 0x140003F30
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_DarkHoleClearOutRequest@CPlayer@@QEAAXXZ
 *Address: 0x140003F30
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_DarkHoleClearOutRequest(CPlayer *this)
{
  CPlayer::pc_DarkHoleClearOutRequest(this);
}



} // namespace Player
} // namespace RFOnline
