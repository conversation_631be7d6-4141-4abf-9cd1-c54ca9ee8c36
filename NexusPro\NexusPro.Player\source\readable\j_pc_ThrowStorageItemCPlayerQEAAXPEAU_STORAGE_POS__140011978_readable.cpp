/**
 * @file j_pc_ThrowStorageItemCPlayerQEAAXPEAU_STORAGE_POS__140011978_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_ThrowStorageItemCPlayerQEAAXPEAU_STORAGE_POS__140011978
 * @note Address: 0x140011978
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_ThrowStorageItem@CPlayer@@QEAAXPEAU_STORAGE_POS_INDIV@@@Z
 *Address: 0x140011978
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_ThrowStorageItem(CPlayer *this, _STORAGE_POS_INDIV *pItem)
{
  CPlayer::pc_ThrowStorageItem(this, pItem);
}



} // namespace Player
} // namespace RFOnline
