/**
 * @file j_pc_GotoAvatorRequestCPlayerQEAAXPEADZ_140013FC0_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_GotoAvatorRequestCPlayerQEAAXPEADZ_140013FC0
 * @note Address: 0x140013FC0
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_GotoAvatorRequest@CPlayer@@QEAAXPEAD@Z
 *Address: 0x140013FC0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_GotoAvatorRequest(CPlayer *this, char *pwszAvatorName)
{
  CPlayer::pc_GotoAvatorRequest(this, pwszAvatorName);
}



} // namespace Player
} // namespace RFOnline
