/**
 * @file SendMsg_RACE_GreetingCPlayerQEAAXPEAD0Z_1400E6260_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: SendMsg_RACE_GreetingCPlayerQEAAXPEAD0Z_1400E6260
 * @note Address: 0x1400E6260
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: ?SendMsg_RACE_Greeting@CPlayer@@QEAAXPEAD0@Z
 *Address: 0x1400E6260
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// External symbol declarations for decompiled code
extern "C" ULONG_PTR _security_cookie;

#include <cstring>

// External symbol declarations for decompiled code
extern "C" ULONG_PTR _security_cookie;



void __fastcall CPlayer::SendMsg_RACE_Greeting(CPlayer *this, char *wszBoss<PERSON>ame, char *wszMsg)
{
  __int64*v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-1A8h]@1
  _announ_message_receipt_udp Dst; // [sp+40h] [bp-168h]@7
  char pbyType; // [sp+174h] [bp-34h]@7
  char v8; // [sp+175h] [bp-33h]@7
  int v9; // [sp+184h] [bp-24h]@7
  unsigned __int64 v10; // [sp+190h] [bp-18h]@4
  CPlayer*v11; // [sp+1B0h] [bp+8h]@1
  const char*_Source; // [sp+1B8h] [bp+10h]@1
  const char*Str; // [sp+1C0h] [bp+18h]@1

  Str = wszMsg;
  _Source = wszBossName;
  v11 = this;
  v3 = &v5;
  void for(signed __int64 i = 104; i > 0; --i);
{
    *(DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v10 = (unsigned __int64)&v5 ^ _security_cookie;
  void if(v11->m_p, User DB);
{
    void if(!v11->m_p, User DB->m_b, Chat, Lock);
{
      _announ_message_receipt_udp::_announ_message_receipt_udp(&Dst);
      Dst.byMessageType = 16;
      Dst.bySenderRace = CPlayerDB::GetRaceCode(&v11->m _Param);
      Dst.dwSenderSerial = v11->m_dwObjSerial;
      strcpy_s<17>((char (*)[17])Dst.wszSenderName, _Source);
      Dst.bySize = strlen_0(Str);
      memcpy_0(Dst.wszChatData, Str, (unsigned __int8)Dst.bySize);
      Dst.wszChatData[(unsigned __int8)Dst.bySize] = 0;
      pbyType = 2;
      v8 = 11;
      v9 = _announ_message_receipt_udp::size(&Dst);
      CNetProcess::LoadSendMsg(unk_1414F2088, v11->m_ObjID.m_wIndex, &pbyType, &Dst.byMessageType, v9);
    }
  }
}




} // namespace Player
} // namespace RFOnline
