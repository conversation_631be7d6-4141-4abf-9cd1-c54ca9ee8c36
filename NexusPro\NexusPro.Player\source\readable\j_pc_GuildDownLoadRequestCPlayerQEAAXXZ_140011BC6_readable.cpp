/**
 * @file j_pc_GuildDownLoadRequestCPlayerQEAAXXZ_140011BC6_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_GuildDownLoadRequestCPlayerQEAAXXZ_140011BC6
 * @note Address: 0x140011BC6
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_GuildDownLoadRequest@CPlayer@@QEAAXXZ
 *Address: 0x140011BC6
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_GuildDownLoadRequest(CPlayer *this)
{
  CPlayer::pc_GuildDownLoadRequest(this);
}



} // namespace Player
} // namespace RFOnline
