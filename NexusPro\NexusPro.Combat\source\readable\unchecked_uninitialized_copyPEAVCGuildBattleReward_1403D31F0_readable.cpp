/**
 * @file unchecked_uninitialized_copyPEAVCGuildBattleReward_1403D31F0_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: unchecked_uninitialized_copyPEAVCGuildBattleReward_1403D31F0
 * @note Address: 0x1403D31F0
 */

#include <cstdint>

namespace RFOnline {
namespace Combat {

﻿/*
 *Function: ??$unchecked_uninitialized_copy@PEAVCGuildBattleRewardItem@GUILD_BATTLE@@PEAV12@V?$allocator@VCGuildBattleRewardItem@GUILD_BATTLE@@@std@@@stdext@@YAPEAVCGuildBattleRewardItem@GUILD_BATTLE@@PEAV12@00AEAV?$allocator@VCGuildBattleRewardItem@GUILD_BATTLE@@@std@@@Z
 *Address: 0x1403D31F0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"
#include <cstring>

// Additional STL includes for compilation
#include <iterator>
#include <memory>



GUILD_BATTLE::CGuildBattleRewardItem *__fastcall stdext::unchecked_uninitialized_copy<GUILD_BATTLE::CGuildBattleRewardItem *,GUILD_BATTLE::CGuildBattleRewardItem *,std::allocator<GUILD_BATTLE::CGuildBattleRewardItem > >(GUILD_BATTLE::CGuildBattleRewardItem *_First, GUILD_BATTLE::CGuildBattleRewardItem *_Last, GUILD_BATTLE::CGuildBattleRewardItem *_Dest, std::allocator<GUILD_BATTLE::CGuildBattleRewardItem> *_Al)
{
  __int64*v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // [sp+0h] [bp-48h]@1
  std::_Range_checked_iterator_tag v8; // [sp+30h] [bp-18h]@4
  std::_Nonscalar_ptr_iterator_tag v9; // [sp+31h] [bp-17h]@4
  GUILD_BATTLE::CGuildBattleRewardItem*__formal; // [sp+50h] [bp+8h]@1
  GUILD_BATTLE::CGuildBattleRewardItem*_Lasta; // [sp+58h] [bp+10h]@1
  GUILD_BATTLE::CGuildBattleRewardItem*_Desta; // [sp+60h] [bp+18h]@1
  std::allocator<GUILD_BATTLE::CGuildBattleRewardItem> *_Ala; // [sp+68h] [bp+20h]@1

  _Ala = _Al;
  _Desta = _Dest;
  _Lasta = _Last;
  __formal = _First;
  v4 = &v7;
  void for(i = 16; i > 0; --i );
{
    *(DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  memset(&v8, 0, sizeof(v8));
  v9 = std::_Ptr_cat<GUILD_BATTLE::CGuildBattleRewardItem *,GUILD_BATTLE::CGuildBattleRewardItem *>(&__formal, &_Desta);
  return std::_Uninit_copy<GUILD_BATTLE::CGuildBattleRewardItem *,GUILD_BATTLE::CGuildBattleRewardItem *,std::allocator<GUILD_BATTLE::CGuildBattleRewardItem > >(
           __formal,
           _Lasta,
           _Desta,
           _Ala,
           v9,
           v8);
}





} // namespace Combat
} // namespace RFOnline
