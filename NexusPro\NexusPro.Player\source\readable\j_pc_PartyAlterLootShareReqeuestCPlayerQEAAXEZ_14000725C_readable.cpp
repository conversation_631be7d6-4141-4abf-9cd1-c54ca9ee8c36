/**
 * @file j_pc_PartyAlterLootShareReqeuestCPlayerQEAAXEZ_14000725C_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_PartyAlterLootShareReqeuestCPlayerQEAAXEZ_14000725C
 * @note Address: 0x14000725C
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_PartyAlterLootShareReqeuest@CPlayer@@QEAAXE@Z
 *Address: 0x14000725C
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_PartyAlterLootShareReqeuest(CPlayer *this, char byLootShareMode)
{
  CPlayer::pc_PartyAlterLootShareReqeuest(this, byLootShareMode);
}



} // namespace Player
} // namespace RFOnline
