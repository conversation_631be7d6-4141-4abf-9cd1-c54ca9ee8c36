/**
 * @file SendMsg_SelectWaitedQuestCPlayerQEAAXEKEZ_1400E2090_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: SendMsg_SelectWaitedQuestCPlayerQEAAXEKEZ_1400E2090
 * @note Address: 0x1400E2090
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: ?SendMsg_SelectWaitedQuest@CPlayer@@QEAAXEKE@Z
 *Address: 0x1400E2090
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::SendMsg_SelectWaitedQuest(CPlayer *this, char byEventType, unsigned int dwEventIndex, char byEventNodeIndex)
{
  __int64*v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-78h]@1
  char szMsg; // [sp+34h] [bp-44h]@4
  unsigned int v8; // [sp+35h] [bp-43h]@4
  char v9; // [sp+39h] [bp-3Fh]@4
  char pbyType; // [sp+54h] [bp-24h]@4
  char v11; // [sp+55h] [bp-23h]@4
  CPlayer*v12; // [sp+80h] [bp+8h]@1

  v12 = this;
  v4 = &v6;
  void for(signed __int64 i = 28; i > 0; --i);
{
    *(DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  szMsg = byEventType;
  v8 = dwEventIndex;
  v9 = byEventNodeIndex;
  pbyType = 24;
  v11 = 2;
  CNetProcess::LoadSendMsg(unk_1414F2088, v12->m_ObjID.m_wIndex, &pbyType, &szMsg, 6u);
}




} // namespace Player
} // namespace RFOnline
