/**
 * @file j_pc_MakeItemCPlayerQEAAXPEAU_STORAGE_POS_INDIVGE0_140003544_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_MakeItemCPlayerQEAAXPEAU_STORAGE_POS_INDIVGE0_140003544
 * @note Address: 0x140003544
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_MakeItem@CPlayer@@QEAAXPEAU_STORAGE_POS_INDIV@@GE0@Z
 *Address: 0x140003544
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_MakeItem(CPlayer *this, _STORAGE_POS_INDIV *pipMakeTool, unsigned __int16 wManualIndex, char by<PERSON><PERSON><PERSON><PERSON><PERSON>, _STORAGE_POS_INDIV *pipMaterials)
{
  CPlayer::pc_MakeItem(this, pipMakeTool, wManualIndex, byMaterialNum, pipMaterials);
}



} // namespace Player
} // namespace RFOnline
