/**
 * @file j_SearchAggroNodeCMonsterAggroMgrQEAAPEAUCAggroNod_140001EE2_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_SearchAggroNodeCMonsterAggroMgrQEAAPEAUCAggroNod_140001EE2
 * @note Address: 0x140001EE2
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?SearchAggroNode@CMonsterAggroMgr@@QEAAPEAUCAggroNode@@PEAVCCharacter@@@Z
 *Address: 0x140001EE2
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


CAggroNode *__fastcall CMonsterAggroMgr::SearchAggroNode(CMonsterAggroMgr *this, CCharacter *pCharacter)
{
  return CMonsterAggroMgr::SearchAggroNode(this, pCharacter);
}



} // namespace Player
} // namespace RFOnline
