/**
 * @file j_PushLinkCPlayerDBQEAA_NHG_NZ_14000B9E2_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_PushLinkCPlayerDBQEAA_NHG_NZ_14000B9E2
 * @note Address: 0x14000B9E2
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?PushLink@CPlayerDB@@QEAA_NHG_N@Z
 *Address: 0x14000B9E2
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


bool __fastcall CPlayerDB::PushLink(CPlayerDB *this, int nLinkIndex, unsigned __int16 wSerail, bool bInit)
{
  return CPlayerDB::PushLink(this, nLinkIndex, wSerail, bInit);
}



} // namespace Player
} // namespace RFOnline
