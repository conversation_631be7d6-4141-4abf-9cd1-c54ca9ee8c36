/**
 * @file j_SF_Resurrect_OnceCPlayerUEAA_NPEAVCCharacterZ_14000E818_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_SF_Resurrect_OnceCPlayerUEAA_NPEAVCCharacterZ_14000E818
 * @note Address: 0x14000E818
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?SF_Resurrect_Once@CPlayer@@UEAA_NPEAVCCharacter@@@Z
 *Address: 0x14000E818
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


bool __fastcall CPlayer::SF_Resurrect_Once(CPlayer *this, CCharacter *pDstObj)
{
  return CPlayer::SF_Resurrect_Once(this, pDstObj);
}



} // namespace Player
} // namespace RFOnline
