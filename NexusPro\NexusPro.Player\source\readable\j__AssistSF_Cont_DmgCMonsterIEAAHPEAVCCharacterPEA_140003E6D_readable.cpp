/**
 * @file j__AssistSF_Cont_DmgCMonsterIEAAHPEAVCCharacterPEA_140003E6D_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j__AssistSF_Cont_DmgCMonsterIEAAHPEAVCCharacterPEA_140003E6D
 * @note Address: 0x140003E6D
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?_AssistSF_Cont_Dmg@CMonster@@IEAAHPEAVCCharacter@@PEAVCMonsterSkill@@@Z
 *Address: 0x140003E6D
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


int __fastcall CMonster::_AssistSF_Cont_Dmg(CMonster *this, CCharacter *pDst, CMonsterSkill *pskill)
{
  return CMonster::_AssistSF_Cont_Dmg(this, pDst, pskill);
}



} // namespace Player
} // namespace RFOnline
