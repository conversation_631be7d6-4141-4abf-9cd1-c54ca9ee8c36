/**
 * @file j_pc_UpdateDataForPostSendCPlayerQEAAXXZ_140002F2C_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_UpdateDataForPostSendCPlayerQEAAXXZ_140002F2C
 * @note Address: 0x140002F2C
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_UpdateDataForPostSend@CPlayer@@QEAAXXZ
 *Address: 0x140002F2C
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_UpdateDataForPostSend(CPlayer *this)
{
  CPlayer::pc_UpdateDataForPostSend(this);
}



} // namespace Player
} // namespace RFOnline
