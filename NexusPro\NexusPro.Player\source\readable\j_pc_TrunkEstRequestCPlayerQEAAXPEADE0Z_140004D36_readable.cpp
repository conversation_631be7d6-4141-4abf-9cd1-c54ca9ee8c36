/**
 * @file j_pc_TrunkEstRequestCPlayerQEAAXPEADE0Z_140004D36_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_TrunkEstRequestCPlayerQEAAXPEADE0Z_140004D36
 * @note Address: 0x140004D36
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_TrunkEstRequest@CPlayer@@QEAAXPEADE0@Z
 *Address: 0x140004D36
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_TrunkEstRequest(CPlayer *this, char *pwszPassword, char byHintIndex, char *pwszHintAnswer)
{
  CPlayer::pc_TrunkEstRequest(this, pwszPassword, byHintIndex, pwszHintAnswer);
}



} // namespace Player
} // namespace RFOnline
