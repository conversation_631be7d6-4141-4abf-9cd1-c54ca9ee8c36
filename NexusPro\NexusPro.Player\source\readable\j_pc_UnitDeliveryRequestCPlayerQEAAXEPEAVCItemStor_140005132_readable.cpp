/**
 * @file j_pc_UnitDeliveryRequestCPlayerQEAAXEPEAVCItemStor_140005132_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_UnitDeliveryRequestCPlayerQEAAXEPEAVCItemStor_140005132
 * @note Address: 0x140005132
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_UnitDeliveryRequest@CPlayer@@QEAAXEPEAVCItemStore@@_NPEAMH@Z
 *Address: 0x140005132
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_UnitDeliveryRequest(CPlayer *this, char bySlotIndex, CItemStore *pStore, bool bPayFee, float *pfNewPos, int bUseNPCLinkIntem)
{
  CPlayer::pc_UnitDeliveryRequest(this, bySlotIndex, pStore, bPayFee, pfNewPos, bUseNPCLinkIntem);
}



} // namespace Player
} // namespace RFOnline
