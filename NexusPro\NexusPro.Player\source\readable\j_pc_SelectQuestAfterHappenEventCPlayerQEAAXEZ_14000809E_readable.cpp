/**
 * @file j_pc_SelectQuestAfterHappenEventCPlayerQEAAXEZ_14000809E_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_SelectQuestAfterHappenEventCPlayerQEAAXEZ_14000809E
 * @note Address: 0x14000809E
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_SelectQuestAfterHappenEvent@CPlayer@@QEAAXE@Z
 *Address: 0x14000809E
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_SelectQuestAfterHappenEvent(CPlayer *this, char bySelectIndex)
{
  CPlayer::pc_SelectQuestAfterHappenEvent(this, bySelectIndex);
}



} // namespace Player
} // namespace RFOnline
