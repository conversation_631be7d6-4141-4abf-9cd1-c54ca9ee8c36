/**
 * @file j_RequestRecallCRecallEffectControllerQEAA_NPEAVCP_14000EE21_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_RequestRecallCRecallEffectControllerQEAA_NPEAVCP_14000EE21
 * @note Address: 0x14000EE21
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?RequestRecall@CRecallEffectController@@QEAA_NPEAVCPlayer@@PEAVCCharacter@@_N22@Z
 *Address: 0x14000EE21
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


bool __fastcall CRecallEffectController::RequestRecall(CRecallEffectController *this, CPlayer *pkPerformer, CCharacter *pkDest, bool bRecallParty, bool bStone, bool bBattleModeUse)
{
  return CRecallEffectController::RequestRecall(this, pkPerformer, pkDest, bRecallParty, bStone, bBattleModeUse);
}



} // namespace Player
} // namespace RFOnline
