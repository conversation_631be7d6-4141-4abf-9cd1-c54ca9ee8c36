/**
 * @file j_pc_GuildJoinApplyRequestCPlayerQEAAXPEADZ_14000583A_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_GuildJoinApplyRequestCPlayerQEAAXPEADZ_14000583A
 * @note Address: 0x14000583A
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_GuildJoinApplyRequest@CPlayer@@QEAAXPEAD@Z
 *Address: 0x14000583A
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_GuildJoinApplyRequest(CPlayer *this, char *pwszGuildName)
{
  CPlayer::pc_GuildJoinApplyRequest(this, pwszGuildName);
}



} // namespace Player
} // namespace RFOnline
