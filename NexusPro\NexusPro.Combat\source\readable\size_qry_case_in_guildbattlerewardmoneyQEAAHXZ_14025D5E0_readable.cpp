/**
 * @file size_qry_case_in_guildbattlerewardmoneyQEAAHXZ_14025D5E0_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: size_qry_case_in_guildbattlerewardmoneyQEAAHXZ_14025D5E0
 * @note Address: 0x14025D5E0
 */

#include <cstdint>

namespace RFOnline {
namespace Combat {

﻿/*
 *Function: ?size@_qry_case_in_guildbattlerewardmoney@@QEAAHXZ
 *Address: 0x14025D5E0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


signed __int64 __fastcall _qry_case_in_guildbattlerewardmoney::size(_qry_case_in_guildbattlerewardmoney *this)
{
  return 40i64;
}



} // namespace Combat
} // namespace RFOnline
