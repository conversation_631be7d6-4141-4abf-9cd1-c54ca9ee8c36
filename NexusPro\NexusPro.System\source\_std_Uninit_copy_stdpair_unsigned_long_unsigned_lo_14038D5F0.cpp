﻿/*
 *Function: _std::_Uninit_copy_std::pair_unsigned_long_unsigned_long______ptr64_std::pair_unsigned_long_unsigned_long______ptr64_std::allocator_std::pair_unsigned_long_unsigned_long______::_1_::catch$0
 *Address: 0x14038D5F0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// Additional STL includes for compilation
#include <memory>
#include <utility>



void __fastcall __noreturn std::_Uninit_copy_std::pair_unsigned_long_unsigned_long______ptr64_std::pair_unsigned_long_unsigned_long______ptr64_std::allocator_std::pair_unsigned_long_unsigned_long______::_1_::catch_0(__int64 a1, __int64 a2)
{
  __int64 i; // rbp@1

  for(i = a2; *((Q WO RD*)i + 32) != *((QWORD*)i + 80); *((QWORD*)i + 32) += 8i64 )
    std::allocator<std::pair<unsigned long,unsigned long > >::destroy(
      *(std::allocator<std::pair<unsigned long,unsigned long>  > **)(i + 88),
      *(std::pair<unsigned long,unsigned long> **)(i + 32));
  CxxThrowException_0(0i64, 0i64);
}



