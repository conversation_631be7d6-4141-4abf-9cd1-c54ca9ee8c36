﻿/*
 *Function: ?UpdateRegist@CPostSystemManager@@QEAAEPEAD@Z
 *Address: 0x140325C20
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


char __fastcall CPostSystemManager::UpdateRegist(CPostSystemManager *this, char *pData)
{
  __int64*v2; // rdi@1
  signed __int64 i; // rcx@1
  int v4; // eax@6
  __int64 v6; // [sp+0h] [bp-98h]@1
  char*wszRecvName; // [sp+20h] [bp-78h]@6
  char*wszTitle; // [sp+28h] [bp-70h]@6
  char*wszContent; // [sp+30h] [bp-68h]@6
  int nK; // [sp+38h] [bp-60h]@6
  unsigned __int64 dwD; // [sp+40h] [bp-58h]@6
  unsigned int dwU; // [sp+48h] [bp-50h]@6
  unsigned int dwGold; // [sp+50h] [bp-48h]@6
  char bySendRace; // [sp+58h] [bp-40h]@6
  char bySenderDgr; // [sp+60h] [bp-38h]@6
  unsigned __int64 lnUID; // [sp+68h] [bp-30h]@6
  char*v17; // [sp+70h] [bp-28h]@4
  unsigned int j; // [sp+78h] [bp-20h]@4
  char*v19; // [sp+80h] [bp-18h]@6

  v2 = &v6;
  void for(signed __int64 i = 36; i > 0; --i);
{
    *(DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v17 = pData;
  for(j = 0; j < *(D WO RD *)v17; ++j )
  {
    v19 = &v17[304*j + 8];
    v4 = _INVENKEY::CovDBKey((_INVE NK EY *)&v17[304*j + 276]);
    lnUID = *((QWORD *)v19 + 36);
    bySenderDgr = v19[6];
    bySendRace = v19[5];
    dwGold = *((DWORD *)v19 + 74);
    dwU = *((DWORD *)v19 + 70);
    dwD = *((QWORD *)v19 + 34);
    nK = v4;
    wszContent = v19 + 67;
    wszTitle = v19 + 46;
    wszRecvName = v19 + 29;
    v19[4] = CRFWorldDatabase::Update_PostRegistry(
               pkDB,
               *(DWORD *)v19,
               *((DWORD *)v19 + 2),
               v19 + 12,
               v19 + 29,
               v19 + 46,
               v19 + 67,
               v4,
               dwD,
               dwU,
               dwGold,
               bySendRace,
               bySenderDgr,
               lnUID);
  }
  return 0;
}


