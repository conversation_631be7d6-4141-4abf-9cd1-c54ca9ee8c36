/**
 * @file j_pc_GestureRequestCPlayerQEAAXEZ_1400066E0_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_GestureRequestCPlayerQEAAXEZ_1400066E0
 * @note Address: 0x1400066E0
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_GestureRequest@CPlayer@@QEAAXE@Z
 *Address: 0x1400066E0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_GestureRequest(CPlayer *this, char byGestureType)
{
  CPlayer::pc_GestureRequest(this, byGestureType);
}



} // namespace Player
} // namespace RFOnline
