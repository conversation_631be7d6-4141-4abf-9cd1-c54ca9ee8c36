/**
 * @file unchecked_uninitialized_fill_nPEAVCGuildBattleRewa_1403D2910_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: unchecked_uninitialized_fill_nPEAVCGuildBattleRewa_1403D2910
 * @note Address: 0x1403D2910
 */

#include <cstdint>

namespace RFOnline {
namespace Combat {

﻿/*
 *Function: ??$unchecked_uninitialized_fill_n@PEAVCGuildBattleRewardItem@GUILD_BATTLE@@_KV12@V?$allocator@VCGuildBattleRewardItem@GUILD_BATTLE@@@std@@@stdext@@YAXPEAVCGuildBattleRewardItem@GUILD_BATTLE@@_KAEBV12@AEAV?$allocator@VCGuildBattleRewardItem@GUILD_BATTLE@@@std@@@Z
 *Address: 0x1403D2910
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"
#include <cstring>

// Additional STL includes for compilation
#include <iterator>
#include <memory>



void __fastcall stdext::unchecked_uninitialized_fill_n<GUILD_BATTLE::CGuildBattleRewardItem *,unsigned __int64,GUILD_BATTLE::CGuildBattleRewardItem,std::allocator<GUILD_BATTLE::CGuildBattleRewardItem > >(GUILD_BATTLE::CGuildBattleRewardItem *_First, unsigned __int64 _Count, GUILD_BATTLE::CGuildBattleRewardItem *_Val, std::allocator<GUILD_BATTLE::CGuildBattleRewardItem> *_Al)
{
  __int64*v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-48h]@1
  std::_Range_checked_iterator_tag v7; // [sp+30h] [bp-18h]@4
  std::_Nonscalar_ptr_iterator_tag v8; // [sp+31h] [bp-17h]@4
  GUILD_BATTLE::CGuildBattleRewardItem*__formal; // [sp+50h] [bp+8h]@1
  unsigned __int64 _Counta; // [sp+58h] [bp+10h]@1
  GUILD_BATTLE::CGuildBattleRewardItem*_Vala; // [sp+60h] [bp+18h]@1
  std::allocator<GUILD_BATTLE::CGuildBattleRewardItem> *_Ala; // [sp+68h] [bp+20h]@1

  _Ala = _Al;
  _Vala = _Val;
  _Counta = _Count;
  __formal = _First;
  v4 = &v6;
  void for(i = 16; i > 0; --i );
{
    *(DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  memset(&v7, 0, sizeof(v7));
  v8 = std::_Ptr_cat<GUILD_BATTLE::CGuildBattleRewardItem *,GUILD_BATTLE::CGuildBattleRewardItem *>(
         &__formal,
         &__formal);
  std::_Uninit_fill_n<GUILD_BATTLE::CGuildBattleRewardItem *,unsigned __int64,GUILD_BATTLE::CGuildBattleRewardItem,std::allocator<GUILD_BATTLE::CGuildBattleRewardItem > >(
    __formal,
    _Counta,
    _Vala,
    _Ala,
    v8,
    v7);
}





} // namespace Combat
} // namespace RFOnline
