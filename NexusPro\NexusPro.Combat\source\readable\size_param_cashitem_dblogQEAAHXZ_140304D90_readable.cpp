/**
 * @file size_param_cashitem_dblogQEAAHXZ_140304D90_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: size_param_cashitem_dblogQEAAHXZ_140304D90
 * @note Address: 0x140304D90
 */

#include <cstdint>

namespace RFOnline {
namespace Combat {

﻿/*
 *Function: ?size@_param_cashitem_dblog@@QEAAHXZ
 *Address: 0x140304D90
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


signed __int64 __fastcall _param_cashitem_dblog::size(_param_cashitem_dblog *this)
{
  return 356 - 16i64 * (20 - this->nBuyNum);
}



} // namespace Combat
} // namespace RFOnline
