/**
 * @file j_player_createCMgrAccountLobbyHistoryQEAAX_NPEAU__1400062FD_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_player_createCMgrAccountLobbyHistoryQEAAX_NPEAU__1400062FD
 * @note Address: 0x1400062FD
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?player_create@CMgrAccountLobbyHistory@@QEAAX_NPEAU_AVATOR_DATA@@PEAD@Z
 *Address: 0x1400062FD
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CMgrAccountLobbyHistory::player_create(CMgrAccountLobbyHistory *this, bool bFirstStart, _AVATOR_DATA *pAvator, char *pszFileName)
{
  CMgrAccountLobbyHistory::player_create(this, bFirstStart, pAvator, pszFileName);
}



} // namespace Player
} // namespace RFOnline
