/**
 * @file j_SF_SkillContHelpTimeInc_OnceCPlayerUEAA_NPEAVCCh_14000A28B_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_SF_SkillContHelpTimeInc_OnceCPlayerUEAA_NPEAVCCh_14000A28B
 * @note Address: 0x14000A28B
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?SF_SkillContHelpTimeInc_Once@CPlayer@@UEAA_NPEAVCCharacter@@M@Z
 *Address: 0x14000A28B
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


bool __fastcall CPlayer::SF_SkillContHelpTimeInc_Once(CPlayer *this, CCharacter *pDstObj, float fEffectValue)
{
  return CPlayer::SF_SkillContHelpTimeInc_Once(this, pDstObj, fEffectValue);
}



} // namespace Player
} // namespace RFOnline
