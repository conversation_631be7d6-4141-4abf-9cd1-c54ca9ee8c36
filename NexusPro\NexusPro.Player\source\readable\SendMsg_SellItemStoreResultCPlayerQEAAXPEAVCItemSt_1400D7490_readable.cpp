/**
 * @file SendMsg_SellItemStoreResultCPlayerQEAAXPEAVCItemSt_1400D7490_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: SendMsg_SellItemStoreResultCPlayerQEAAXPEAVCItemSt_1400D7490
 * @note Address: 0x1400D7490
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: ?SendMsg_SellItemStoreResult@CPlayer@@QEAAXPEAVCItemStore@@E@Z
 *Address: 0x1400D7490
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __usercall CPlayer::SendMsg_SellItemStoreResult(CPlayer *this@<rcx>, CItemStore *pStore@<rdx>, char byErrCode@<r8b>, float a4@<xmm0>)
{
  __int64*v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-88h]@1
  char szMsg; // [sp+38h] [bp-50h]@4
  unsigned int v8; // [sp+39h] [bp-4Fh]@4
  unsigned int v9; // [sp+3Dh] [bp-4Bh]@4
  int v10; // [sp+41h] [bp-47h]@4
  int v11; // [sp+45h] [bp-43h]@4
  char v12; // [sp+49h] [bp-3Fh]@4
  char v13; // [sp+4Ah] [bp-3Eh]@4
  char pbyType; // [sp+64h] [bp-24h]@4
  char v15; // [sp+65h] [bp-23h]@4
  CPlayer*v16; // [sp+90h] [bp+8h]@1
  CItemStore*v17; // [sp+98h] [bp+10h]@1
  char v18; // [sp+A0h] [bp+18h]@1

  v18 = byErrCode;
  v17 = pStore;
  v16 = this;
  v4 = &v6;
  void for(i = 32; i; --i );
{
    *(DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  szMsg = byErrCode == 0;
  v8 = CPlayerDB::GetDalant(&v16->m _Param);
  v9 = CPlayerDB::GetGold(&v16->m _Param);
  v10 = CItemStore::GetLastTradeDalant(v17);
  v11 = CItemStore::GetLastTradeGold(v17);
  _effect_parameter::GetEff_Have(&v16->m_EP, 1);
  v12 = 100 * (signed int)ffloor(a4);
  v13 = v18;
  pbyType = 12;
  v15 = 5;
  CNetProcess::LoadSendMsg(unk_1414F2088, v16->m_ObjID.m_wIndex, &pbyType, &szMsg, 0x13u);
}




} // namespace Player
} // namespace RFOnline
