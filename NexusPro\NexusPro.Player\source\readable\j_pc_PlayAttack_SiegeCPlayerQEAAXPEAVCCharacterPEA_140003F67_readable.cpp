/**
 * @file j_pc_PlayAttack_SiegeCPlayerQEAAXPEAVCCharacterPEA_140003F67_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_PlayAttack_SiegeCPlayerQEAAXPEAVCCharacterPEA_140003F67
 * @note Address: 0x140003F67
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_PlayAttack_Siege@CPlayer@@QEAAXPEAVC<PERSON>haracter@@PEAMEGG@Z
 *Address: 0x140003F67
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_PlayAttack_Siege(CPlayer *this, CCharacter *pDst, float *pfAttackPos, char byAtt<PERSON><PERSON>, unsigned __int16 wBulletSerial, unsigned __int16 wEffBtSerial)
{
  CPlayer::pc_PlayAttack_Siege(this, pDst, pfAttackPos, byAttPart, wBulletSerial, wEffBtSerial);
}



} // namespace Player
} // namespace RFOnline
