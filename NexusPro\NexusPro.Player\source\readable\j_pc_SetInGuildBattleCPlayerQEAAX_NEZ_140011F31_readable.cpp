/**
 * @file j_pc_SetInGuildBattleCPlayerQEAAX_NEZ_140011F31_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_SetInGuildBattleCPlayerQEAAX_NEZ_140011F31
 * @note Address: 0x140011F31
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_SetInGuildBattle@CPlayer@@QEAAX_NE@Z
 *Address: 0x140011F31
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_SetInGuildBattle(CPlayer *this, bool bInGuildBattle, char byColorInx)
{
  CPlayer::pc_SetInGuildBattle(this, bInGuildBattle, byColorInx);
}



} // namespace Player
} // namespace RFOnline
