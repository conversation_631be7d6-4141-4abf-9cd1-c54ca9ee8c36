/**
 * @file j_pc_UsePotionItemCPlayerQEAAXPEAV1PEAU_STORAGE_PO_140012ED6_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_UsePotionItemCPlayerQEAAXPEAV1PEAU_STORAGE_PO_140012ED6
 * @note Address: 0x140012ED6
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_UsePotionItem@CPlayer@@QEAAXPEAV1@PEAU_STORAGE_POS_INDIV@@@Z
 *Address: 0x140012ED6
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_UsePotionItem(CPlayer *this, CPlayer *pTargetPlayer, _STORAGE_POS_INDIV *pItem)
{
  CPlayer::pc_UsePotionItem(this, pTargetPlayer, pItem);
}



} // namespace Player
} // namespace RFOnline
