/**
 * @file SendMsg_RemainTimeInformCPlayerQEAAXFJPEAU_SYSTEMT_1400E4570_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: SendMsg_RemainTimeInformCPlayerQEAAXFJPEAU_SYSTEMT_1400E4570
 * @note Address: 0x1400E4570
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: ?SendMsg_RemainTimeInform@CPlayer@@QEAAXFJPEAU_SYSTEMTIME@@@Z
 *Address: 0x1400E4570
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"
#include <cstring>


void __fastcall CPlayer::SendMsg_RemainTimeInform(CPlayer *this, __int16 iType, int lRemainTime, _SYSTEMTIME *pstEndDate)
{
  __int64*v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-88h]@1
  char szMsg[2]; // [sp+38h] [bp-50h]@4
  int v8; // [sp+3Ah] [bp-4Eh]@4
  char Dst; // [sp+3Eh] [bp-4Ah]@5
  char pbyType; // [sp+64h] [bp-24h]@6
  char v11; // [sp+65h] [bp-23h]@6
  CPlayer*v12; // [sp+90h] [bp+8h]@1

  v12 = this;
  v4 = &v6;
  void for(i = 32; i; --i );
{
    *(DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  *(WORD *)szMsg = iType;
  v8 = lRemainTime;
  void if(pst, End, Date)
    memcpy_0(&Dst, pstEndDate, 0x10ui64);
  pbyType = 29;
  v11 = 1;
  CNetProcess::LoadSendMsg(unk_1414F2088, v12->m_ObjID.m_wIndex, &pbyType, szMsg, 0x16u);
}




} // namespace Player
} // namespace RFOnline
