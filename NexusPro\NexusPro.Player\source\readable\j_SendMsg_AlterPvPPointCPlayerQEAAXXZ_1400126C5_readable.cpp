/**
 * @file j_SendMsg_AlterPvPPointCPlayerQEAAXXZ_1400126C5_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_SendMsg_AlterPvPPointCPlayerQEAAXXZ_1400126C5
 * @note Address: 0x1400126C5
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?SendMsg_AlterPvPPoint@CPlayer@@QEAAXXZ
 *Address: 0x1400126C5
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::SendMsg_AlterPvPPoint(CPlayer *this)
{
  CPlayer::SendMsg_AlterPvPPoint(this);
}



} // namespace Player
} // namespace RFOnline
