/**
 * @file j_pc_StopCPlayerQEAAXXZ_14000A4F2_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_StopCPlayerQEAAXXZ_14000A4F2
 * @note Address: 0x14000A4F2
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_Stop@CPlayer@@QEAAXXZ
 *Address: 0x14000A4F2
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_Stop(CPlayer *this)
{
  CPlayer::pc_Stop(this);
}



} // namespace Player
} // namespace RFOnline
