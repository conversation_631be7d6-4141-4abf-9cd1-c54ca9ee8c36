/**
 * @file j_pc_GuildSelfLeaveRequestCPlayerQEAAXXZ_14000894A_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_GuildSelfLeaveRequestCPlayerQEAAXXZ_14000894A
 * @note Address: 0x14000894A
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_GuildSelfLeaveRequest@CPlayer@@QEAAXXZ
 *Address: 0x14000894A
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_GuildSelfLeaveRequest(CPlayer *this)
{
  CPlayer::pc_GuildSelfLeaveRequest(this);
}



} // namespace Player
} // namespace RFOnline
