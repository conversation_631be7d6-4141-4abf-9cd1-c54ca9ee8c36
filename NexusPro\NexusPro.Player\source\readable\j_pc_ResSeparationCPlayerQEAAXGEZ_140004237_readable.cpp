/**
 * @file j_pc_ResSeparationCPlayerQEAAXGEZ_140004237_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_ResSeparationCPlayerQEAAXGEZ_140004237
 * @note Address: 0x140004237
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_ResSeparation@CPlayer@@QEAAXGE@Z
 *Address: 0x140004237
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_ResSeparation(CPlayer *this, unsigned __int16 wStartSerial, char byMoveAmount)
{
  CPlayer::pc_ResSeparation(this, wStartSerial, byMoveAmount);
}



} // namespace Player
} // namespace RFOnline
