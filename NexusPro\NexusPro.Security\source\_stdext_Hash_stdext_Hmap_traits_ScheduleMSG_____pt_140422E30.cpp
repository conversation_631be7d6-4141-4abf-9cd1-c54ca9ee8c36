﻿/*
 *Function: _stdext::_Hash_stdext::_Hmap_traits_ScheduleMSG_____ptr64_unsigned_long_stdext::hash_compare_ScheduleMSG_____ptr64_std::less_ScheduleMSG_____ptr64____std::allocator_std::pair_ScheduleMSG_____ptr64_const_unsigned_long____0___::lower_bound_::_1_::dtor$2
 *Address: 0x140422E30
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// Additional STL includes for compilation
#include <iterator>
#include <list>
#include <memory>
#include <utility>



void __fastcall stdext::_Hash_stdext::_Hmap_traits_ScheduleMSG_____ptr64_unsigned_long_stdext::hash_compare_ScheduleMSG_____ptr64_std::less_ScheduleMSG_____ptr64____std::allocator_std::pair_ScheduleMSG_____ptr64_const_unsigned_long____0___::lower_bound_::_1_::dtor_2(__int64 a1, __int64 a2)
{
  if(*((D WO RD*)a2 + 144) &1 )
  {
    *((DWORD*)a2 + 144) &= 0xFFFFFFFE;
    std::list<std::pair<ScheduleMSG*const,unsigned long>,std::allocator<std::pair<ScheduleMSG*const,unsigned long > >>::_Iterator<0>::~_Iterator<0>((std::list<std::pair<ScheduleMSG*const,unsigned long>,std::allocator<std::pair<ScheduleMSG*const,unsigned long>  > >::_Iterator<0> *)(a2 + 120));
  }
}



