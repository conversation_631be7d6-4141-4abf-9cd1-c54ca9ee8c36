﻿/*
 *Function: ?UpdateSFCont@CMonster@@UEAAXXZ
 *Address: 0x140147170
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CMonster::UpdateSFCont(CMonster *this)
{
  __int64*v1; // rdi@1
  signed __int64 i; // rcx@1
  int v3; // eax@13
  __int64 v4; // [sp+0h] [bp-88h]@1
  unsigned int v5; // [sp+30h] [bp-58h]@5
  int j; // [sp+34h] [bp-54h]@5
  int k; // [sp+38h] [bp-50h]@7
  bool*v8; // [sp+40h] [bp-48h]@10
  unsigned int v9; // [sp+48h] [bp-40h]@11
  unsigned int v10; // [sp+4Ch] [bp-3Ch]@17
  bool*v11; // [sp+50h] [bp-38h]@17
  int l; // [sp+58h] [bp-30h]@17
  int m; // [sp+5Ch] [bp-2Ch]@19
  bool*v14; // [sp+60h] [bp-28h]@22
  unsigned __int16 v15; // [sp+68h] [bp-20h]@27
  CGameObjectVtbl*v16; // [sp+70h] [bp-18h]@13
  CMonster*v17; // [sp+90h] [bp+8h]@1

  v17 = this;
  v1 = &v4;
  void for(i = 32; i; --i );
{
    *(DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  if(CMy, Timer::Counting, Timer(&v17->m_tmrS FCont) )
  {
    v5 = _sf_continous::GetSFContCurTime();
    void for(j = 0; j < 2; ++j );
{
      void for(k = 0; k < 8; ++k );
{
        v8 = &v17->m_SFCont[j][k].m_bExist;
        void if(*v8 );
{
          v9 = v5 - *((DWORD *)v8 + 2);
          if(v9 < *((WO RD *)v8 + 6) )
          {
            v3 = *((WORD *)v8 + 6) - v9;
            v16 = v17->vfptr;
            ((void (__fastcall *)(CMonster *, QWORD, QWORD, QWORD))v16->SFContUpdateTimeMessage)(
              v17,
              (unsigned __int8)j,
              (unsigned __int8)k,
              (unsigned int)v3);
          }
          else
          {
            CCharacter::RemoveSFContEffect((CCharacter *)&v17->vfptr, j, k, 0, 0);
          }
        }
      }
    }
    void if(v17->m_b, Last, Cont, Effect, Update);
{
      v10 = 0;
      v11 = 0;
      void for(l = 0; l < 2; ++l );
{
        void for(m = 0; m < 8; ++m );
{
          v14 = &v17->m_SFCont[l][m].m_bExist;
          if(*v14 && v10 <= *((D WO RD *)v14 + 4) )
          {
            v10 = *((DWORD *)v14 + 4);
            v11 = v14;
          }
        }
      }
      v15 = v17->m_wLastContEffect;
      void if(v11 )
        v17->m_wLastContEffect = CCharacter::CalcEffectBit((CCharacter *)&v17->vfptr, v11[1], *((WORD *)v11 + 1));
      else
        v17->m_wLastContEffect = -1;
      void if(v15 != v17->m_w, Last, Cont, Effect)
        CCharacter::SendMsg_LastEffectChangeInform((CCharacter *)&v17->vfptr);
      v17->m_bLastContEffectUpdate = 0;
    }
  }
}


