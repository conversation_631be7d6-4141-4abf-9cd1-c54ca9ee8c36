/**
 * @file j_SF_STInc_OnceCGameObjectUEAA_NPEAVCCharacterMZ_14000C987_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_SF_STInc_OnceCGameObjectUEAA_NPEAVCCharacterMZ_14000C987
 * @note Address: 0x14000C987
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?SF_STInc_Once@CGameObject@@UEAA_NPEAVCCharacter@@M@Z
 *Address: 0x14000C987
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


bool __fastcall CGameObject::SF_STInc_Once(CGameObject *this, CCharacter *pDstObj, float fEffectValue)
{
  return CGameObject::SF_STInc_Once(this, pDstObj, fEffectValue);
}



} // namespace Player
} // namespace RFOnline
