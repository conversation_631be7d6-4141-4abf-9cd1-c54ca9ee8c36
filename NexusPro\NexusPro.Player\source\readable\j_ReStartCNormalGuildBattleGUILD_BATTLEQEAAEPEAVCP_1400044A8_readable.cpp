/**
 * @file j_ReStartCNormalGuildBattleGUILD_BATTLEQEAAEPEAVCP_1400044A8_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_ReStartCNormalGuildBattleGUILD_BATTLEQEAAEPEAVCP_1400044A8
 * @note Address: 0x1400044A8
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?ReStart@CNormalGuildBattle@GUILD_BATTLE@@QEAAEPEAVCPlayer@@KK@Z
 *Address: 0x1400044A8
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


char __fastcall GUILD_BATTLE::CNormalGuildBattle::ReStart(GUILD_BATTLE::CNormalGuildBattle *this, CPlayer *pkPlayer, unsigned int dwGuildSerial, unsigned int dwCharacSerial)
{
  return GUILD_BATTLE::CNormalGuildBattle::ReStart(this, pkPlayer, dwGuildSerial, dwCharacSerial);
}



} // namespace Player
} // namespace RFOnline
