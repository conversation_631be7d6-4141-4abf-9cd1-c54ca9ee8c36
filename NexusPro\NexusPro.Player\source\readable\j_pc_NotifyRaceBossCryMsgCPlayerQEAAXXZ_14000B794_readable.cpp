/**
 * @file j_pc_NotifyRaceBossCryMsgCPlayerQEAAXXZ_14000B794_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_NotifyRaceBossCryMsgCPlayerQEAAXXZ_14000B794
 * @note Address: 0x14000B794
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_NotifyRaceBossCryMsg@CPlayer@@QEAAXXZ
 *Address: 0x14000B794
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_NotifyRaceBossCryMsg(CPlayer *this)
{
  CPlayer::pc_NotifyRaceBossCryMsg(this);
}



} // namespace Player
} // namespace RFOnline
