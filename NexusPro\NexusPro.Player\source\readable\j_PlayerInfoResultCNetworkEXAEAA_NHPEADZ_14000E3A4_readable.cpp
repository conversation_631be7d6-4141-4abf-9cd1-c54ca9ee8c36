/**
 * @file j_PlayerInfoResultCNetworkEXAEAA_NHPEADZ_14000E3A4_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_PlayerInfoResultCNetworkEXAEAA_NHPEADZ_14000E3A4
 * @note Address: 0x14000E3A4
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?PlayerInfoResult@CNetworkEX@@AEAA_NHPEAD@Z
 *Address: 0x14000E3A4
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


bool __fastcall CNetworkEX::PlayerInfoResult(CNetworkEX *this, int n, char *pBuf)
{
  return CNetworkEX::PlayerInfoResult(this, n, pBuf);
}



} // namespace Player
} // namespace RFOnline
