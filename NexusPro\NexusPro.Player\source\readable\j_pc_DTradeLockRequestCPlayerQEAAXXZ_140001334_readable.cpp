/**
 * @file j_pc_DTradeLockRequestCPlayerQEAAXXZ_140001334_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_DTradeLockRequestCPlayerQEAAXXZ_140001334
 * @note Address: 0x140001334
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_DTradeLockRequest@CPlayer@@QEAAXXZ
 *Address: 0x140001334
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_DTradeLockRequest(CPlayer *this)
{
  CPlayer::pc_DTradeLockRequest(this);
}



} // namespace Player
} // namespace RFOnline
