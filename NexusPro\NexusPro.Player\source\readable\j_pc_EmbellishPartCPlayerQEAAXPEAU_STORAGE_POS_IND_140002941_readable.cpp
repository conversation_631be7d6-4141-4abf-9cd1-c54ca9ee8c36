/**
 * @file j_pc_EmbellishPartCPlayerQEAAXPEAU_STORAGE_POS_IND_140002941_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_EmbellishPartCPlayerQEAAXPEAU_STORAGE_POS_IND_140002941
 * @note Address: 0x140002941
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_EmbellishPart@CPlayer@@QEAAXPEAU_STORAGE_POS_INDIV@@G@Z
 *Address: 0x140002941
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_EmbellishPart(CPlayer *this, _STORAGE_POS_INDIV *pItem, unsigned __int16 wChangeSerial)
{
  CPlayer::pc_EmbellishPart(this, pItem, wChangeSerial);
}



} // namespace Player
} // namespace RFOnline
