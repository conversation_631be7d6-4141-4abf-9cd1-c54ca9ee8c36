/**
 * @file j__TrapReturnCPlayerQEAAXPEAVCTrapGZ_1400060FF_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j__TrapReturnCPlayerQEAAXPEAVCTrapGZ_1400060FF
 * @note Address: 0x1400060FF
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?_TrapReturn@CPlayer@@QEAAXPEAVCTrap@@G@Z
 *Address: 0x1400060FF
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::_TrapReturn(CPlayer *this, CTrap *pTrap, unsigned __int16 wAddSerial)
{
  CPlayer::_TrapReturn(this, pTrap, wAddSerial);
}



} // namespace Player
} // namespace RFOnline
