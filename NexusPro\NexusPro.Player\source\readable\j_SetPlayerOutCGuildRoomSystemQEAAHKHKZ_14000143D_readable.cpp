/**
 * @file j_SetPlayerOutCGuildRoomSystemQEAAHKHKZ_14000143D_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_SetPlayerOutCGuildRoomSystemQEAAHKHKZ_14000143D
 * @note Address: 0x14000143D
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?SetPlayerOut@CGuildRoomSystem@@QEAAHKHK@Z
 *Address: 0x14000143D
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


int __fastcall CGuildRoomSystem::SetPlayerOut(CGuildRoomSystem *this, unsigned int dwGuildSerial, int n, unsigned int dwCharSerial)
{
  return CGuildRoomSystem::SetPlayerOut(this, dwGuildSerial, n, dwCharSerial);
}



} // namespace Player
} // namespace RFOnline
