/**
 * @file j_pc_UnitFrameRepairRequestCPlayerQEAAXEHZ_14000201D_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_UnitFrameRepairRequestCPlayerQEAAXEHZ_14000201D
 * @note Address: 0x14000201D
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_UnitFrameRepairRequest@CPlayer@@QEAAXEH@Z
 *Address: 0x14000201D
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_UnitFrameRepairRequest(CPlayer *this, char bySlotIndex, int bUseNPCLinkIntem)
{
  CPlayer::pc_UnitFrameRepairRequest(this, bySlotIndex, bUseNPCLinkIntem);
}



} // namespace Player
} // namespace RFOnline
