/**
 * @file j_SearchNearEnemyCGuardTowerQEAAPEAVCCharacterXZ_14000EFAC_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_SearchNearEnemyCGuardTowerQEAAPEAVCCharacterXZ_14000EFAC
 * @note Address: 0x14000EFAC
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?SearchNearEnemy@CGuardTower@@QEAAPEAVCCharacter@@XZ
 *Address: 0x14000EFAC
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


CCharacter *__fastcall CGuardTower::SearchNearEnemy(CGuard, Tower *this)
{
  return CGuardTower::SearchNearEnemy(this);
}



} // namespace Player
} // namespace RFOnline
