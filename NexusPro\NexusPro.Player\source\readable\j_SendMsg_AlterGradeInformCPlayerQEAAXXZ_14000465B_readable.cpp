/**
 * @file j_SendMsg_AlterGradeInformCPlayerQEAAXXZ_14000465B_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_SendMsg_AlterGradeInformCPlayerQEAAXXZ_14000465B
 * @note Address: 0x14000465B
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?SendMsg_AlterGradeInform@CPlayer@@QEAAXXZ
 *Address: 0x14000465B
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::SendMsg_AlterGradeInform(CPlayer *this)
{
  CPlayer::SendMsg_AlterGradeInform(this);
}



} // namespace Player
} // namespace RFOnline
