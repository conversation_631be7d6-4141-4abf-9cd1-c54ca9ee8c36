/**
 * @file j_SF_StunCPlayerUEAA_NPEAVCCharacterMZ_140006C58_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_SF_StunCPlayerUEAA_NPEAVCCharacterMZ_140006C58
 * @note Address: 0x140006C58
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?SF_Stun@CPlayer@@UEAA_NPEAVCCharacter@@M@Z
 *Address: 0x140006C58
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


bool __fastcall CPlayer::SF_Stun(CPlayer *this, CCharacter *pDstObj, float fEffectValue)
{
  return CPlayer::SF_Stun(this, pDstObj, fEffectValue);
}



} // namespace Player
} // namespace RFOnline
