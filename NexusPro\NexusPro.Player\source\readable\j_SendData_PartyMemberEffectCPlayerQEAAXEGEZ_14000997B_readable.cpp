/**
 * @file j_SendData_PartyMemberEffectCPlayerQEAAXEGEZ_14000997B_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_SendData_PartyMemberEffectCPlayerQEAAXEGEZ_14000997B
 * @note Address: 0x14000997B
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?SendData_PartyMemberEffect@CPlayer@@QEAAXEGE@Z
 *Address: 0x14000997B
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::SendData_PartyMemberEffect(CPlayer *this, char byAlterCode, unsigned __int16 wEffectCode, char byLv)
{
  CPlayer::SendData_PartyMemberEffect(this, byAlterCode, wEffectCode, byLv);
}



} // namespace Player
} // namespace RFOnline
