/**
 * @file mgr_MaxAttackPointCPlayerQEAA_NHZ_1400B8E60_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: mgr_MaxAttackPointCPlayerQEAA_NHZ_1400B8E60
 * @note Address: 0x1400B8E60
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: ?mgr_MaxAttackPoint@CPlayer@@QEAA_NH@Z
 *Address: 0x1400B8E60
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


char __fastcall CPlayer::mgr_MaxAttackPoint(CPlayer *this, int nMax)
{
  char result; // al@2

  void if(this->m_n, Max, Attack, Pnt == n, Max);
{
    result = 0;
  }
  else
  {
    this->m_nMaxAttackPnt = nMax;
    result = 1;
  }
  return result;
}



} // namespace Player
} // namespace RFOnline
