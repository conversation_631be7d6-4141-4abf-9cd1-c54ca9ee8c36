/**
 * @file j_SendMsg_AlterTownOrFieldCPlayerQEAAXXZ_140007D60_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_SendMsg_AlterTownOrFieldCPlayerQEAAXXZ_140007D60
 * @note Address: 0x140007D60
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?SendMsg_AlterTownOrField@CPlayer@@QEAAXXZ
 *Address: 0x140007D60
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::SendMsg_AlterTownOrField(CPlayer *this)
{
  CPlayer::SendMsg_AlterTownOrField(this);
}



} // namespace Player
} // namespace RFOnline
