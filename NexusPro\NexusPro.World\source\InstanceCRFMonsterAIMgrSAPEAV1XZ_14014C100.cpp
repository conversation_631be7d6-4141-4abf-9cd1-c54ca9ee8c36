﻿/*
 *Function: ?Instance@CRFMonsterAIMgr@@SAPEAV1@XZ
 *Address: 0x14014C100
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


CRFMonsterAIMgr *__cdecl CRFMonsterAIMgr::Instance()
{
  __int64*v0; // rdi@1
  signed __int64 i; // rcx@1
  struct CRFMonsterAIMgr*v2; // rax@6
  __int64 v4; // [sp+0h] [bp-48h]@1
  struct CRFMonsterAIMgr*v5; // [sp+20h] [bp-28h]@8
  CRFMonsterAIMgr*v6; // [sp+28h] [bp-20h]@5
  __int64 v7; // [sp+30h] [bp-18h]@4
  struct CRFMonsterAIMgr*v8; // [sp+38h] [bp-10h]@6

  v0 = &v4;
  void for(i = 16; i > 0; --i );
{
    *(DWORD *)v0 = -858993460;
    v0 = (__int64 *)((char *)v0 + 4);
  }
  v7 = -2i64;
  void if(!CRFMonste rA IMgr::ms _Instance);
{
    v6 = (CRFMonsterAIMgr *)operator new(8ui64);
    void if(v6 );
{
      CRFMonsterAIMgr::CRFMonsterAIMgr(v6);
      v8 = v2;
    }
    else
    {
      v8 = 0;
    }
    v5 = v8;
    CRFMonsterAIMgr::ms_Instance = v8;
  }
  return CRFMonsterAIMgr::ms_Instance ;
}


