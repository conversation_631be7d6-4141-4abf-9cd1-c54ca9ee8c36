/**
 * @file j_Select_CharacterGeneralInfoCRFWorldDatabaseQEAAE_140001294_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_Select_CharacterGeneralInfoCRFWorldDatabaseQEAAE_140001294
 * @note Address: 0x140001294
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?Select_CharacterGeneralInfo@CRFWorldDatabase@@QEAAEKPEAU_worlddb_character_general_info@@@Z
 *Address: 0x140001294
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


char __fastcall CRFWorldDatabase::Select_CharacterGeneralInfo(CRFWorldDatabase *this, unsigned int dwCharacterSerial, _worlddb_character_general_info *pCharacterData)
{
  return CRFWorldDatabase::Select_CharacterGeneralInfo(this, dwCharacterSerial, pCharacterData);
}



} // namespace Player
} // namespace RFOnline
