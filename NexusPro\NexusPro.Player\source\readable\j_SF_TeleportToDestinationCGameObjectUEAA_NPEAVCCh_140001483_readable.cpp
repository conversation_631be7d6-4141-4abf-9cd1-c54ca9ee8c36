/**
 * @file j_SF_TeleportToDestinationCGameObjectUEAA_NPEAVCCh_140001483_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_SF_TeleportToDestinationCGameObjectUEAA_NPEAVCCh_140001483
 * @note Address: 0x140001483
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?SF_TeleportToDestination@CGameObject@@UEAA_NPEAVCCharacter@@_N@Z
 *Address: 0x140001483
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


bool __fastcall CGameObject::SF_TeleportToDestination(CGameObject *this, CCharacter *pDstObj, bool bStone)
{
  return CGameObject::SF_TeleportToDestination(this, pDstObj, bStone);
}



} // namespace Player
} // namespace RFOnline
