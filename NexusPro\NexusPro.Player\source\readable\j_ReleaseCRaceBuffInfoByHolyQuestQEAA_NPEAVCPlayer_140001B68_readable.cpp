/**
 * @file j_ReleaseCRaceBuffInfoByHolyQuestQEAA_NPEAVCPlayer_140001B68_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_ReleaseCRaceBuffInfoByHolyQuestQEAA_NPEAVCPlayer_140001B68
 * @note Address: 0x140001B68
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?Release@CRaceBuffInfoByHolyQuest@@QEAA_NPEAVCPlayer@@@Z
 *Address: 0x140001B68
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


bool __fastcall CRaceBuffInfoByHolyQuest::Release(CRaceBuffInfoByHolyQuest *this, CPlayer *pkDest)
{
  return CRaceBuffInfoByHolyQuest::Release(this, pkDest);
}



} // namespace Player
} // namespace RFOnline
