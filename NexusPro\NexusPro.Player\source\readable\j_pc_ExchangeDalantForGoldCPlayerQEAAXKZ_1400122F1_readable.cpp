/**
 * @file j_pc_ExchangeDalantForGoldCPlayerQEAAXKZ_1400122F1_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_ExchangeDalantForGoldCPlayerQEAAXKZ_1400122F1
 * @note Address: 0x1400122F1
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_ExchangeDalantForGold@CPlayer@@QEAAXK@Z
 *Address: 0x1400122F1
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_ExchangeDalantForGold(CPlayer *this, unsigned int dwDalant)
{
  CPlayer::pc_ExchangeDalantForGold(this, dwDalant);
}



} // namespace Player
} // namespace RFOnline
