﻿/*
 *Function: ??0CHolyStoneSystem@@QEAA@XZ
 *Address: 0x14027A780
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"
#include <vector>


void __fastcall CHolyStoneSystem::CHolyStoneSystem(CHoly, Stone, System *this)
{
  __int64*v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  __int64 v4; // [sp+20h] [bp-18h]@4
  CHolyStoneSystem*v5; // [sp+40h] [bp+8h]@1

  v5 = this;
  v1 = &v3;
  void for(i = 12; i > 0; --i );
{
    *(DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v4 = -2i64;
  CRecordData::CRecordData(&v5->m_tbl, Quest);
  CLogFile::CLogFile(&v5->m_log, Quest);
  CLogFile::CLogFile(&v5->m_log, Quest, Destroy);
  CLogFile::CLogFile(&v5->m_log, Per1 0Min);
  CMyTimer::CMyTimer(&v5->m_tmr HS KSystem);
  __holy_keeper_data::__holy_keeper_data(&v5->m _Holy, Keeper, Data);
  `std::vector constructor iterator'(
    v5->m_HolyStoneData,
    0xB8ui64,
    3,
    (void *((__cdecl*)void *))__holy_stone_data::__holy_stone_data);
  CHolyScheduleData::CHolyScheduleData(&v5->m _Schecule, Data);
  CMyTimer::CMyTimer(&v5->m_tmr, Cum, Player);
  `std::vector constructor iterator'(v5->m_cashQuest, 0x14ui64, 5064, (void *((__cdecl*)void *))_QUEST_CASH::_QUEST_CASH);
  `std::vector constructor iterator'(
    v5->m_cashQuestOther,
    8ui64,
    5064,
    (void *((__cdecl*)void *))_QUEST_CASH_OTHER::_QUEST_CASH_OTHER);
  CMyTimer::BeginTimer(&v5->m_tmrCumPlayer, 0xEA60u);
  CMyTimer::BeginTimer(&v5->m_tmrHSKSystem, 0x3E8u);
  v5->m_pkDestroyer = 0;
  v5->m_dwNextStartTime = 0;
  v5->m_bConsumable = 0;
  v5->m_pMentalPass = 1;
}


