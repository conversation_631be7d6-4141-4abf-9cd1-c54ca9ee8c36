/**
 * @file j_pc_RequestDialogWithNPCCPlayerQEAAXPEAVCItemStor_140010FA0_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_RequestDialogWithNPCCPlayerQEAAXPEAVCItemStor_140010FA0
 * @note Address: 0x140010FA0
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_RequestDialogWithNPC@CPlayer@@QEAAXPEAVCItemStore@@@Z
 *Address: 0x140010FA0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_RequestDialogWithNPC(CPlayer *this, CItemStore *pStore)
{
  CPlayer::pc_RequestDialogWithNPC(this, pStore);
}



} // namespace Player
} // namespace RFOnline
