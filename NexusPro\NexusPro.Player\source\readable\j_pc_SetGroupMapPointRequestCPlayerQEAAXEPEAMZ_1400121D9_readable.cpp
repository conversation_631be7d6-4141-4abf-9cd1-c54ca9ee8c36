/**
 * @file j_pc_SetGroupMapPointRequestCPlayerQEAAXEPEAMZ_1400121D9_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_SetGroupMapPointRequestCPlayerQEAAXEPEAMZ_1400121D9
 * @note Address: 0x1400121D9
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_SetGroupMapPointRequest@CPlayer@@QEAAXEPEAM@Z
 *Address: 0x1400121D9
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_SetGroupMapPointRequest(CPlayer *this, char byGroupType, float *pzTar)
{
  CPlayer::pc_SetGroupMapPointRequest(this, byGroupType, pzTar);
}



} // namespace Player
} // namespace RFOnline
