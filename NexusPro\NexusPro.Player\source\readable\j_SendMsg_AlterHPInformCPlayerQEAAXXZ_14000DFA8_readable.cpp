/**
 * @file j_SendMsg_AlterHPInformCPlayerQEAAXXZ_14000DFA8_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_SendMsg_AlterHPInformCPlayerQEAAXXZ_14000DFA8
 * @note Address: 0x14000DFA8
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?SendMsg_AlterHPInform@CPlayer@@QEAAXXZ
 *Address: 0x14000DFA8
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::SendMsg_AlterHPInform(CPlayer *this)
{
  CPlayer::SendMsg_AlterHPInform(this);
}



} // namespace Player
} // namespace RFOnline
