/**
 * @file j_Select_CharacterBaseInfoByNameCRFWorldDatabaseQE_140008571_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_Select_CharacterBaseInfoByNameCRFWorldDatabaseQE_140008571
 * @note Address: 0x140008571
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?Select_CharacterBaseInfoByName@CRFWorldDatabase@@QEAAEPEADPEAU_worlddb_character_base_info@@@Z
 *Address: 0x140008571
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


char __fastcall CRFWorldDatabase::Select_CharacterBaseInfoByName(CRFWorldDatabase *this, char *pwsz<PERSON><PERSON>cterName, _worlddb_character_base_info *pCharacterData)
{
  return CRFWorldDatabase::Select_CharacterBaseInfoByName(this, pwszCharacterName, pCharacterData);
}



} // namespace Player
} // namespace RFOnline
