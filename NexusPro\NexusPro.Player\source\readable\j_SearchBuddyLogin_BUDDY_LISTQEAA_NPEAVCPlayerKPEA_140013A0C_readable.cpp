/**
 * @file j_SearchBuddyLogin_BUDDY_LISTQEAA_NPEAVCPlayerKPEA_140013A0C_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_SearchBuddyLogin_BUDDY_LISTQEAA_NPEAVCPlayerKPEA_140013A0C
 * @note Address: 0x140013A0C
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?SearchBuddyLogin@_BUDDY_LIST@@QEAA_NPEAVCPlayer@@KPEAD@Z
 *Address: 0x140013A0C
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


bool __fastcall _BUDDY_LIST::SearchBuddyLogin(_BUDDY_LIST *this, CPlayer *pLoger, unsigned int dwSerial, char *pwszName)
{
  return _BUDDY_LIST::SearchBuddyLogin(this, pLoger, dwSerial, pwszName);
}



} // namespace Player
} // namespace RFOnline
