/**
 * @file j_SF_SPDecCGameObjectUEAA_NPEAVCCharacterMZ_14000A84E_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_SF_SPDecCGameObjectUEAA_NPEAVCCharacterMZ_14000A84E
 * @note Address: 0x14000A84E
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?SF_SPDec@CGameObject@@UEAA_NPEAVCCharacter@@M@Z
 *Address: 0x14000A84E
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


bool __fastcall CGameObject::SF_SPDec(CGameObject *this, CCharacter *pDstObj, float fEffectValue)
{
  return CGameObject::SF_SPDec(this, pDstObj, fEffectValue);
}



} // namespace Player
} // namespace RFOnline
