/**
 * @file j_SendData_PartyMemberInfoCPlayerQEAAXGZ_14000CA0E_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_SendData_PartyMemberInfoCPlayerQEAAXGZ_14000CA0E
 * @note Address: 0x14000CA0E
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?SendData_PartyMemberInfo@CPlayer@@QEAAXG@Z
 *Address: 0x14000CA0E
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::SendData_PartyMemberInfo(CPlayer *this, unsigned __int16 wDstIndex)
{
  CPlayer::SendData_PartyMemberInfo(this, wDstIndex);
}



} // namespace Player
} // namespace RFOnline
