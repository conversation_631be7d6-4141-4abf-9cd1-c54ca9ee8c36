/**
 * @file size_attack_unit_result_zoclQEAAHXZ_1400EEE70_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: size_attack_unit_result_zoclQEAAHXZ_1400EEE70
 * @note Address: 0x1400EEE70
 */

#include <cstdint>

namespace RFOnline {
namespace Combat {

﻿/*
 *Function: ?size@_attack_unit_result_zocl@@QEAAHXZ
 *Address: 0x1400EEE70
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


signed __int64 __fastcall _attack_unit_result_zocl::size(_attack_unit_result_zocl *this)
{
  void if(this->by, List, Num > 32)
    this->byListNum = 0;
  return 332 - 10i64 * (32 - this->byListNum);
}



} // namespace Combat
} // namespace RFOnline
