/**
 * @file j_pc_UseRecoverLossExpItemCPlayerQEAADGZ_14001359D_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_UseRecoverLossExpItemCPlayerQEAADGZ_14001359D
 * @note Address: 0x14001359D
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_UseRecoverLossExpItem@CPlayer@@QEAADG@Z
 *Address: 0x14001359D
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


char __fastcall CPlayer::pc_UseRecoverLossExpItem(CPlayer *this, unsigned __int16 wItemSerial)
{
  return CPlayer::pc_UseRecoverLossExpItem(this, wItemSerial);
}



} // namespace Player
} // namespace RFOnline
