/**
 * @file j_SellCompleteCUnmannedTraderUserInfoQEAAEPEAVCPla_140004E5D_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_SellCompleteCUnmannedTraderUserInfoQEAAEPEAVCPla_140004E5D
 * @note Address: 0x140004E5D
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?SellComplete@CUnmannedTraderUserInfo@@QEAAEPEAVCPlayer@@0KKKK_JPEAVCLogFile@@@Z
 *Address: 0x140004E5D
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


char __fastcall CUnmannedTraderUserInfo::SellComplete(CUnmannedTraderUserInfo *this, CPlayer *pkSellPlayer, CPlayer *pkBuyer, unsigned int dwOriPrice, unsigned int dwRealPrice, unsigned int dwTax, unsigned int dwRegistSerial, __int64 tResultTime, CLogFile *pkLogger)
{
  return CUnmannedTraderUserInfo::SellComplete(
           this,
           pkSellPlayer,
           pkBuyer,
           dwOriPrice,
           dwRealPrice,
           dwTax,
           dwRegistSerial,
           tResultTime,
           pkLogger);
}



} // namespace Player
} // namespace RFOnline
