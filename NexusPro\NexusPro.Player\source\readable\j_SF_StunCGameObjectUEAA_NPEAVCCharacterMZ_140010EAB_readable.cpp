/**
 * @file j_SF_StunCGameObjectUEAA_NPEAVCCharacterMZ_140010EAB_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_SF_StunCGameObjectUEAA_NPEAVCCharacterMZ_140010EAB
 * @note Address: 0x140010EAB
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?SF_Stun@CGameObject@@UEAA_NPEAVCCharacter@@M@Z
 *Address: 0x140010EAB
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


bool __fastcall CGameObject::SF_Stun(CGameObject *this, CCharacter *pDstObj, float fEffectValue)
{
  return CGameObject::SF_Stun(this, pDstObj, fEffectValue);
}



} // namespace Player
} // namespace RFOnline
