﻿/*
 *Function: ?SymmetricDecrypt@?$DL_EncryptionAlgorithm_Xor@V?$HMAC@VSHA1@CryptoPP@@@CryptoPP@@$00@CryptoPP@@UEBA?AUDecodingResult@2@PEBE0_KPEAEAEBVNameValuePairs@2@@Z
 *Address: 0x14063D040
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"
#include <cstring>


CryptoPP::DecodingResult *__fastcall CryptoPP::DL_EncryptionAlgorithm_Xor<CryptoPP::HMAC<CryptoPP::SHA1>,1 >::SymmetricDecrypt(__int64 a1, CryptoPP *a2, CryptoPP::NameValuePairs *a3, unsigned __int8 *a4, __int64 a5, CryptoPP *a6, CryptoPP::NameValuePairs *a7)
{
  const unsigned __int8*v7; // rax@1
  const char*v8; // rax@1
  const unsigned __int8*v9; // rax@1
  unsigned int v10; // eax@1
  CryptoPP::DecodingResult*result; // rax@2
  char*xorBlock; // [sp+20h] [bp-178h]@1
  const unsigned __int8*len; // [sp+30h] [bp-168h]@1
  CryptoPP::ConstByteArrayParameter value; // [sp+40h] [bp-158h]@1
  CryptoPP::HMAC<CryptoPP::SHA1> v15; // [sp+70h] [bp-128h]@1
  char*key; // [sp+160h] [bp-38h]@1
  unsigned __int8 v17; // [sp+170h] [bp-28h]@1
  char v18; // [sp+171h] [bp-27h]@1
  char v19; // [sp+172h] [bp-26h]@1
  char v20; // [sp+173h] [bp-25h]@1
  char block; // [sp+174h] [bp-24h]@1
  __int64 v22; // [sp+178h] [bp-20h]@1
  unsigned __int64 v23; // [sp+180h] [bp-18h]@1
  CryptoPP::DecodingResult*v24; // [sp+1A8h] [bp+10h]@1
  CryptoPP::NameValuePairs*v25; // [sp+1B0h] [bp+18h]@1
  unsigned __int8*v26; // [sp+1B8h] [bp+20h]@1

  v26 = a4;
  v25 = a3;
  v24 = (CryptoPP::DecodingResult *)a2;
  v22 = -2i64;
  ((DWORD)(v7) = (*(int (__fastcall **)(__int64, __int64))(*(QWORD *)a1 + 24i64))(a1, a5);
  len = v7;
  key = (char *)v25;
  CryptoPP::ConstByteArrayParameter::ConstByteArrayParameter(&value, 0i64, 0);
  v8 = CryptoPP::Name::EncodingParameters();
  CryptoPP::NameValuePairs::GetValue<CryptoPP::ConstByteArrayParameter>(a7, v8, &value);
  typename CryptoPP::HMAC<CryptoPP::SHA1>::HMAC<CryptoPP::SHA1>(&v15, key, 0x10ui64);
  CryptoPP::HMAC_Base::Update((Crypto PP::HM AC _Base *)&v15.vfptr, v26, (__int64)len);
  v23 = CryptoPP::ConstByteArrayParameter::size(&value);
  v9 = (const unsigned __int8 *)CryptoPP::ConstByteArrayParameter::begin(&value);
  CryptoPP::HMAC_Base::Update((Crypto PP::HM AC _Base *)&v15.vfptr, v9, v23);
  v17 = 0;
  v18 = 0;
  v19 = 0;
  v20 = 0;
  memset(&block, 0, 4ui64);
  v10 = CryptoPP::ConstByteArrayParameter::size(&value);
  CryptoPP::PutWord<unsigned int>(0, BIG_ENDIAN_ORDER, &block, v10, 0i64);
  CryptoPP::HMAC_Base::Update((Crypto PP::HM AC _Base *)&v15.vfptr, &v17, 8i64);
  if(Crypto PP::Hash, Transformation::Verify((Crypto PP::Hash, Transformation *)&v15.vfptr, (const char *)&v26[(QWORD)len]) )
  {
    CryptoPP::xorbuf(a6, v26, (const unsigned __int8 *)&v25[2], len, (unsigned __int64)xorBlock);
    CryptoPP::DecodingResult::DecodingResult(v24, (unsigned __int64)len);
    typename CryptoPP::HMAC<CryptoPP::SHA1>::~HMAC<CryptoPP::SHA1>(&v15);
    CryptoPP::ConstByteArrayParameter::~ConstByteArrayParameter();
    result = v24;
  }
  else
  {
    CryptoPP::DecodingResult::DecodingResult(v24);
    typename CryptoPP::HMAC<CryptoPP::SHA1>::~HMAC<CryptoPP::SHA1>(&v15);
    CryptoPP::ConstByteArrayParameter::~ConstByteArrayParameter();
    result = v24;
  }
  return result;
}


