/**
 * @file j_pc_TrunkIoMoneyRequestCPlayerQEAAXEKKZ_14000EF93_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_TrunkIoMoneyRequestCPlayerQEAAXEKKZ_14000EF93
 * @note Address: 0x14000EF93
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_TrunkIoMoneyRequest@CPlayer@@QEAAXEKK@Z
 *Address: 0x14000EF93
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_TrunkIoMoneyRequest(CPlayer *this, char byCase, unsigned int dwDalant, unsigned int dwGold)
{
  CPlayer::pc_TrunkIoMoneyRequest(this, byCase, dwDalant, dwGold);
}



} // namespace Player
} // namespace RFOnline
