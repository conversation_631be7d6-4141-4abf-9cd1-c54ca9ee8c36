/**
 * @file j_pc_ChatTradeRequestMsgCPlayerQEAAXEPEADZ_140004601_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_ChatTradeRequestMsgCPlayerQEAAXEPEADZ_140004601
 * @note Address: 0x140004601
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_ChatTradeRequestMsg@CPlayer@@QEAAXEPEAD@Z
 *Address: 0x140004601
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_ChatTradeRequestMsg(CPlayer *this, char bySubType, char *pwszTradeMsg)
{
  CPlayer::pc_ChatTradeRequestMsg(this, bySubType, pwszTradeMsg);
}



} // namespace Player
} // namespace RFOnline
