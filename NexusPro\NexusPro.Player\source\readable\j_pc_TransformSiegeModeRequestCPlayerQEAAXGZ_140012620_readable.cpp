/**
 * @file j_pc_TransformSiegeModeRequestCPlayerQEAAXGZ_140012620_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_TransformSiegeModeRequestCPlayerQEAAXGZ_140012620
 * @note Address: 0x140012620
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_TransformSiegeModeRequest@CPlayer@@QEAAXG@Z
 *Address: 0x140012620
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_TransformSiegeModeRequest(CPlayer *this, unsigned __int16 wItemSerial)
{
  CPlayer::pc_TransformSiegeModeRequest(this, wItemSerial);
}



} // namespace Player
} // namespace RFOnline
