/**
 * @file j_pc_UnitBulletReplaceRequestCPlayerQEAAXEEEZ_1400085A8_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_UnitBulletReplaceRequestCPlayerQEAAXEEEZ_1400085A8
 * @note Address: 0x1400085A8
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_UnitBulletReplaceRequest@CPlayer@@QEAAXEEE@Z
 *Address: 0x1400085A8
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_UnitBulletReplaceRequest(CPlayer *this, char bySlotIndex, char byPackIndex, char byBulletPart)
{
  CPlayer::pc_UnitBulletReplaceRequest(this, bySlotIndex, byPackIndex, byBulletPart);
}



} // namespace Player
} // namespace RFOnline
