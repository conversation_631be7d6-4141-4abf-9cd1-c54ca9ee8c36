/**
 * @file j_SF_SelfDestructionCGameObjectUEAA_NPEAVCCharacte_14000BB81_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_SF_SelfDestructionCGameObjectUEAA_NPEAVCCharacte_14000BB81
 * @note Address: 0x14000BB81
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?SF_SelfDestruction@CGameObject@@UEAA_NPEAVCCharacter@@M@Z
 *Address: 0x14000BB81
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


bool __fastcall CGameObject::SF_SelfDestruction(CGameObject *this, CCharacter *pDstObj, float fEffectValue)
{
  return CGameObject::SF_SelfDestruction(this, pDstObj, fEffectValue);
}



} // namespace Player
} // namespace RFOnline
