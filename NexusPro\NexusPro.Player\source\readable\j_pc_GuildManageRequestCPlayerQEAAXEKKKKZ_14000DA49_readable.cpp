/**
 * @file j_pc_GuildManageRequestCPlayerQEAAXEKKKKZ_14000DA49_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_GuildManageRequestCPlayerQEAAXEKKKKZ_14000DA49
 * @note Address: 0x14000DA49
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_GuildManageRequest@CPlayer@@QEAAXEKKKK@Z
 *Address: 0x14000DA49
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_GuildManageRequest(CPlayer *this, char byType, unsigned int dwDst, unsigned int dwObj1, unsigned int dwObj2, unsigned int dwObj3)
{
  CPlayer::pc_GuildManageRequest(this, byType, dwDst, dwObj1, dwObj2, dwObj3);
}



} // namespace Player
} // namespace RFOnline
