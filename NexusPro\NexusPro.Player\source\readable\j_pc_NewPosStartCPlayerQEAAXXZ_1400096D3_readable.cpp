/**
 * @file j_pc_NewPosStartCPlayerQEAAXXZ_1400096D3_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_NewPosStartCPlayerQEAAXXZ_1400096D3
 * @note Address: 0x1400096D3
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_NewPosStart@CPlayer@@QEAAXXZ
 *Address: 0x1400096D3
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_NewPosStart(CPlayer *this)
{
  CPlayer::pc_NewPosStart(this);
}



} // namespace Player
} // namespace RFOnline
