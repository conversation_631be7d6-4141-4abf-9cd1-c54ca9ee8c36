/**
 * @file j_SetPvPPointCPlayerDBQEAAXNZ_14000CBC1_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_SetPvPPointCPlayerDBQEAAXNZ_14000CBC1
 * @note Address: 0x14000CBC1
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?SetPvPPoint@CPlayerDB@@QEAAXN@Z
 *Address: 0x14000CBC1
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayerDB::SetPvPPoint(CPlayerDB *this, long double dPoint)
{
  CPlayerDB::SetPvPPoint(this, dPoint);
}



} // namespace Player
} // namespace RFOnline
