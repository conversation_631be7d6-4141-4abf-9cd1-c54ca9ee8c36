﻿/*
 *Function: _CAtlAllocator::Init_::_1_::fin$0
 *Address: 0x140674380
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


__int64 __fastcall CAtlAllocator::Init_::_1_::fin_0(__int64 a1, __int64 a2)
{
  __int64 v2; // rbp@1
  __int64 result; // rax@6

  v2 = a2;
  if(*((Q WO RD*)a2 + 48) )
  {
    *((DWORD*)a2 + 120) = SetThreadToken(0i64, *((HANDLE*)a2 + 48));
    if(!*((D WO RD*)v2 + 120)
      && CrtDbgReportW_0(
           2i64,
           (__int64)L"f:\\dd\\vctools\\vc7libs\\ship\\atlmfc\\src\\atl\\atls\\allocate.cpp",
           117i64,
           0i64) == 1 )
    {
      __debugbreak();
    }
    CloseHandle(*((HA ND LE*)v2 + 48));
  }
  result = *((BYTE*)*(QWORD *)(v2 + 176) + 64i64);
  if(!*((BY TE*)*(QWORD *)(v2 + 176) + 64i64) )
  {
    if(*((Q WO RD*)*(QWORD *)(v2 + 176) + 72i64) )
    {
      UnmapViewOfFile(*((LPC VO ID*)*(QWORD *)(v2 + 176) + 72i64));
      *((QWORD*)*(QWORD *)(v2 + 176) + 72i64) = 0;
    }
    result = *((QWORD*)v2 + 176);
    if(*((Q WO RD*)result + 56) )
    {
      CloseHandle(*((HA ND LE*)*(QWORD *)(v2 + 176) + 56i64));
      result = *((QWORD*)v2 + 176);
      *((QWORD*)result + 56) = 0;
    }
  }
  return result;
}


