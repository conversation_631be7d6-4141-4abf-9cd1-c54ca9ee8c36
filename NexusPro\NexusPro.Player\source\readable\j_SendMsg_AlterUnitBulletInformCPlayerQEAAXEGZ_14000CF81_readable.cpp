/**
 * @file j_SendMsg_AlterUnitBulletInformCPlayerQEAAXEGZ_14000CF81_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_SendMsg_AlterUnitBulletInformCPlayerQEAAXEGZ_14000CF81
 * @note Address: 0x14000CF81
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?SendMsg_AlterUnitBulletInform@CPlayer@@QEAAXEG@Z
 *Address: 0x14000CF81
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::SendMsg_AlterUnitBulletInform(CPlayer *this, char byPart, unsigned __int16 wLeftNum)
{
  CPlayer::SendMsg_AlterUnitBulletInform(this, byPart, wLeftNum);
}



} // namespace Player
} // namespace RFOnline
