/**
 * @file j_RecvKillMessageCAnimusUEAAXPEAVCCharacterZ_140004B10_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_RecvKillMessageCAnimusUEAAXPEAVCCharacterZ_140004B10
 * @note Address: 0x140004B10
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?RecvKillMessage@CAnimus@@UEAAXPEAVCCharacter@@@Z
 *Address: 0x140004B10
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CAnimus::RecvKillMessage(CAnimus *this, CCharacter *pDier)
{
  CAnimus::RecvKillMessage(this, pDier);
}



} // namespace Player
} // namespace RFOnline
