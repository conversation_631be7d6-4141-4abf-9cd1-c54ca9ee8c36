/**
 * @file j_pc_PartyReqBlockCPlayerQEAAX_NZ_14000E129_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_PartyReqBlockCPlayerQEAAX_NZ_14000E129
 * @note Address: 0x14000E129
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_PartyReqBlock@CPlayer@@QEAAX_N@Z
 *Address: 0x14000E129
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_PartyReqBlock(CPlayer *this, bool bBlock)
{
  CPlayer::pc_PartyReqBlock(this, bBlock);
}



} // namespace Player
} // namespace RFOnline
