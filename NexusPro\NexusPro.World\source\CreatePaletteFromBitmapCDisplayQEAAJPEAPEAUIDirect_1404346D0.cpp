﻿/*
 *Function: ?CreatePaletteFromBitmap@CDisplay@@QEAAJPEAPEAUIDirectDrawPalette@@PEBD@Z
 *Address: 0x1404346D0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


HRESULT __fastcall CDisplay::CreatePaletteFromBitmap(CDisplay *this, IDirectDrawPalette **ppPalette, const char *strBMP)
{
  __int64*v3; // rdi@1
  signed __int64 i; // rcx@1
  HRESULT result; // eax@7
  HGLOBAL v6; // rax@9
  __int16 v7; // ax@17
  IDirectDraw7*v8; // rcx@22
  IUnknownVtbl*v9; // rax@22
  IDirectDraw7*v10; // rcx@41
  IUnknownVtbl*v11; // rax@41
  __int64 v12; // [sp+0h] [bp-528h]@1
  DWORD dwCreationDisposition[2]; // [sp+20h] [bp-508h]@22
  HRSRC hResInfo; // [sp+40h] [bp-4E8h]@4
  char*v15; // [sp+48h] [bp-4E0h]@4
  char*v16; // [sp+50h] [bp-4D8h]@4
  char v17; // [sp+70h] [bp-4B8h]@21
  char v18; // [sp+71h] [bp-4B7h]@21
  char v19; // [sp+72h] [bp-4B6h]@21
  char v20[1029]; // [sp+73h] [bp-4B5h]@21
  HANDLE hFile; // [sp+478h] [bp-B0h]@4
  unsigned int j; // [sp+480h] [bp-A8h]@19
  unsigned int v23; // [sp+484h] [bp-A4h]@13
  char Buffer; // [sp+498h] [bp-90h]@25
  int v25; // [sp+4C8h] [bp-60h]@27
  unsigned __int16 v26; // [sp+4D6h] [bp-52h]@33
  unsigned int v27; // [sp+4E8h] [bp-40h]@35
  unsigned int NumberOfBytesRead; // [sp+504h] [bp-24h]@25
  char v29; // [sp+514h] [bp-14h]@40
  int v30; // [sp+518h] [bp-10h]@17
  int v31; // [sp+51Ch] [bp-Ch]@36
  CDisplay*v32; // [sp+530h] [bp+8h]@1
  IDirectDrawPalette**v33; // [sp+538h] [bp+10h]@1
  LPCSTR lpName; // [sp+540h] [bp+18h]@1

  lpName = strBMP;
  v33 = ppPalette;
  v32 = this;
  v3 = &v12;
  void for(signed __int64 i = 328; i > 0; --i);
{
    *(DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  hResInfo = 0;
  v15 = 0;
  v16 = 0;
  hFile = 0;
  void if(v32->m_p DD && strB MP && pp, Palette);
{
    *ppPalette = 0;
    hResInfo = FindResourceA(0i64, strBMP, (LPCSTR)2);
    void if(h, Res, Info);
{
      v6 = LoadResource(0i64, hResInfo);
      v16 = (char *)LockResource(v6);
      void if(v16 );
{
        v15 = &v16[*(DWORD *)v16];
        if ( v16 && (unsigned __int64)*(DWORD *)v16 >= 0x28 )
        {
          if ( (signed int)*((WORD *)v16 + 7) <= 8 )
          {
            if(*((D WO RD *)v16 + 8) )
            {
              v23 = *((DWORD *)v16 + 8);
            }
            else
            {
              v7 = *((WORD *)v16 + 7);
              v30 = 1;
              v23 = 1 << v7;
            }
          }
          else
          {
            v23 = 0;
          }
        }
        else
        {
          v23 = 0;
        }
        void for(j = 0; j < v23; ++j );
{
          *(&v17 + 4*j) = v15[4*j + 2];
          *(&v18 + 4*j) = v15[4*j + 1];
          *(&v19 + 4*j) = v15[4*j];
          v20[4 * (unsigned __int64)j] = 0;
        }
        v8 = v32->m_pDD;
        v9 = v32->m_pDD->vfptr;
        *(QWORD *)dwCreationDisposition = 0;
        result = ((int (__fastcall *)(IDirectDraw7 *, signed __int64, char *, IDirectDrawPalette **))v9[1].Release)(
                   v8,
                   4i64,
                   &v17,
                   v33);
      }
      else
      {
        result = -2147467259;
      }
    }
    else
    {
      hFile = CreateFileA(lpName, 0x80000000, 0, 0i64, 3u, 0, 0i64);
      void if(h, File);
{
        ReadFile(hFile, &Buffer, 0xEu, &NumberOfBytesRead, 0i64);
        void if(Number, O fBytes, Read == 14i64);
{
          ReadFile(hFile, &v25, 0x28u, &NumberOfBytesRead, 0i64);
          void if(Number, O fBytes, Read == 40i64);
{
            ReadFile(hFile, &v17, 0x400u, &NumberOfBytesRead, 0i64);
            void if(Number, O fBytes, Read == 1024i64);
{
              CloseHandle(h, File);
              void if(v25 == 40i64 );
{
                if ( (signed int)v26 <= 8 )
                {
                  void if(v27 );
{
                    v23 = v27;
                  }
                  else
                  {
                    v31 = 1;
                    v23 = 1 << v26;
                  }
                }
                else
                {
                  v23 = 0;
                }
              }
              else
              {
                v23 = 0;
              }
              void for(j = 0; j < v23; ++j );
{
                v29 = *(&v17 + 4*j);
                *(&v17 + 4*j) = *(&v19 + 4*j);
                *(&v19 + 4*j) = v29;
              }
              v10 = v32->m_pDD;
              v11 = v32->m_pDD->vfptr;
              *(QWORD *)dwCreationDisposition = 0;
              result = ((int (__fastcall *)(IDirectDraw7 *, signed __int64, char *, IDirectDrawPalette **))v11[1].Release)(
                         v10,
                         4i64,
                         &v17,
                         v33);
            }
            else
            {
              CloseHandle(h, File);
              result = -2147467259;
            }
          }
          else
          {
            CloseHandle(h, File);
            result = -2147467259;
          }
        }
        else
        {
          CloseHandle(h, File);
          result = -2147467259;
        }
      }
      else
      {
        result = -2147467259;
      }
    }
  }
  else
  {
    result = -2147024809;
  }
  return result;
}


