/**
 * @file j_pc_GuildQueryInfoRequestCPlayerQEAAXKZ_14000BD2A_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_GuildQueryInfoRequestCPlayerQEAAXKZ_14000BD2A
 * @note Address: 0x14000BD2A
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_GuildQueryInfoRequest@CPlayer@@QEAAXK@Z
 *Address: 0x14000BD2A
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_GuildQueryInfoRequest(CPlayer *this, unsigned int dwGuildSerial)
{
  CPlayer::pc_GuildQueryInfoRequest(this, dwGuildSerial);
}



} // namespace Player
} // namespace RFOnline
