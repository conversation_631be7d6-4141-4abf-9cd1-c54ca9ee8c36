/**
 * @file j_pc_PartySuccessionReqeuestCPlayerQEAAXKZ_140011C89_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_PartySuccessionReqeuestCPlayerQEAAXKZ_140011C89
 * @note Address: 0x140011C89
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_PartySuccessionReqeuest@CPlayer@@QEAAXK@Z
 *Address: 0x140011C89
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_PartySuccessionReqeuest(CPlayer *this, unsigned int dwSuccessorSerial)
{
  CPlayer::pc_PartySuccessionReqeuest(this, dwSuccessorSerial);
}



} // namespace Player
} // namespace RFOnline
