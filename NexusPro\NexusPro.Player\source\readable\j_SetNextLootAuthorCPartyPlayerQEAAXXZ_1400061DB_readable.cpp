/**
 * @file j_SetNextLootAuthorCPartyPlayerQEAAXXZ_1400061DB_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_SetNextLootAuthorCPartyPlayerQEAAXXZ_1400061DB
 * @note Address: 0x1400061DB
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?SetNextLootAuthor@CPartyPlayer@@QEAAXXZ
 *Address: 0x1400061DB
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPartyPlayer::SetNextLootAuthor(CParty, Player *this)
{
  CPartyPlayer::SetNextLootAuthor(this);
}



} // namespace Player
} // namespace RFOnline
