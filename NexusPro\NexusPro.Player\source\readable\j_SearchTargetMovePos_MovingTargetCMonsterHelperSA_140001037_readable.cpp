/**
 * @file j_SearchTargetMovePos_MovingTargetCMonsterHelperSA_140001037_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_SearchTargetMovePos_MovingTargetCMonsterHelperSA_140001037
 * @note Address: 0x140001037
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?SearchTargetMovePos_MovingTarget@CMonsterHelper@@SAHPEAVCMonster@@PEAVC<PERSON>haracter@@AEAY02M@Z
 *Address: 0x140001037
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


int __fastcall CMonsterHelper::SearchTargetMovePos_MovingTarget(CMonster *pMon, CCharacter *pTargetCharacter, float (*tarPos)[3])
{
  return CMonsterHelper::SearchTargetMovePos_MovingTarget(pMon, pTargetCharacter, tarPos);
}



} // namespace Player
} // namespace RFOnline
