/**
 * @file j_SendGetGravityStoneCNormalGuildBattleGuildGUILD__140010C3F_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_SendGetGravityStoneCNormalGuildBattleGuildGUILD__140010C3F
 * @note Address: 0x140010C3F
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?SendGetGravityStone@CNormalGuildBattleGuild@GUILD_BATTLE@@QEAAXPEAV12@PEAVCPlayer@@H@Z
 *Address: 0x140010C3F
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall GUILD_BATTLE::CNormalGuildBattleGuild::SendGetGravityStone(GUILD_BATTLE::CNormalGuildBattleGuild *this, GUILD_BATTLE::CNormalGuildBattleGuild *pkTakeGuild, CPlayer *pkPlayer, int iTakePortalInx)
{
  GUILD_BATTLE::CNormalGuildBattleGuild::SendGetGravityStone(this, pkTakeGuild, pkPlayer, iTakePortalInx);
}



} // namespace Player
} // namespace RFOnline
