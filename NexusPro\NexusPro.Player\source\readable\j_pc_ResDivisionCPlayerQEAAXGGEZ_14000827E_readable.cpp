/**
 * @file j_pc_ResDivisionCPlayerQEAAXGGEZ_14000827E_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_ResDivisionCPlayerQEAAXGGEZ_14000827E
 * @note Address: 0x14000827E
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_ResDivision@CPlayer@@QEAAXGGE@Z
 *Address: 0x14000827E
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_ResDivision(CPlayer *this, unsigned __int16 wStartSerial, unsigned __int16 wTarSerial, char byMoveAmount)
{
  CPlayer::pc_ResDivision(this, wStartSerial, wTarSerial, byMoveAmount);
}



} // namespace Player
} // namespace RFOnline
