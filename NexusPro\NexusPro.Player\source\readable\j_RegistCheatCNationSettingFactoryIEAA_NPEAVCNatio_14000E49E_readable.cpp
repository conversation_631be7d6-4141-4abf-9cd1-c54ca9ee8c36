/**
 * @file j_RegistCheatCNationSettingFactoryIEAA_NPEAVCNatio_14000E49E_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_RegistCheatCNationSettingFactoryIEAA_NPEAVCNatio_14000E49E
 * @note Address: 0x14000E49E
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?RegistCheat@CNationSettingFactory@@IEAA_NPEAVCNationSettingData@@PEBDP6A_NPEAVCPlayer@@@ZHH@Z
 *Address: 0x14000E49E
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


bool __fastcall CNationSettingFactory::RegistCheat(CNationSettingFactory *this, CNationSettingData *pkData, const char *szCheat, bool (__cdecl *pCheatCommandFn)(CPlayer *), int iUseDegree, int iMgrDegree)
{
  return CNationSettingFactory::RegistCheat(this, pkData, szCheat, pCheatCommandFn, iUseDegree, iMgrDegree);
}



} // namespace Player
} // namespace RFOnline
