/**
 * @file size_personal_automine_attacked_zoclQEAAHXZ_1402DE340_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: size_personal_automine_attacked_zoclQEAAHXZ_1402DE340
 * @note Address: 0x1402DE340
 */

#include <cstdint>

namespace RFOnline {
namespace Combat {

﻿/*
 *Function: ?size@_personal_automine_attacked_zocl@@QEAAHXZ
 *Address: 0x1402DE340
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


signed __int64 __fastcall _personal_automine_attacked_zocl::size(_personal_automine_attacked_zocl *this)
{
  return 2i64;
}



} // namespace Combat
} // namespace RFOnline
