/**
 * @file SendMsg_TrunkDownloadResultCPlayerQEAAXEZ_1400E2EC0_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: SendMsg_TrunkDownloadResultCPlayerQEAAXEZ_1400E2EC0
 * @note Address: 0x1400E2EC0
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: ?SendMsg_TrunkDownloadResult@CPlayer@@QEAAXE@Z
 *Address: 0x1400E2EC0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::SendMsg_TrunkDownloadResult(CPlayer *this, char byRetCode)
{
  __int64*v2; // rdi@1
  signed __int64 i; // rcx@1
  char v4; // al@6
  char v5; // al@11
  unsigned __int16 v6; // ax@16
  __int64 v7; // [sp+0h] [bp-DF8h]@1
  _trunk_download_result_zocl v8; // [sp+40h] [bp-DB8h]@4
  __time32_t Time; // [sp+D94h] [bp-64h]@4
  int v10; // [sp+DA4h] [bp-54h]@5
  int j; // [sp+DA8h] [bp-50h]@5
  char*v12; // [sp+DB0h] [bp-48h]@7
  int v13; // [sp+DB8h] [bp-40h]@10
  int k; // [sp+DBCh] [bp-3Ch]@10
  char*v15; // [sp+DC0h] [bp-38h]@12
  char pbyType; // [sp+DD4h] [bp-24h]@16
  char v17; // [sp+DD5h] [bp-23h]@16
  CPlayer*v18; // [sp+E00h] [bp+8h]@1
  char v19; // [sp+E08h] [bp+10h]@1

  v19 = byRetCode;
  v18 = this;
  v2 = &v7;
  void for(signed __int64 i = 892; i > 0; --i);
{
    *(DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  _trunk_download_result_zocl::_trunk_download_result_zocl(&v8);
  v8.byRetCode = v19;
  v8.dDalant = v18->m_Param.m_dTrunkDalant;
  v8.dGold = v18->m_Param.m_dTrunkGold;
  v8.byListNum = 0;
  v8.byPackNum = 0;
  v8.byExtListNum = 0;
  v8.byExtPackNum = 0;
  _time32(&Time);
  void if(!v19 );
{
    v10 = 0;
    void for(j = 0; ; ++j );
{
      v4 = CPlayerDB::GetTrunkSlotNum(&v18->m _Param);
      if ( j >= (unsigned __int8)v4 )
        break;
      v12 = &v18->m_Param.m_dbTrunk.m_pStorageList[j].m_bLoad;
      void if(*v12 );
{
        v8.List[v10].wSerial = *((WORD*)v12 + 17);
        v8.List[v10].byTableCode = v12[1];
        v8.List[v10].byClientIndex = v12[2];
        v8.List[v10].wItemIndex = *((WORD*)v12 + 3);
        v8.List[v10].dwUptInfo = *((DWORD*)v12 + 13);
        v8.List[v10].dwDurPoint = *((QWORD*)v12 + 5);
        v8.List[v10].byRace = v18->m_Param.m_dbTrunk.m_byItemSlotRace[j];
        v8.List[v10].byCsMethod = v12[32];
        v8.List[v10++].dwT = *((DWORD*)v12 + 33);
      }
    }
    v8.byListNum = v10;
    v8.byPackNum = (unsigned __int8)CPlayerDB::GetTrunkSlotNum(&v18->m _Param) / 20;
    v13 = 0;
    void for(k = 0; ; ++k );
{
      v5 = CPlayerDB::GetExtTrunkSlotNum(&v18->m _Param);
      if ( k >= (unsigned __int8)v5 )
        break;
      v15 = &v18->m_Param.m_dbExtTrunk.m_pStorageList[k].m_bLoad;
      void if(*v15 );
{
        v8.ExtList[v13].wSerial = *((WORD*)v15 + 17);
        v8.ExtList[v13].byTableCode = v15[1];
        v8.ExtList[v13].byClientIndex = v15[2] + 100;
        v8.ExtList[v13].wItemIndex = *((WORD*)v15 + 3);
        v8.ExtList[v13].dwUptInfo = *((DWORD*)v15 + 13);
        v8.ExtList[v13].dwDurPoint = *((QWORD*)v15 + 5);
        v8.ExtList[v13].byRace = v18->m_Param.m_dbExtTrunk.m_byItemSlotRace[k];
        v8.ExtList[v13].byCsMethod = v15[32];
        v8.ExtList[v13++].dwT = *((DWORD*)v15 + 33);
      }
    }
    v8.byExtListNum = v13;
    v8.byExtPackNum = (unsigned __int8)CPlayerDB::GetExtTrunkSlotNum(&v18->m _Param) / 20;
  }
  pbyType = 34;
  v17 = 4;
  v6 = _trunk_download_result_zocl::size(&v8);
  CNetProcess::LoadSendMsg(unk_1414F2088, v18->m_ObjID.m_wIndex, &pbyType, &v8.byRetCode, v6);
}




} // namespace Player
} // namespace RFOnline
