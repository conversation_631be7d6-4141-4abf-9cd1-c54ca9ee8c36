/**
 * @file j_pc_ClassSkillRequestCPlayerQEAAXGPEAU_CHRIDPEAGZ_140012EBD_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_ClassSkillRequestCPlayerQEAAXGPEAU_CHRIDPEAGZ_140012EBD
 * @note Address: 0x140012EBD
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_ClassSkillRequest@CPlayer@@QEAAXGPEAU_CHRID@@PEAG@Z
 *Address: 0x140012EBD
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_ClassSkillRequest(CPlayer *this, unsigned __int16 wSkillIndex, _CHRID *pidDst, unsigned __int16 *pConsumeSerial)
{
  CPlayer::pc_ClassSkillRequest(this, wSkillIndex, pidDst, pConsumeSerial);
}



} // namespace Player
} // namespace RFOnline
