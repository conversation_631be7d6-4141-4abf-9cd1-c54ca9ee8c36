/**
 * @file j_pc_CuttingCompleteCPlayerQEAAXEZ_140011FC7_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_CuttingCompleteCPlayerQEAAXEZ_140011FC7
 * @note Address: 0x140011FC7
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_CuttingComplete@CPlayer@@QEAAXE@Z
 *Address: 0x140011FC7
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_CuttingComplete(CPlayer *this, char byNpcRace)
{
  CPlayer::pc_CuttingComplete(this, byNpcRace);
}



} // namespace Player
} // namespace RFOnline
