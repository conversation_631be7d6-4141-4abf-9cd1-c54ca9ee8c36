/**
 * @file j_SetMstHaveEffectCPlayerQEAAXPEBU_ResourceItem_fl_14000B118_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_SetMstHaveEffectCPlayerQEAAXPEBU_ResourceItem_fl_14000B118
 * @note Address: 0x14000B118
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?SetMstHaveEffect@CPlayer@@QEAAXPEBU_ResourceItem_fld@@PEBU_db_con@_STORAGE_LIST@@_NH@Z
 *Address: 0x14000B118
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::SetMstHaveEffect(CPlayer *this, _ResourceItem_fld *pFld, _STORAGE_LIST::_db_con *pItem, bool bAdd, int nAlter)
{
  CPlayer::SetMstHaveEffect(this, pFld, pItem, bAdd, nAlter);
}



} // namespace Player
} // namespace RFOnline
