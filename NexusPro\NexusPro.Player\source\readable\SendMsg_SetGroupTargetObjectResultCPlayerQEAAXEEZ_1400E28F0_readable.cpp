/**
 * @file SendMsg_SetGroupTargetObjectResultCPlayerQEAAXEEZ_1400E28F0_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: SendMsg_SetGroupTargetObjectResultCPlayerQEAAXEEZ_1400E28F0
 * @note Address: 0x1400E28F0
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: ?SendMsg_SetGroupTargetObjectResult@CPlayer@@QEAAXEE@Z
 *Address: 0x1400E28F0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"
#include <cstring>


void __fastcall CPlayer::SendMsg_SetGroupTargetObjectResult(CPlayer *this, char byRetCode, char byGroupType)
{
  __int64*v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-88h]@1
  char szMsg; // [sp+38h] [bp-50h]@4
  char v7; // [sp+39h] [bp-4Fh]@6
  char v8; // [sp+3Ah] [bp-4Eh]@6
  char v9; // [sp+3Bh] [bp-4Dh]@6
  unsigned int v10; // [sp+3Ch] [bp-4Ch]@6
  char Dst; // [sp+40h] [bp-48h]@6
  char pbyType; // [sp+64h] [bp-24h]@7
  char v13; // [sp+65h] [bp-23h]@7
  CPlayer*v14; // [sp+90h] [bp+8h]@1
  char v15; // [sp+A0h] [bp+18h]@1

  v15 = byGroupType;
  v14 = this;
  v3 = &v5;
  void for(i = 32; i; --i );
{
    *(DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  szMsg = byRetCode;
  if(!by, Ret, Code && v14->m _Group, Target, Object[(unsigned __int8)byGroupType].pObject )
  {
    v7 = byGroupType;
    v8 = CMapData::GetMapCode(v14->m _Group, Target, Object[(unsigned __int8)byGroupType].pObject->m_pCurMap);
    v9 = v14->m_GroupTargetObject[(unsigned __int8)v15].byID;
    v10 = v14->m_GroupTargetObject[(unsigned __int8)v15].dwSerial;
    memcpy_0(&Dst, v14->m_GroupTargetObject[(unsigned __int8)v15].pObject->m_fCurPos, 0xCui64);
  }
  pbyType = 13;
  v13 = 107;
  CNetProcess::LoadSendMsg(unk_1414F2088, v14->m_ObjID.m_wIndex, &pbyType, &szMsg, 0x14u);
}




} // namespace Player
} // namespace RFOnline
