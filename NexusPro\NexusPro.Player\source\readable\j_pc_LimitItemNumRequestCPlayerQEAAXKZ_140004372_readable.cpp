/**
 * @file j_pc_LimitItemNumRequestCPlayerQEAAXKZ_140004372_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_LimitItemNumRequestCPlayerQEAAXKZ_140004372
 * @note Address: 0x140004372
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_LimitItemNumRequest@CPlayer@@QEAAXK@Z
 *Address: 0x140004372
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_LimitItemNumRequest(CPlayer *this, unsigned int dwStoreIndex)
{
  CPlayer::pc_LimitItemNumRequest(this, dwStoreIndex);
}



} // namespace Player
} // namespace RFOnline
