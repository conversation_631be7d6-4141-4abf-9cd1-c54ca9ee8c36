/**
 * @file j_pc_CombineItemExAcceptCPlayerQEAAXPEAU_combine_e_14000A8B2_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_CombineItemExAcceptCPlayerQEAAXPEAU_combine_e_14000A8B2
 * @note Address: 0x14000A8B2
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_CombineItemExAccept@CPlayer@@QEAAXPEAU_combine_ex_item_accept_request_clzo@@@Z
 *Address: 0x14000A8B2
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_CombineItemExAccept(CPlayer *this, _combine_ex_item_accept_request_clzo *pRecv)
{
  CPlayer::pc_CombineItemExAccept(this, pRecv);
}



} // namespace Player
} // namespace RFOnline
