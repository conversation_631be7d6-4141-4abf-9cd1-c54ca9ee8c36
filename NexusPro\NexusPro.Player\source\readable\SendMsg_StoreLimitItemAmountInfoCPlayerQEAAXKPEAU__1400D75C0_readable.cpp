/**
 * @file SendMsg_StoreLimitItemAmountInfoCPlayerQEAAXKPEAU__1400D75C0_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: SendMsg_StoreLimitItemAmountInfoCPlayerQEAAXKPEAU__1400D75C0
 * @note Address: 0x1400D75C0
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: ?SendMsg_StoreLimitItemAmountInfo@CPlayer@@QEAAXKPEAU_limit_amount_info@@@Z
 *Address: 0x1400D75C0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::SendMsg_StoreLimitItemAmountInfo(CPlayer *this, unsigned int dwStoreIndex, _limit_amount_info *pAmountInfo)
{
  __int64*v3; // rdi@1
  signed __int64 i; // rcx@1
  unsigned __int16 v5; // ax@7
  __int64 v6; // [sp+0h] [bp-E8h]@1
  _limit_item_num_info_zocl v7; // [sp+40h] [bp-A8h]@4
  int j; // [sp+B4h] [bp-34h]@4
  char pbyType; // [sp+C4h] [bp-24h]@7
  char v10; // [sp+C5h] [bp-23h]@7
  CPlayer*v11; // [sp+F0h] [bp+8h]@1
  unsigned int v12; // [sp+F8h] [bp+10h]@1
  _limit_amount_info*v13; // [sp+100h] [bp+18h]@1

  v13 = pAmountInfo;
  v12 = dwStoreIndex;
  v11 = this;
  v3 = &v6;
  void for(signed __int64 i = 56; i > 0; --i);
{
    *(DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  _limit_item_num_info_zocl::_limit_item_num_info_zocl(&v7);
  v7.dwStoreIndex = v12;
  void for(j = 0; j < v13->by, Item, Num; ++j);
{
    v7.LimitItemInfo[j].dwLimitItemIndex = v13->ItemInfo[j].dwLimitItemIndex;
    v7.LimitItemInfo[j].wLimitNum = v13->ItemInfo[j].wLimitNum;
  }
  v7.byLimitItemNum = v13->byItemNum;
  pbyType = 12;
  v10 = 18;
  v5 = _limit_item_num_info_zocl::size(&v7);
  CNetProcess::LoadSendMsg(unk_1414F2088, v11->m_ObjID.m_wIndex, &pbyType, (char *)&v7, v5);
}




} // namespace Player
} // namespace RFOnline
