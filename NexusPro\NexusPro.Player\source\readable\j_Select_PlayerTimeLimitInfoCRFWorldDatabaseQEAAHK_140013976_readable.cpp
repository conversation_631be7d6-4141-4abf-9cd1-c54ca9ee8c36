/**
 * @file j_Select_PlayerTimeLimitInfoCRFWorldDatabaseQEAAHK_140013976_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_Select_PlayerTimeLimitInfoCRFWorldDatabaseQEAAHK_140013976
 * @note Address: 0x140013976
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?Select_PlayerTimeLimitInfo@CRFWorldDatabase@@QEAAHKPEAKPEAE@Z
 *Address: 0x140013976
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


int __fastcall CRFWorldDatabase::Select_PlayerTimeLimitInfo(CRFWorldDatabase *this, unsigned int dwAccountSerial, unsigned int *pdwFatigue, char *pbyStatus)
{
  return CRFWorldDatabase::Select_PlayerTimeLimitInfo(this, dwAccountSerial, pdwFatigue, pbyStatus);
}



} // namespace Player
} // namespace RFOnline
