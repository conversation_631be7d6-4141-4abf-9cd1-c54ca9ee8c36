/**
 * @file j_pc_PlayAttack_SelfDestructionCPlayerQEAAXXZ_14000B5A0_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_PlayAttack_SelfDestructionCPlayerQEAAXXZ_14000B5A0
 * @note Address: 0x14000B5A0
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_PlayAttack_SelfDestruction@CPlayer@@QEAAXXZ
 *Address: 0x14000B5A0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_PlayAttack_SelfDestruction(CPlayer *this)
{
  CPlayer::pc_PlayAttack_SelfDestruction(this);
}



} // namespace Player
} // namespace RFOnline
