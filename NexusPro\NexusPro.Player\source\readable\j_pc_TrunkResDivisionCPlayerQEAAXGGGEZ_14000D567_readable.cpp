/**
 * @file j_pc_TrunkResDivisionCPlayerQEAAXGGGEZ_14000D567_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_TrunkResDivisionCPlayerQEAAXGGGEZ_14000D567
 * @note Address: 0x14000D567
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_TrunkResDivision@CPlayer@@QEAAXGGGE@Z
 *Address: 0x14000D567
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_TrunkResDivision(CPlayer *this, unsigned __int16 wStartSerial, unsigned __int16 wTarSerial, unsigned __int16 wMoveAmount, char byStorageIndex)
{
  CPlayer::pc_TrunkResDivision(this, wStartSerial, wTarSerial, wMoveAmount, byStorageIndex);
}



} // namespace Player
} // namespace RFOnline
