/**
 * @file j_pc_Chat<PERSON>ultiFarRequestCPlayerQEAAXEPEAU_w_namePE_140001014_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_ChatMultiFarRequestCPlayerQEAAXEPEAU_w_namePE_140001014
 * @note Address: 0x140001014
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_ChatMultiFarRequest@CPlayer@@QEAAXEPEAU_w_name@@PEAD@Z
 *Address: 0x140001014
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_ChatMultiFarRequest(CPlayer *this, char byDstNum, _w_name *pDstName, char *pwszMsg)
{
  CPlayer::pc_ChatMultiFarRequest(this, by<PERSON>t<PERSON>um, pDstName, pwszMsg);
}



} // namespace Player
} // namespace RFOnline
