/**
 * @file j_pc_UnitLeaveRequestCPlayerQEAAXPEAMZ_14000E94E_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_UnitLeaveRequestCPlayerQEAAXPEAMZ_14000E94E
 * @note Address: 0x14000E94E
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_UnitLeaveRequest@CPlayer@@QEAAXPEAM@Z
 *Address: 0x14000E94E
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_UnitLeaveRequest(CPlayer *this, float *pfNewPos)
{
  CPlayer::pc_UnitLeaveRequest(this, pfNewPos);
}



} // namespace Player
} // namespace RFOnline
