/**
 * @file j_RecvKillMessageCGuardTowerUEAAXPEAVCCharacterZ_140011F5E_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_RecvKillMessageCGuardTowerUEAAXPEAVCCharacterZ_140011F5E
 * @note Address: 0x140011F5E
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?RecvKillMessage@CGuardTower@@UEAAXPEAVCCharacter@@@Z
 *Address: 0x140011F5E
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CGuardTower::RecvKillMessage(CGuardTower *this, CCharacter *pDier)
{
  CGuardTower::RecvKillMessage(this, pDier);
}



} // namespace Player
} // namespace RFOnline
