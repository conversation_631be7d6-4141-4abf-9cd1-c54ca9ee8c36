/**
 * @file j_pc_ChatRaceBossRequestCPlayerQEAAXPEADZ_140013B29_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_ChatRaceBossRequestCPlayerQEAAXPEADZ_140013B29
 * @note Address: 0x140013B29
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_ChatRaceBossRequest@CPlayer@@QEAAXPEAD@Z
 *Address: 0x140013B29
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_ChatRaceBossRequest(CPlayer *this, char *pwszChatData)
{
  CPlayer::pc_ChatRaceBossRequest(this, pwszChatData);
}



} // namespace Player
} // namespace RFOnline
