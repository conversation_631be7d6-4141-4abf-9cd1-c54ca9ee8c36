/**
 * @file j_PopLinkCPlayerDBQEAAXHZ_14000CD29_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_PopLinkCPlayerDBQEAAXHZ_14000CD29
 * @note Address: 0x14000CD29
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?PopLink@CPlayerDB@@QEAAXH@Z
 *Address: 0x14000CD29
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayerDB::PopLink(CPlayerDB *this, int nLinkIndex)
{
  CPlayerDB::PopLink(this, nLinkIndex);
}



} // namespace Player
} // namespace RFOnline
