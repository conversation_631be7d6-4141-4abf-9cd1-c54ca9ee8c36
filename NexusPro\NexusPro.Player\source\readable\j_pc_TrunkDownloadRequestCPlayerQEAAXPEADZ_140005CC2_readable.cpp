/**
 * @file j_pc_TrunkDownloadRequestCPlayerQEAAXPEADZ_140005CC2_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_TrunkDownloadRequestCPlayerQEAAXPEADZ_140005CC2
 * @note Address: 0x140005CC2
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_TrunkDownloadRequest@CPlayer@@QEAAXPEAD@Z
 *Address: 0x140005CC2
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_TrunkDownloadRequest(CPlayer *this, char *pwszPassword)
{
  CPlayer::pc_TrunkDownloadRequest(this, pwszPassword);
}



} // namespace Player
} // namespace RFOnline
