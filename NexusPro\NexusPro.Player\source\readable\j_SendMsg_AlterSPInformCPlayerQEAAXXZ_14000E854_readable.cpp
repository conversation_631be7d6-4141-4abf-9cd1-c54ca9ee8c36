/**
 * @file j_SendMsg_AlterSPInformCPlayerQEAAXXZ_14000E854_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_SendMsg_AlterSPInformCPlayerQEAAXXZ_14000E854
 * @note Address: 0x14000E854
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?SendMsg_AlterSPInform@CPlayer@@QEAAXXZ
 *Address: 0x14000E854
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::SendMsg_AlterSPInform(CPlayer *this)
{
  CPlayer::SendMsg_AlterSPInform(this);
}



} // namespace Player
} // namespace RFOnline
