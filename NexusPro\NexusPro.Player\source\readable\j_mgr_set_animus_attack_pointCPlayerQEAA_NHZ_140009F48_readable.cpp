/**
 * @file j_mgr_set_animus_attack_pointCPlayerQEAA_NHZ_140009F48_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_mgr_set_animus_attack_pointCPlayerQEAA_NHZ_140009F48
 * @note Address: 0x140009F48
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?mgr_set_animus_attack_point@CPlayer@@QEAA_NH@Z
 *Address: 0x140009F48
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


bool __fastcall CPlayer::mgr_set_animus_attack_point(CPlayer *this, int nPoint)
{
  return CPlayer::mgr_set_animus_attack_point(this, nPoint);
}



} // namespace Player
} // namespace RFOnline
