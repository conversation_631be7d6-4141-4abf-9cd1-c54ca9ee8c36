/**
 * @file j_RegistCandidateMgrQEAA_NPEAVCPlayerZ_14000E3EF_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_RegistCandidateMgrQEAA_NPEAVCPlayerZ_14000E3EF
 * @note Address: 0x14000E3EF
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?Regist@CandidateMgr@@QEAA_NPEAVCPlayer@@@Z
 *Address: 0x14000E3EF
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


bool __fastcall CandidateMgr::Regist(CandidateMgr *this, CPlayer *pOne)
{
  return CandidateMgr::Regist(this, pOne);
}



} // namespace Player
} // namespace RFOnline
