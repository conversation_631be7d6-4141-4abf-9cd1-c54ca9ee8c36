/**
 * @file SendMsg_RobedHPCCharacterQEAAXPEBV1GZ_140178910_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: SendMsg_RobedHPCCharacterQEAAXPEBV1GZ_140178910
 * @note Address: 0x140178910
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: ?SendMsg_RobedHP@CCharacter@@QEAAXPEBV1@G@Z
 *Address: 0x140178910
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CCharacter::SendMsg_RobedHP(CCharacter *this, CCharacter *pkPerform, unsigned __int16 wRobedHP)
{
  __int64*v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-88h]@1
  char szMsg; // [sp+38h] [bp-50h]@4
  unsigned __int16 v7; // [sp+39h] [bp-4Fh]@4
  unsigned int v8; // [sp+3Bh] [bp-4Dh]@4
  char v9; // [sp+3Fh] [bp-49h]@4
  unsigned __int16 v10; // [sp+40h] [bp-48h]@4
  unsigned int v11; // [sp+42h] [bp-46h]@4
  unsigned __int16 v12; // [sp+46h] [bp-42h]@4
  char pbyType; // [sp+64h] [bp-24h]@4
  char v14; // [sp+65h] [bp-23h]@4
  CCharacter*v15; // [sp+90h] [bp+8h]@1

  v15 = this;
  v3 = &v5;
  void for(i = 32; i; --i );
{
    *(DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v9 = pkPerform->m_ObjID.m_byID;
  v10 = pkPerform->m_ObjID.m_wIndex;
  v11 = pkPerform->m_dwObjSerial;
  szMsg = v15->m_ObjID.m_byID;
  v7 = v15->m_ObjID.m_wIndex;
  v8 = v15->m_dwObjSerial;
  v12 = wRobedHP;
  pbyType = 17;
  v14 = 31;
  CGameObject::CircleReport((CGame, Object *)&v15->vfptr, &pbyType, &szMsg, 16, 0);
}




} // namespace Player
} // namespace RFOnline
