/**
 * @file j_SendGoalMsgCNormalGuildBattleGUILD_BATTLEIEAAX_N_14000DA7B_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_SendGoalMsgCNormalGuildBattleGUILD_BATTLEIEAAX_N_14000DA7B
 * @note Address: 0x14000DA7B
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?SendGoalMsg@CNormalGuildBattle@GUILD_BATTLE@@IEAAX_NPEADPEAVCPlayer@@H@Z
 *Address: 0x14000DA7B
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall GUILD_BATTLE::CNormalGuildBattle::SendGoalMsg(GUILD_BATTLE::CNormalGuildBattle *this, bool b1P, char *wszGuildName, CPlayer *pkPlayer, int iPortalInx)
{
  GUILD_BATTLE::CNormalGuildBattle::SendGoalMsg(this, b1P, wszGuildName, pkPlayer, iPortalInx);
}



} // namespace Player
} // namespace RFOnline
