/**
 * @file j_Select_CharacterBaseInfoCRFWorldDatabaseQEAAEKPE_14000540C_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_Select_CharacterBaseInfoCRFWorldDatabaseQEAAEKPE_14000540C
 * @note Address: 0x14000540C
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?Select_CharacterBaseInfo@CRFWorldDatabase@@QEAAEKPEAU_worlddb_character_base_info@@@Z
 *Address: 0x14000540C
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


char __fastcall CRFWorldDatabase::Select_CharacterBaseInfo(CRFWorldDatabase *this, unsigned int dwCharacterSerial, _worlddb_character_base_info *pCharacterData)
{
  return CRFWorldDatabase::Select_CharacterBaseInfo(this, dwCharacterSerial, pCharacterData);
}



} // namespace Player
} // namespace RFOnline
