/**
 * @file j_SendCTotalGuildRankManagerQEAAXKEPEAVCPlayerZ_14000C5A9_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_SendCTotalGuildRankManagerQEAAXKEPEAVCPlayerZ_14000C5A9
 * @note Address: 0x14000C5A9
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?Send@CTotalGuildRankManager@@QEAAXKEPEAVCPlayer@@@Z
 *Address: 0x14000C5A9
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CTotalGuildRankManager::Send(CTotalGuildRankManager *this, unsigned int dwVer, char byTabRace, CPlayer *pkPlayer)
{
  CTotalGuildRankManager::Send(this, dwVer, byTabRace, pkPlayer);
}



} // namespace Player
} // namespace RFOnline
