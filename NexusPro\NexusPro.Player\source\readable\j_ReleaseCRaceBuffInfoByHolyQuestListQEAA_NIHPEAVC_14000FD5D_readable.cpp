/**
 * @file j_ReleaseCRaceBuffInfoByHolyQuestListQEAA_NIHPEAVC_14000FD5D_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_ReleaseCRaceBuffInfoByHolyQuestListQEAA_NIHPEAVC_14000FD5D
 * @note Address: 0x14000FD5D
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?Release@CRaceBuffInfoByHolyQuestList@@QEAA_NIHPEAVCPlayer@@@Z
 *Address: 0x14000FD5D
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


bool __fastcall CRaceBuffInfoByHolyQuestList::Release(CRaceBuffInfoByHolyQuestList *this, unsigned int uiContinueCnt, int iResultType, CPlayer *pkDest)
{
  return CRaceBuffInfoByHolyQuestList::Release(this, uiContinueCnt, iResultType, pkDest);
}



} // namespace Player
} // namespace RFOnline
