/**
 * @file j_pc_ReleaseTargetObjectRequestCPlayerQEAAXXZ_140012CEC_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_ReleaseTargetObjectRequestCPlayerQEAAXXZ_140012CEC
 * @note Address: 0x140012CEC
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_ReleaseTargetObjectRequest@CPlayer@@QEAAXXZ
 *Address: 0x140012CEC
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_ReleaseTargetObjectRequest(CPlayer *this)
{
  CPlayer::pc_ReleaseTargetObjectRequest(this);
}



} // namespace Player
} // namespace RFOnline
