/**
 * @file j_pc_SetGroupTargetObjectRequestCPlayerQEAAXPEAVCG_14000D2E2_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_SetGroupTargetObjectRequestCPlayerQEAAXPEAVCG_14000D2E2
 * @note Address: 0x14000D2E2
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_SetGroupTargetObjectRequest@CPlayer@@QEAAXPEAVCGameObject@@KE@Z
 *Address: 0x14000D2E2
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_SetGroupTargetObjectRequest(CPlayer *this, CGameObject *pTar, unsigned int dwSerial, char byGroupType)
{
  CPlayer::pc_SetGroupTargetObjectRequest(this, pTar, dwSerial, byGroupType);
}



} // namespace Player
} // namespace RFOnline
