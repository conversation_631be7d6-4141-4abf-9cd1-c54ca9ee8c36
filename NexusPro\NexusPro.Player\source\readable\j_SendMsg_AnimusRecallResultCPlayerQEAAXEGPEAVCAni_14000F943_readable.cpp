/**
 * @file j_SendMsg_AnimusRecallResultCPlayerQEAAXEGPEAVCAni_14000F943_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_SendMsg_AnimusRecallResultCPlayerQEAAXEGPEAVCAni_14000F943
 * @note Address: 0x14000F943
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?SendMsg_AnimusRecallResult@CPlayer@@QEAAXEGPEAVCAnimus@@@Z
 *Address: 0x14000F943
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::SendMsg_AnimusRecallResult(CPlayer *this, char byResultCode, unsigned __int16 wLeftFP, CAnimus *pNewAnimus)
{
  CPlayer::SendMsg_AnimusRecallResult(this, byResultCode, wLeftFP, pNewAnimus);
}



} // namespace Player
} // namespace RFOnline
