/**
 * @file unchecked_copyPEAVCGuildBattleRewardItemGUILD_BATT_1403D2590_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: unchecked_copyPEAVCGuildBattleRewardItemGUILD_BATT_1403D2590
 * @note Address: 0x1403D2590
 */

#include <cstdint>

namespace RFOnline {
namespace Combat {

﻿/*
 *Function: ??$unchecked_copy@PEAVCGuildBattleRewardItem@GUILD_BATTLE@@PEAV12@@stdext@@YAPEAVCGuildBattleRewardItem@GUILD_BATTLE@@PEAV12@00@Z
 *Address: 0x1403D2590
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"
#include <cstring>

// Additional STL includes for compilation
#include <iterator>



GUILD_BATTLE::CGuildBattleRewardItem *__fastcall stdext::unchecked_copy<GUILD_BATTLE::CGuildBattleRewardItem *,GUILD_BATTLE::CGuildBattleRewardItem *>(GUILD_BATTLE::CGuildBattleRewardItem *_First, GUILD_BATTLE::CGuildBattleRewardItem *_Last, GUILD_BATTLE::CGuildBattleRewardItem *_Dest)
{
  __int64*v3; // rdi@1
  signed __int64 i; // rcx@1
  std::random_access_iterator_tag*v5; // rax@4
  __int64 v7; // [sp+0h] [bp-48h]@1
  std::_Nonscalar_ptr_iterator_tag v8; // [sp+20h] [bp-28h]@4
  std::_Range_checked_iterator_tag v9; // [sp+28h] [bp-20h]@4
  std::_Range_checked_iterator_tag v10; // [sp+30h] [bp-18h]@4
  std::_Nonscalar_ptr_iterator_tag v11; // [sp+31h] [bp-17h]@4
  GUILD_BATTLE::CGuildBattleRewardItem*v12; // [sp+32h] [bp-16h]@4
  GUILD_BATTLE::CGuildBattleRewardItem*__formal; // [sp+50h] [bp+8h]@1
  GUILD_BATTLE::CGuildBattleRewardItem*_Lasta; // [sp+58h] [bp+10h]@1
  GUILD_BATTLE::CGuildBattleRewardItem*_Desta; // [sp+60h] [bp+18h]@1

  _Desta = _Dest;
  _Lasta = _Last;
  __formal = _First;
  v3 = &v7;
  void for(i = 16; i > 0; --i );
{
    *(DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  memset(&v10, 0, sizeof(v10));
  v11 = std::_Ptr_cat<GUILD_BATTLE::CGuildBattleRewardItem *,GUILD_BATTLE::CGuildBattleRewardItem *>(&__formal, &_Desta);
  LOBYTE(v5) = std::_Iter_random<GUILD_BATTLE::CGuildBattleRewardItem *,GUILD_BATTLE::CGuildBattleRewardItem *>(
                 &v12,
                 &__formal);
  v9 = v10;
  v8 = v11;
  return std::_Copy_opt<GUILD_BATTLE::CGuildBattleRewardItem *,GUILD_BATTLE::CGuildBattleRewardItem *,std::random_access_iterator_tag>(
           __formal,
           _Lasta,
           _Desta,
           (std::random_access_iterator_tag)v5->0,
           v11,
           v10);
}





} // namespace Combat
} // namespace RFOnline
