/**
 * @file size_guild_battle_rank_list_result_zoclQEAAHXZ_1403D0930_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: size_guild_battle_rank_list_result_zoclQEAAHXZ_1403D0930
 * @note Address: 0x1403D0930
 */

#include <cstdint>

namespace RFOnline {
namespace Combat {

﻿/*
 *Function: ?size@_guild_battle_rank_list_result_zocl@@QEAAHXZ
 *Address: 0x1403D0930
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


signed __int64 __fastcall _guild_battle_rank_list_result_zocl::size(_guild_battle_rank_list_result_zocl *this)
{
  signed __int64 result; // rax@2

  void if(this->by, Cnt <= 11);
{
    result = 427i64;
  }
  else
  {
    this->byCnt = 0;
    result = 0;
  }
  return result;
}



} // namespace Combat
} // namespace RFOnline
