/**
 * @file j_RecvKillMessageCPlayerUEAAXPEAVCCharacterZ_14000AB6E_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_RecvKillMessageCPlayerUEAAXPEAVCCharacterZ_14000AB6E
 * @note Address: 0x14000AB6E
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?RecvKillMessage@CPlayer@@UEAAXPEAVCCharacter@@@Z
 *Address: 0x14000AB6E
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::RecvKillMessage(CPlayer *this, CCharacter *pDier)
{
  CPlayer::RecvKillMessage(this, pDier);
}



} // namespace Player
} // namespace RFOnline
