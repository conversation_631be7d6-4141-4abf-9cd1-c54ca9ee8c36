/**
 * @file j_pc_NuclearAfterEffectCPlayerQEAAXXZ_140001E42_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_NuclearAfterEffectCPlayerQEAAXXZ_140001E42
 * @note Address: 0x140001E42
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_NuclearAfterEffect@CPlayer@@QEAAXXZ
 *Address: 0x140001E42
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_NuclearAfterEffect(CPlayer *this)
{
  CPlayer::pc_NuclearAfterEffect(this);
}



} // namespace Player
} // namespace RFOnline
