/**
 * @file j_SendMsg_AlterEquipSPInformCPlayerQEAAXXZ_140004273_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_SendMsg_AlterEquipSPInformCPlayerQEAAXXZ_140004273
 * @note Address: 0x140004273
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?SendMsg_AlterEquipSPInform@CPlayer@@QEAAXXZ
 *Address: 0x140004273
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::SendMsg_AlterEquipSPInform(CPlayer *this)
{
  CPlayer::SendMsg_AlterEquipSPInform(this);
}



} // namespace Player
} // namespace RFOnline
