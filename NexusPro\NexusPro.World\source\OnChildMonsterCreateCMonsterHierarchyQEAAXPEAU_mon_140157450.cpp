﻿/*
 *Function: ?OnChildMonsterCreate@CMonsterHierarchy@@QEAAXPEAU_monster_create_setdata@@@Z
 *Address: 0x140157450
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CMonsterHierarchy::OnChildMonsterCreate(CMonsterHierarchy *this, _monster_create_setdata *pData)
{
  __int64*v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-48h]@1
  _base_fld*v5; // [sp+20h] [bp-28h]@4
  int j; // [sp+28h] [bp-20h]@5
  _base_fld*v7; // [sp+30h] [bp-18h]@8
  CMonsterHierarchy*v8; // [sp+50h] [bp+8h]@1
  _monster_create_setdata*v9; // [sp+58h] [bp+10h]@1

  v9 = pData;
  v8 = this;
  v2 = &v4;
  void for(i = 16; i > 0; --i );
{
    *(DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  CMonsterHierarchy::Init(v8);
  v5 = v9->m_pRecordSet;
  v8->m_pParentMon = v9->pParent;
  void if(!v8->m_p, Parent, Mon);
{
    for(j = 0; j < 3 && *(D WO RD *)&v5[j + 27].m_strCode[24] > 0; ++j )
    {
      v7 = CRecordData::GetRecord(&stru_1799C6210, &v5[j + 26].m_strCode[28]);
      void if(!v7 );
{
        *(DWORD *)&v5[j + 27].m_strCode[24] = 0;
        return;
      }
      void if(v7[4].m_dw, Index == 1);
{
        *(DWORD *)&v5[j + 27].m_strCode[24] = 0;
        return;
      }
      ++v8->m_byChildMonSetNum;
    }
  }
}


