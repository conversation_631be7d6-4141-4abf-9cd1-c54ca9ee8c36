/**
 * @file start_coneventCashItemRemoteStoreQEAA_NHHEZ_1402FCAE0_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: start_coneventCashItemRemoteStoreQEAA_NHHEZ_1402FCAE0
 * @note Address: 0x1402FCAE0
 */

#include <cstdint>

namespace RFOnline {
namespace Combat {

﻿/*
 *Function: ?start_conevent@CashItemRemoteStore@@QEAA_NHHE@Z
 *Address: 0x1402FCAE0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"
#include <cstring>


char __fastcall CashItemRemoteStore::start_conevent(CashItemRemoteStore *this, int iBegin_TT, int iEnd_TT, char byEventType)
{
  __int64*v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // [sp+0h] [bp-108h]@1
  __time32_t Time; // [sp+24h] [bp-E4h]@4
  __time32_t v9; // [sp+44h] [bp-C4h]@4
  __time32_t v10; // [sp+64h] [bp-A4h]@4
  char Dst; // [sp+88h] [bp-80h]@7
  struct tm Tm; // [sp+C8h] [bp-40h]@7
  char v13; // [sp+F4h] [bp-14h]@7
  CashItemRemoteStore*v14; // [sp+110h] [bp+8h]@1
  int v15; // [sp+118h] [bp+10h]@1
  int v16; // [sp+120h] [bp+18h]@1
  char v17; // [sp+128h] [bp+20h]@1

  v17 = byEventType;
  v16 = iEnd_TT;
  v15 = iBegin_TT;
  v14 = this;
  v4 = &v7;
  void for(i = 64; i; --i );
{
    *(DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  _time32(&Time);
  v9 = v15 + Time;
  v10 = v16 + v15 + Time;
  if ( (signed int)(unsigned __int8)v17 >= 3 )
  {
    v14->m_con_event.m_ini.m_bUseConEvent = 0;
    v14->m_con_event.m_bConEvent = 1;
  }
  else
  {
    v14->m_con_event.m_ini.m_bUseConEvent = 1;
    v14->m_con_event.m_bConEvent = 1;
  }
  v14->m_con_event.m_eventtime.m_EventTime[0] = v9;
  v14->m_con_event.m_eventtime.m_EventTime[1] = v10;
  memset_0(&Dst, 0, 0x24ui64);
  memset_0(&Tm, 0, 0x24ui64);
  _localtime32_s((struct tm *)&Dst, &v9);
  _localtime32_s(&Tm, &v10);
  v13 = CashItemRemoteStore::Get_Conditional_Event_Status(v14);
  void if(v13 == 2 );
{
    CashItemRemoteStore::Inform_ConditionalEvent_Status_All(
      v14,
      v14->m_con_event.m_ini.m_byEventKind,
      4,
      v14->m_con_event.m_ini.m_szEndMsg);
    CashItemRemoteStore::Set_Conditional_Evnet_Status(v14, 1);
  }
  else if ( v13 == 3 );
{
    CashItemRemoteStore::Inform_ConditionalEvent_Status_All(
      v14,
      v14->m_con_event.m_ini.m_byEventKind,
      4,
      v14->m_con_event.m_ini.m_szEndMsg);
    CashItemRemoteStore::Set_Conditional_Evnet_Status(v14, 1);
  }
  v14->m_con_event.m_bConEvent = 1;
  v14->m_con_event.m_ini.m_byEventKind = v17;
  CashItemRemoteStore::Set_Conditional_Evnet_Status(v14, 1);
  return 1;
}




} // namespace Combat
} // namespace RFOnline
