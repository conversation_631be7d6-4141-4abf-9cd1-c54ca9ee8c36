/**
 * @file j_pc_TrunkAlterItemSlotRequestCPlayerQEAAXKEEZ_140013A5C_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_TrunkAlterItemSlotRequestCPlayerQEAAXKEEZ_140013A5C
 * @note Address: 0x140013A5C
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_TrunkAlterItemSlotRequest@CPlayer@@QEAAXKEE@Z
 *Address: 0x140013A5C
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_TrunkAlterItemSlotRequest(CPlayer *this, unsigned int dwItemSerial, char byClientSlotIndex, char byStorageIndex)
{
  CPlayer::pc_TrunkAlterItemSlotRequest(this, dwItemSerial, byClientSlotIndex, byStorageIndex);
}



} // namespace Player
} // namespace RFOnline
