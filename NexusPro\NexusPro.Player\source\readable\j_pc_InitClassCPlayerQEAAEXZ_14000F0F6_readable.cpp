/**
 * @file j_pc_InitClassCPlayerQEAAEXZ_14000F0F6_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_InitClassCPlayerQEAAEXZ_14000F0F6
 * @note Address: 0x14000F0F6
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_InitClass@CPlayer@@QEAAEXZ
 *Address: 0x14000F0F6
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


char __fastcall CPlayer::pc_InitClass(CPlayer *this)
{
  return CPlayer::pc_InitClass(this);
}



} // namespace Player
} // namespace RFOnline
