/**
 * @file j_pc_SetTargetObjectRequestCPlayerQEAAXPEAVCGameOb_14000F407_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_SetTargetObjectRequestCPlayerQEAAXPEAVCGameOb_14000F407
 * @note Address: 0x14000F407
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_SetTargetObjectRequest@CPlayer@@QEAAXPEAVCGameObject@@K_N@Z
 *Address: 0x14000F407
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_SetTargetObjectRequest(CPlayer *this, CGameObject *pTar, unsigned int dwSerial, bool bForce)
{
  CPlayer::pc_SetTargetObjectRequest(this, pTar, dwSerial, bForce);
}



} // namespace Player
} // namespace RFOnline
