/**
 * @file j_pc_WhisperBlockCPlayerQEAAX_NZ_14001164E_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_WhisperBlockCPlayerQEAAX_NZ_14001164E
 * @note Address: 0x14001164E
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_Whisper<PERSON><PERSON>@CPlayer@@QEAAX_N@Z
 *Address: 0x14001164E
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_Whisper<PERSON>lock(CPlayer *this, bool bBlock)
{
  CPlayer::pc_WhisperBlock(this, bBlock);
}



} // namespace Player
} // namespace RFOnline
