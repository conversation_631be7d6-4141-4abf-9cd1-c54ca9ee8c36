/**
 * @file j_pc_DTradeCancleRequestCPlayerQEAAXXZ_140010947_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_DTradeCancleRequestCPlayerQEAAXXZ_140010947
 * @note Address: 0x140010947
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_DTradeCancleRequest@CPlayer@@QEAAXXZ
 *Address: 0x140010947
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_DTradeCancleRequest(CPlayer *this)
{
  CPlayer::pc_DTradeCancleRequest(this);
}



} // namespace Player
} // namespace RFOnline
