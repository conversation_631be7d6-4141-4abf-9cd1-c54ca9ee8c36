/**
 * @file size_qry_case_dest_guild_out_guildbattlecostQEAAHX_14025D5D0_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: size_qry_case_dest_guild_out_guildbattlecostQEAAHX_14025D5D0
 * @note Address: 0x14025D5D0
 */

#include <cstdint>

namespace RFOnline {
namespace Combat {

﻿/*
 *Function: ?size@_qry_case_dest_guild_out_guildbattlecost@@QEAAHXZ
 *Address: 0x14025D5D0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


signed __int64 __fastcall _qry_case_dest_guild_out_guildbattlecost::size(_qry_case_dest_guild_out_guildbattlecost *this)
{
  return 64i64;
}



} // namespace Combat
} // namespace RFOnline
