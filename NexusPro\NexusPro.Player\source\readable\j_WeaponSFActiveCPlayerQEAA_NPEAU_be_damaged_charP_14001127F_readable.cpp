/**
 * @file j_WeaponSFActiveCPlayerQEAA_NPEAU_be_damaged_charP_14001127F_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_WeaponSFActiveCPlayerQEAA_NPEAU_be_damaged_charP_14001127F
 * @note Address: 0x14001127F
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?WeaponSFActive@CPlayer@@QEAA_NPEAU_be_damaged_char@@PEAH1G@Z
 *Address: 0x14001127F
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


bool __fastcall CPlayer::WeaponSFActive(CPlayer *this, _be_damaged_char *pDamList, int *nDamagedObjNum, int *nShotNum, unsigned __int16 wBulletSerial)
{
  return CPlayer::WeaponSFActive(this, pDamList, nDamagedObjNum, nShotNum, wBulletSerial);
}



} // namespace Player
} // namespace RFOnline
