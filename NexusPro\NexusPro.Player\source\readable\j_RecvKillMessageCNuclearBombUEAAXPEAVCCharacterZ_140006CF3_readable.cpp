/**
 * @file j_RecvKillMessageCNuclearBombUEAAXPEAVCCharacterZ_140006CF3_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_RecvKillMessageCNuclearBombUEAAXPEAVCCharacterZ_140006CF3
 * @note Address: 0x140006CF3
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?RecvKillMessage@CNuclearBomb@@UEAAXPEAVCCharacter@@@Z
 *Address: 0x140006CF3
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CNuclearBomb::RecvKillMessage(CNuclearBomb *this, CCharacter *pDier)
{
  CNuclearBomb::RecvKillMessage(this, pDier);
}



} // namespace Player
} // namespace RFOnline
