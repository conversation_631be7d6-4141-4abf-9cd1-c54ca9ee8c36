/**
 * @file j_RecallCRecallRequestQEAAEPEAVCPlayer_NZ_1400113F1_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_RecallCRecallRequestQEAAEPEAVCPlayer_NZ_1400113F1
 * @note Address: 0x1400113F1
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?Recall@CRecallRequest@@QEAAEPEAVCPlayer@@_N@Z
 *Address: 0x1400113F1
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


char __fastcall CRecallRequest::Recall(CRecallRequest *this, CPlayer *pkDest, bool bStone)
{
  return CRecallRequest::Recall(this, pkDest, bStone);
}



} // namespace Player
} // namespace RFOnline
