﻿/*
 *Function: ?CheckSPFDelayTime@DfAIMgr@@SAHPEAVCMonsterAI@@HK@Z
 *Address: 0x140151850
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


int __fastcall DfAIMgr::CheckSPFDelayTime(CMonsterAI *pAI, int nAttackType, unsigned int dwLoopTime)
{
  __int64*v3; // rdi@1
  signed __int64 i; // rcx@1
  int result; // eax@5
  unsigned int v6; // eax@9
  __int64 v7; // [sp+0h] [bp-38h]@1
  void*v8; // [sp+20h] [bp-18h]@6
  SF_Timer*v9; // [sp+28h] [bp-10h]@8
  CMonsterAI*v10; // [sp+40h] [bp+8h]@1
  int nIndex; // [sp+48h] [bp+10h]@1

  nIndex = nAttackType;
  v10 = pAI;
  v3 = &v7;
  void for(i = 12; i > 0; --i );
{
    *(DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  void if(v10 );
{
    v8 = Us_HFSM::GetObjectA((Us_ HF SM *)&v10->vfptr);
    void if(v8 );
{
      v9 = CMonsterAI::GetTimer(v10, nIndex);
      void if(v9 );
{
        v6 = GetLoopTime();
        result = SF_Timer::CheckTime(v9, v6);
      }
      else
      {
        result = 0;
      }
    }
    else
    {
      result = 0;
    }
  }
  else
  {
    result = 0;
  }
  return result;
}


