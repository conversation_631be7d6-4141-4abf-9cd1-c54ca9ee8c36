/**
 * @file j_pc_RequestUILockInitCPlayerQEAAXPEAVCUserDBPEAD1_14000B6CC_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_RequestUILockInitCPlayerQEAAXPEAVCUserDBPEAD1_14000B6CC
 * @note Address: 0x14000B6CC
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_RequestUILockInit@CPlayer@@QEAAXPEAVCUserDB@@PEAD1E1@Z
 *Address: 0x14000B6CC
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_RequestUILockInit(CPlayer *this, CUserDB *pUserDB, char *sz<PERSON><PERSON>ock<PERSON><PERSON>, char *sz<PERSON><PERSON><PERSON><PERSON>W_Confirm, char byUILock_HintIndex, char *uszUILock_HintAnswer)
{
  CPlayer::pc_RequestUILockInit(this, pUserDB, szUILockPW, szUILockPW_Confirm, byUILock_HintIndex, uszUILock_HintAnswer);
}



} // namespace Player
} // namespace RFOnline
