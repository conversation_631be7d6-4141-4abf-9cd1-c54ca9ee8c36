﻿/*
 *Function: ?HearMapSound@CBsp@@QEAAXXZ
 *Address: 0x1404FD620
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


int CBsp::HearMapSound(...)
{
  CBsp*v3; // rdi@1
  signed __int64 v4; // rcx@2
  unsigned int v5; // er12@2
  __int64 v6; // rbp@3
  _SOUND_ENTITIES_LIST*v7; // rax@3
  unsigned __int64 v8; // rbx@3
  signed __int64 v9; // rcx@3
  __int128 v10; // xmm0@8
  float v11; // xmm0_4@10
  _SOUND_ENTITIES_LIST*v12; // rax@10
  float v13; // xmm7_4@10
  __int128 v14; // xmm6@11
  _SOUND_ENTITIES_LIST*v15; // r8@13
  unsigned __int64 v16; // rbx@14
  _SOUND_ENTITIES_LIST*v17; // rbx@18
  _SOUND_ENTITIES_LIST*v18; // rcx@18
  __int128 v19; // xmm0@18
  float v20; // xmm7_4@18
  float v21; // xmm7_4@19
  _SOUND_ENTITIES_LIST*v22; // rax@20
  __int128 v23; // xmm6@21
  _SOUND_ENTITIES_LIST*v24; // r8@23
  _SOUND_ENTITIES_LIST*v25; // rbx@25
  _SOUND_ENTITIES_LIST*v26; // rcx@25
  float v27; // xmm2_4@25
  unsigned __int64 v28; // rbx@26
  __int64 v29; // r13@30
  unsigned int v30; // esi@32
  char v31; // r12@32
  _SOUND_ENTITIES_LIST*v32; // rdx@34
  int v33; // ebp@34
  signed __int64 v34; // rbx@34
  float v35; // [sp+20h] [bp-78h]@2
  float v36; // [sp+24h] [bp-74h]@5
  float v37; // [sp+28h] [bp-70h]@7

  v3 = this;
  if ( __PAIR__(this->mSoundEntitiesListNum, this->mSoundEntityListNum) )
  {
    GetCameraPos((float (*)[3])&v35);
    v4 = (signed __int64)&v3->mLeafSoundEntityList[v3->mNowLeafNum];
    v5 = 0;
    if(*((WO RD*)v4 + 2) )
    {
      do
      {
        v6 = v3->mSoundEntityID[v5 + *(WORD *)v4];
        v7 = v3->mSoundEntitiesList;
        v8 = v3->mSoundEntityID[v5 + *(WORD *)v4];
        v9 = (signed __int64)&v7[v6];
        if(v35 > v7[v6].B BMin[0]
          && *((float*)v9 + 68) > v35
          && v36 > *((float*)v9 + 60)
          && *((float*)v9 + 72) > v36
          && v37 > *((float*)v9 + 64) )
        {
          v10 = *((DWORD*)v9 + 76);
          if ( *(float *)&v10 > v37 )
          {
            if(*((BY TE*)v9 + 4) &1 )
            {
              v11 = _SOUND_ENTITIES_LIST::GetVolume(&v7[v6], v7[v6].Pos);
              v12 = v3->mSoundEntitiesList;
              v13 = v11;
              void if(v12[v6].Flag&0x10);
{
                v14 = 0;
              }
              else
              {
                ((DWORD)(v10) = _SOUND_ENTITIES_LIST::GetPan(&v12[v6], v12[v6].Pos);
                v14 = v10;
              }
              v15 = v3->mSoundEntitiesList;
              void if(v15[v6].Flag&4);
{
                v16 = v8 >> 3;
                if(v3->m, Sound, Entity, Cache[v16] & (unsigned __int8)(1 << (v6&7)) )
                {
                  a3 = v14;
                  IM_SetWaveVolumeAndPan(v3->m, Sound, Entity, List[(unsigned __int64)v15[v6].ID].ID, v13, *(float *)&v14);
                }
                else
                {
                  v3->mSoundEntityCache[v16] |= 1 << (v6&7);
                  IM_SetLoopCntWave(v3->m, Sound, Entity, List[(unsigned __int64)v3->mSoundEntitiesList[v6].ID].ID, 0);
                  a3 = v14;
                  IM_PlayWave(v3->m, Sound, Entity, List[(unsigned __int64)v3->mSoundEntitiesList[v6].ID].ID,
                    v13,
                    *(float *)&v14);
                }
              }
              else
              {
                v3->mSoundEntitiesList[v6].NextPlayTime = R3GetLoopTime() + v3->mSoundEntitiesList[v6].NextPlayTime;
                if(v3->m, Sound, Entities, List[v6].Next, Play, Time > (float)v3->mSoundEntitiesList[v6].EventTime )
                {
                  v17 = v3->mSoundEntitiesList;
                  v17[v6].NextPlayTime = (float)(rand() % (v17[v6].EventTime + 1));
                  v18 = v3->mSoundEntitiesList;
                  *(float *)&a3 = (float)v18[v6].EventTime;
                  v19 = a3;
                  v18[v6].NextPlayTime = (float)(*(float *)&a3 - (float)(*(float *)&a3*0.25))
                                       + (float)(v18[v6].NextPlayTime*0.5);
                  v3->mSoundEntitiesList[v6].NextPlayTime = (float)v3->mSoundEntitiesList[v6].EventTime
                                                          - v3->mSoundEntitiesList[v6].NextPlayTime;
                  v20 = _SOUND_ENTITIES_LIST::GetVolume(&v3->mSoundEntitiesList[v6], v3->mSoundEntitiesList[v6].Pos);
                  ((DWORD)(v19) = _SOUND_ENTITIES_LIST::GetPan(
                                   &v3->mSoundEntitiesList[v6],
                                   v3->mSoundEntitiesList[v6].Pos);
                  IM_SetLoopCntWave(v3->m, Sound, Entity, List[(unsigned __int64)v3->mSoundEntitiesList[v6].ID].ID, 1u);
                  a3 = v19;
                  IM_PlayWave(v3->m, Sound, Entity, List[(unsigned __int64)v3->mSoundEntitiesList[v6].ID].ID,
                    v20,
                    *(float *)&v19);
                }
              }
            }
            else
            {
              v21 = _SOUND_ENTITIES_LIST::GetBoxIntensity((_SOUND_ENTITIES_ LI ST *)v9, &v35);
              void if(v21 > 0.0 );
{
                v22 = v3->mSoundEntitiesList;
                void if(v22[v6].Flag&0x10);
{
                  v23 = 0;
                }
                else
                {
                  ((DWORD)(v10) = _SOUND_ENTITIES_LIST::GetPan(&v22[v6], v22[v6].Pos);
                  v23 = v10;
                }
                v24 = v3->mSoundEntitiesList;
                void if(v24[v6].Flag&4);
{
                  v28 = v8 >> 3;
                  if(v3->m, Sound, Entity, Cache[v28] & (unsigned __int8)(1 << (v6&7)) )
                  {
                    a3 = v23;
                    IM_SetWaveVolumeAndPan(v3->m, Sound, Entity, List[(unsigned __int64)v24[v6].ID].ID, v21, *(float *)&v23);
                  }
                  else
                  {
                    v3->mSoundEntityCache[v28] |= 1 << (v6&7);
                    IM_SetLoopCntWave(v3->m, Sound, Entity, List[(unsigned __int64)v3->mSoundEntitiesList[v6].ID].ID, 0);
                    a3 = v23;
                    IM_PlayWave(v3->m, Sound, Entity, List[(unsigned __int64)v3->mSoundEntitiesList[v6].ID].ID,
                      v21,
                      *(float *)&v23);
                  }
                }
                else
                {
                  v3->mSoundEntitiesList[v6].NextPlayTime = R3GetLoopTime() + v3->mSoundEntitiesList[v6].NextPlayTime;
                  if(v3->m, Sound, Entities, List[v6].Next, Play, Time > (float)v3->mSoundEntitiesList[v6].EventTime )
                  {
                    v25 = v3->mSoundEntitiesList;
                    v25[v6].NextPlayTime = (float)(rand() % (v25[v6].EventTime + 1));
                    v26 = v3->mSoundEntitiesList;
                    v27 = (float)v26[v6].EventTime;
                    v26[v6].NextPlayTime = (float)(v27 - (float)(v27*0.25)) + (float)(v26[v6].NextPlayTime*0.5);
                    v3->mSoundEntitiesList[v6].NextPlayTime = (float)v3->mSoundEntitiesList[v6].EventTime
                                                            - v3->mSoundEntitiesList[v6].NextPlayTime;
                    IM_SetLoopCntWave(v3->m, Sound, Entity, List[(unsigned __int64)v3->mSoundEntitiesList[v6].ID].ID, 1u);
                    a3 = v23;
                    IM_PlayWave(v3->m, Sound, Entity, List[(unsigned __int64)v3->mSoundEntitiesList[v6].ID].ID,
                      v21,
                      *(float *)&v23);
                  }
                }
              }
            }
          }
        }
        ++v5;
        v4 = (signed __int64)&v3->mLeafSoundEntityList[v3->mNowLeafNum];
      }
      while(v5 < *((WO RD*)v4 + 2) );
    }
    v29 = 0;
    void if(v3->m, Sound, Entity, Cache, Size);
{
      do
      {
        void if(v3->m, Sound, Entity, Cache[v29]);
{
          v30 = 0;
          v31 = 1;
          do
          {
            if ( (unsigned __int8)v31 &v3->mSoundEntityCache[v29] )
            {
              v32 = v3->mSoundEntitiesList;
              v33 = v30 + 8*v29;
              v34 = (signed int)(v30 + 8*v29);
              if(v35 <= v32[v34].B BMin[0]
                || v32[v34].B BMax[0] <= v35
                || v36 <= v32[v34].B BMin[1]
                || v32[v34].B BMax[1] <= v36
                || v37 <= v32[v34].B BMin[2]
                || v32[v34].B BMax[2] <= v37)
              {
                IM_StopWave(v3->m, Sound, Entity, List[(unsigned __int64)v32[v34].ID].ID);
                v3->mSoundEntityCache[v34 >> 3] &= ~(1 << (v33& 7));
              }
            }
            ++v30;
            v31 *= 2;
          }
          while ( v30 < 8 );
        }
        v29 = (unsigned int)(v29 + 1);
      }
      while ( (unsigned int)v29 < v3->mSoundEntityCacheSize );
    }
  }
}


