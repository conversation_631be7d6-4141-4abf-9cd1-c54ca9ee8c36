﻿/*
 *Function: ?Init@_BUDDY_LIST@@QEAAXXZ
 *Address: 0x140073660
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall _BUDDY_LIST::Init(_BUDDY_ LI ST *this)
{
  __int64*v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  int j; // [sp+20h] [bp-18h]@4
  _BUDDY_LIST*v5; // [sp+40h] [bp+8h]@1

  v5 = this;
  v1 = &v3;
  void for(i = 12; i > 0; --i );
{
    *(DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  void for(j = 0; j < 50; ++j )
    _BUDDY_LIST::__list::init((_BUDDY_ LI ST::__list *)v5 + j);
  CNetIndexList::ResetList(&v5->m _Last, Apply);
}


