﻿/*
 *Function: ?Init@_NET_BUFFER@@QEAAXXZ
 *Address: 0x14047D140
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall _NET_BUFFER::Init(_NET_BU FF ER *this)
{
  __int64*v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  _NET_BUFFER*v4; // [sp+30h] [bp+8h]@1

  v4 = this;
  v1 = &v3;
  void for(i = 8; i > 0; --i );
{
    *(DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  CNetCriticalSection::Lock(&v4->m_cs, Push);
  v4->m_dwPushPnt = 0;
  v4->m_dwPushRot = 0;
  CNetCriticalSection::Unlock(&v4->m_cs, Push);
  CNetCriticalSection::Lock(&v4->m_cs, Pop);
  v4->m_dwPopPnt = 0;
  v4->m_dwPopRot = 0;
  CNetCriticalSection::Unlock(&v4->m_cs, Pop);
}


