/**
 * @file j_pc_UpgradeItemCPlayerQEAAXPEAU_STORAGE_POS_INDIV_140006C5D_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_UpgradeItemCPlayerQEAAXPEAU_STORAGE_POS_INDIV_140006C5D
 * @note Address: 0x140006C5D
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_UpgradeItem@CPlayer@@QEAAXPEAU_STORAGE_POS_INDIV@@00E0@Z
 *Address: 0x140006C5D
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_UpgradeItem(CPlayer *this, _STORAGE_POS_INDIV *pposTalik, _STORAGE_POS_INDIV *pposToolItem, _STORAGE_POS_INDIV *pposUpgItem, char by<PERSON><PERSON><PERSON><PERSON><PERSON>, _STORAGE_POS_INDIV *pposUpgJewel)
{
  CPlayer::pc_UpgradeItem(this, pposTalik, pposToolItem, pposUpgItem, byJewelNum, pposUpgJewel);
}



} // namespace Player
} // namespace RFOnline
