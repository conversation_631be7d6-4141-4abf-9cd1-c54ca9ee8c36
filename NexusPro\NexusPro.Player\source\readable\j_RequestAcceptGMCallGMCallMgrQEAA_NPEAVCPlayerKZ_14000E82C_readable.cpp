/**
 * @file j_RequestAcceptGMCallGMCallMgrQEAA_NPEAVCPlayerKZ_14000E82C_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_RequestAcceptGMCallGMCallMgrQEAA_NPEAVCPlayerKZ_14000E82C
 * @note Address: 0x14000E82C
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?RequestAcceptGMCall@GMCallMgr@@QEAA_NPEAVCPlayer@@K@Z
 *Address: 0x14000E82C
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


bool __fastcall GMCallMgr::RequestAcceptGMCall(GMCallMgr *this, CPlayer *pOne, unsigned int dwUserSerial)
{
  return GMCallMgr::RequestAcceptGMCall(this, pOne, dwUserSerial);
}



} // namespace Player
} // namespace RFOnline
