/**
 * @file SendMsg_TalikCrystalExchangeResultCPlayerQEAAXEEPE_1400E94E0_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: SendMsg_TalikCrystalExchangeResultCPlayerQEAAXEEPE_1400E94E0
 * @note Address: 0x1400E94E0
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: ?SendMsg_TalikCrystalExchangeResult@CPlayer@@QEAAXEEPEAU_db_con@_STORAGE_LIST@@@Z
 *Address: 0x1400E94E0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::SendMsg_TalikCrystalExchangeResult(CPlayer *this, char byRet, char byExchange<PERSON><PERSON>, _STORAGE_LIST::_db_con *pNewItem)
{
  ;
}



} // namespace Player
} // namespace RFOnline
