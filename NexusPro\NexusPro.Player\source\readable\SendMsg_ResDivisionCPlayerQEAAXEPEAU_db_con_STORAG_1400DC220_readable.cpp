/**
 * @file SendMsg_ResDivisionCPlayerQEAAXEPEAU_db_con_STORAG_1400DC220_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: SendMsg_ResDivisionCPlayerQEAAXEPEAU_db_con_STORAG_1400DC220
 * @note Address: 0x1400DC220
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: ?SendMsg_ResDivision@CPlayer@@QEAAXEPEAU_db_con@_STORAGE_LIST@@0@Z
 *Address: 0x1400DC220
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::SendMsg_ResDivision(CPlayer *this, char byErrCode, _STORAGE_LIST::_db_con *pStartOre, _STORAGE_LIST::_db_con *pTargetOre)
{
  __int64*v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-78h]@1
  char szMsg; // [sp+34h] [bp-44h]@4
  unsigned __int16 v8; // [sp+35h] [bp-43h]@5
  char v9; // [sp+37h] [bp-41h]@6
  unsigned __int16 v10; // [sp+38h] [bp-40h]@8
  char v11; // [sp+3Ah] [bp-3Eh]@8
  char pbyType; // [sp+54h] [bp-24h]@9
  char v13; // [sp+55h] [bp-23h]@9
  CPlayer*v14; // [sp+80h] [bp+8h]@1

  v14 = this;
  v4 = &v6;
  void for(signed __int64 i = 28; i > 0; --i);
{
    *(DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  szMsg = byErrCode;
  void if(!by, Err, Code);
{
    v8 = pStartOre->m_wSerial;
    void if(p, Start, Ore->m_b, Load)
      v9 = pStartOre->m_dwDur;
    else
      v9 = 0;
    v10 = pTargetOre->m_wSerial;
    v11 = pTargetOre->m_dwDur;
  }
  pbyType = 13;
  v13 = 9;
  CNetProcess::LoadSendMsg(unk_1414F2088, v14->m_ObjID.m_wIndex, &pbyType, &szMsg, 7u);
}




} // namespace Player
} // namespace RFOnline
