/**
 * @file j_ReleasecStaticMember_PlayerSAXXZ_1400096A6_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_ReleasecStaticMember_PlayerSAXXZ_1400096A6
 * @note Address: 0x1400096A6
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?Release@cStaticMember_Player@@SAXXZ
 *Address: 0x1400096A6
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void cStaticMember_Player::Release(void)
{
  cStaticMember_Player::Release();
}



} // namespace Player
} // namespace RFOnline
