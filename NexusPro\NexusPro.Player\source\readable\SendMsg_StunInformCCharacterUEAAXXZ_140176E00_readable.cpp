/**
 * @file SendMsg_StunInformCCharacterUEAAXXZ_140176E00_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: SendMsg_StunInformCCharacterUEAAXXZ_140176E00
 * @note Address: 0x140176E00
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: ?SendMsg_StunInform@CCharacter@@UEAAXXZ
 *Address: 0x140176E00
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CCharacter::SendMsg_StunInform(CCharacter *this)
{
  __int64*v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-78h]@1
  char szMsg; // [sp+34h] [bp-44h]@4
  unsigned __int16 v5; // [sp+35h] [bp-43h]@4
  unsigned int v6; // [sp+37h] [bp-41h]@4
  char pbyType; // [sp+54h] [bp-24h]@4
  char v8; // [sp+55h] [bp-23h]@4
  CCharacter*v9; // [sp+80h] [bp+8h]@1

  v9 = this;
  v1 = &v3;
  void for(signed __int64 i = 28; i > 0; --i);
{
    *(DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  szMsg = v9->m_ObjID.m_byID;
  v6 = v9->m_dwObjSerial;
  v5 = v9->m_ObjID.m_wIndex;
  pbyType = 17;
  v8 = 16;
  CGameObject::CircleReport((CGame, Object *)&v9->vfptr, &pbyType, &szMsg, 7, 0);
}




} // namespace Player
} // namespace RFOnline
