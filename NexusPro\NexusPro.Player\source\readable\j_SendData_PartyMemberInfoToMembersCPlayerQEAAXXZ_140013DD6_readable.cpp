/**
 * @file j_SendData_PartyMemberInfoToMembersCPlayerQEAAXXZ_140013DD6_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_SendData_PartyMemberInfoToMembersCPlayerQEAAXXZ_140013DD6
 * @note Address: 0x140013DD6
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?SendData_PartyMemberInfoToMembers@CPlayer@@QEAAXXZ
 *Address: 0x140013DD6
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::SendData_PartyMemberInfoToMembers(CPlayer *this)
{
  CPlayer::SendData_PartyMemberInfoToMembers(this);
}



} // namespace Player
} // namespace RFOnline
