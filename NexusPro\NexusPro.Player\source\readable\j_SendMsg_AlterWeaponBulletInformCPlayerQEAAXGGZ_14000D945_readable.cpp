/**
 * @file j_SendMsg_AlterWeaponBulletInformCPlayerQEAAXGGZ_14000D945_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_SendMsg_AlterWeaponBulletInformCPlayerQEAAXGGZ_14000D945
 * @note Address: 0x14000D945
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?SendMsg_AlterWeaponBulletInform@CPlayer@@QEAAXGG@Z
 *Address: 0x14000D945
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::SendMsg_AlterWeaponBulletInform(CPlayer *this, unsigned __int16 wItemSerial, unsigned __int16 wLeftNum)
{
  CPlayer::SendMsg_AlterWeaponBulletInform(this, wItemSerial, wLeftNum);
}



} // namespace Player
} // namespace RFOnline
