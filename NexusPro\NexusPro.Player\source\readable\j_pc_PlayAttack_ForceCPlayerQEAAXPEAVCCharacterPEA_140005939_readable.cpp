/**
 * @file j_pc_PlayAttack_ForceCPlayerQEAAXPEAVCCharacterPEA_140005939_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_PlayAttack_ForceCPlayerQEAAXPEAVCCharacterPEA_140005939
 * @note Address: 0x140005939
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_PlayAttack_Force@CPlayer@@QEAAXPEAVCCharacter@@PEAMGPEAGG@Z
 *Address: 0x140005939
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_PlayAttack_Force(CPlayer *this, CCharacter *pDst, float *pfAreaPos, unsigned __int16 wForceSerial, unsigned __int16 *pConsumeSerial, unsigned __int16 wEffBtSerial)
{
  CPlayer::pc_PlayAttack_Force(this, pDst, pfAreaPos, wForceSerial, pConsumeSerial, wEffBtSerial);
}



} // namespace Player
} // namespace RFOnline
