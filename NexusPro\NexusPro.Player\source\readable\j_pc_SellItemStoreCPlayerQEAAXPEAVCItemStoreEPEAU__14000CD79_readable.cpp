/**
 * @file j_pc_SellItemStoreCPlayerQEAAXPEAVCItemStoreEPEAU__14000CD79_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_SellItemStoreCPlayerQEAAXPEAVCItemStoreEPEAU__14000CD79
 * @note Address: 0x14000CD79
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_SellItemStore@CPlayer@@QEAAXPEAVCItemStore@@EPEAU_list@_sell_store_request_clzo@@H@Z
 *Address: 0x14000CD79
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_SellItemStore(CPlayer *this, CItemStore *pStore, char by<PERSON><PERSON><PERSON><PERSON>, _sell_store_request_clzo::_list *pList, int bUseNPCLinkIntem)
{
  CPlayer::pc_SellItemStore(this, pStore, byOfferNum, pList, bUseNPCLinkIntem);
}



} // namespace Player
} // namespace RFOnline
