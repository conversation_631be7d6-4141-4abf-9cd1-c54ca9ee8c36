/**
 * @file j_SF_TeleportToDestinationCPlayerUEAA_NPEAVCCharac_14000CC6B_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_SF_TeleportToDestinationCPlayerUEAA_NPEAVCCharac_14000CC6B
 * @note Address: 0x14000CC6B
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?SF_TeleportToDestination@CPlayer@@UEAA_NPEAVCCharacter@@_N@Z
 *Address: 0x14000CC6B
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


bool __fastcall CPlayer::SF_TeleportToDestination(CPlayer *this, CCharacter *pDstObj, bool bStone)
{
  return CPlayer::SF_TeleportToDestination(this, pDstObj, bStone);
}



} // namespace Player
} // namespace RFOnline
