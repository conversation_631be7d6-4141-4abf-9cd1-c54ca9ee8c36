/**
 * @file j_SendMsg_AskReEnterCDarkHoleChannelQEAAXPEAVCPlay_14000E480_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_SendMsg_AskReEnterCDarkHoleChannelQEAAXPEAVCPlay_14000E480
 * @note Address: 0x14000E480
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?SendMsg_AskReEnter@CDarkHoleChannel@@QEAAXPEAVCPlayer@@@Z
 *Address: 0x14000E480
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CDarkHoleChannel::SendMsg_AskReEnter(CDarkHoleChannel *this, CPlayer *pDst)
{
  CDarkHoleChannel::SendMsg_AskReEnter(this, pDst);
}



} // namespace Player
} // namespace RFOnline
