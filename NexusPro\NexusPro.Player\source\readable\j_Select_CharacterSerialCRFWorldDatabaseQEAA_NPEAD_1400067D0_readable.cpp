/**
 * @file j_Select_CharacterSerialCRFWorldDatabaseQEAA_NPEAD_1400067D0_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_Select_CharacterSerialCRFWorldDatabaseQEAA_NPEAD_1400067D0
 * @note Address: 0x1400067D0
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?Select_CharacterSerial@CRFWorldDatabase@@QEAA_NPEADPEAK@Z
 *Address: 0x1400067D0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


bool __fastcall CRFWorldDatabase::Select_CharacterSerial(CRFWorldDatabase *this, char *pwszCharacterName, unsigned int *pSerial)
{
  return CRFWorldDatabase::Select_CharacterSerial(this, pwszCharacterName, pSerial);
}



} // namespace Player
} // namespace RFOnline
