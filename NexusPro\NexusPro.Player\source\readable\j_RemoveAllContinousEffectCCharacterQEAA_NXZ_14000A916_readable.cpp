/**
 * @file j_RemoveAllContinousEffectCCharacterQEAA_NXZ_14000A916_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_RemoveAllContinousEffectCCharacterQEAA_NXZ_14000A916
 * @note Address: 0x14000A916
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?RemoveAllContinousEffect@CCharacter@@QEAA_NXZ
 *Address: 0x14000A916
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


bool __fastcall CCharacter::RemoveAllContinousEffect(CCharacter *this)
{
  return CCharacter::RemoveAllContinousEffect(this);
}



} // namespace Player
} // namespace RFOnline
