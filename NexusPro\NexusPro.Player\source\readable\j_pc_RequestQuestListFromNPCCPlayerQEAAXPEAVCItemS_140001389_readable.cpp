/**
 * @file j_pc_RequestQuestListFromNPCCPlayerQEAAXPEAVCItemS_140001389_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_RequestQuestListFromNPCCPlayerQEAAXPEAVCItemS_140001389
 * @note Address: 0x140001389
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_RequestQuestListFromNPC@CPlayer@@QEAAXPEAVCItemStore@@@Z
 *Address: 0x140001389
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_RequestQuestListFromNPC(CPlayer *this, CItemStore *pStore)
{
  CPlayer::pc_RequestQuestListFromNPC(this, pStore);
}



} // namespace Player
} // namespace RFOnline
