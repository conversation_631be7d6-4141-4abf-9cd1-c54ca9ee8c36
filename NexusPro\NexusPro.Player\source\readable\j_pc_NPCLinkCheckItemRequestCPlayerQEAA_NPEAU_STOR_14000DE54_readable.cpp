/**
 * @file j_pc_NPCLinkCheckItemRequestCPlayerQEAA_NPEAU_STOR_14000DE54_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_NPCLinkCheckItemRequestCPlayerQEAA_NPEAU_STOR_14000DE54
 * @note Address: 0x14000DE54
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_NPCLinkCheckItemRequest@CPlayer@@QEAA_NPEAU_STORAGE_POS_INDIV@@@Z
 *Address: 0x14000DE54
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


bool __fastcall CPlayer::pc_NPCLinkCheckItemRequest(CPlayer *this, _STORAGE_POS_INDIV *pStorage)
{
  return CPlayer::pc_NPCLinkCheckItemRequest(this, pStorage);
}



} // namespace Player
} // namespace RFOnline
