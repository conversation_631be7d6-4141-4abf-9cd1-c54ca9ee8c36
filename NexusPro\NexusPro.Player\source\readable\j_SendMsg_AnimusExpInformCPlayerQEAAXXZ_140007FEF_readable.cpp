/**
 * @file j_SendMsg_AnimusExpInformCPlayerQEAAXXZ_140007FEF_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_SendMsg_AnimusExpInformCPlayerQEAAXXZ_140007FEF
 * @note Address: 0x140007FEF
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?SendMsg_AnimusExpInform@CPlayer@@QEAAXXZ
 *Address: 0x140007FEF
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::SendMsg_AnimusExpInform(CPlayer *this)
{
  CPlayer::SendMsg_AnimusExpInform(this);
}



} // namespace Player
} // namespace RFOnline
