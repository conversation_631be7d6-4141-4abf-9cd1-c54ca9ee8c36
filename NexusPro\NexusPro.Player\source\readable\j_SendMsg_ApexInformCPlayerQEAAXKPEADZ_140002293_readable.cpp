/**
 * @file j_SendMsg_ApexInformCPlayerQEAAXKPEADZ_140002293_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_SendMsg_ApexInformCPlayerQEAAXKPEADZ_140002293
 * @note Address: 0x140002293
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?SendMsg_ApexInform@CPlayer@@QEAAXKPEAD@Z
 *Address: 0x140002293
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::SendMsg_ApexInform(CPlayer *this, unsigned int dwRecvSize, char *pMsg)
{
  CPlayer::SendMsg_ApexInform(this, dwRecvSize, pMsg);
}



} // namespace Player
} // namespace RFOnline
