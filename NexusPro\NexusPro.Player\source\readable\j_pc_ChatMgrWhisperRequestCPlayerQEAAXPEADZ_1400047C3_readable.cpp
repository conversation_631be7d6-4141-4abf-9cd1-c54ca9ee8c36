/**
 * @file j_pc_ChatMgrWhisperRequestCPlayerQEAAXPEADZ_1400047C3_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_ChatMgrWhisperRequestCPlayerQEAAXPEADZ_1400047C3
 * @note Address: 0x1400047C3
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_ChatMgrWhisperRequest@CPlayer@@QEAAXPEAD@Z
 *Address: 0x1400047C3
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_ChatMgrWhisperRequest(CPlayer *this, char *pwszChatData)
{
  CPlayer::pc_ChatMgrWhisperRequest(this, pwszChatData);
}



} // namespace Player
} // namespace RFOnline
