/**
 * @file j_pc_RegistBindCPlayerQEAAXPEAVCItemStoreZ_14001392B_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_RegistBindCPlayerQEAAXPEAVCItemStoreZ_14001392B
 * @note Address: 0x14001392B
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_RegistBind@CPlayer@@QEAAXPEAVCItemStore@@@Z
 *Address: 0x14001392B
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_RegistBind(CPlayer *this, CItemStore *pStore)
{
  CPlayer::pc_RegistBind(this, pStore);
}



} // namespace Player
} // namespace RFOnline
