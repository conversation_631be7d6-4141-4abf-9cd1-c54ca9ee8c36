/**
 * @file j_pc_EquipPartCPlayerQEAAXPEAU_STORAGE_POS_INDIVZ_140011FA4_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_EquipPartCPlayerQEAAXPEAU_STORAGE_POS_INDIVZ_140011FA4
 * @note Address: 0x140011FA4
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_EquipPart@CPlayer@@QEAAXPEAU_STORAGE_POS_INDIV@@@Z
 *Address: 0x140011FA4
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_EquipPart(CPlayer *this, _STORAGE_POS_INDIV *pItem)
{
  CPlayer::pc_EquipPart(this, pItem);
}



} // namespace Player
} // namespace RFOnline
