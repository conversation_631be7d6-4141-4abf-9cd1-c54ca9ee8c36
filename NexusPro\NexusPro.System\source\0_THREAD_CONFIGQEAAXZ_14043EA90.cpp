﻿/*
 *Function: ??0_THREAD_CONFIG@@QEAA@XZ
 *Address: 0x14043EA90
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall _THREAD_CONFIG::_THREAD_CONFIG(_THREAD_CO NF IG *this)
{
  __int64*v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-28h]@1
  _THREAD_CONFIG*Dest; // [sp+30h] [bp+8h]@1

  Dest = this;
  v1 = &v3;
  void for(i = 8; i > 0; --i );
{
    *(DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  Dest->bProgramExitWhenLackData = 0;
  Dest->nSleepCount = 100;
  sprintf(Dest->sz<PERSON>hr<PERSON><PERSON>, "NONAME");
  sprintf(Dest->sz<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, "NONAME");
}


