﻿/*
 *Function: ?OpenWorldSuccessResult@CNetworkEX@@AEAA_NKPEAD@Z
 *Address: 0x1401C0340
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// External symbol declarations
extern "C" IMAGE_DOS_HEADER __ImageBase;
extern "C" DWORD off_14000003C;
extern "C" void*_delayLoadHelper2(ImgDelayDescr*pidd, void**ppfnIATEntry);
extern struct EqSukData { void*pwszEpSuk; } EqSukList[16];
extern void MyMessageBox(const char*title, const char*message);


char __fastcall CNetworkEX::OpenWorldSuccessResult(CNetworkEX *this, unsigned int n, char *pMsg)
{
  __int64*v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v6; // [sp+0h] [bp-38h]@1
  char*v7; // [sp+20h] [bp-18h]@4

  v3 = &v6;
  void for(i = 12; i > 0; --i );
{
    *(DWORD *)v3 = -*********;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v7 = pMsg;
  if(unk_179 9C9 AE9 == (unsigned __int8)pMsg[50] )
  {
    CMainThread::pc_OpenWorldSuccessResult(&g_Main, *v7, v7 + 1, v7 + 33);
    result = 1;
  }
  else
  {
    MyMessageBox("Open, World(Account -> Zone)",
      "!!Server type is wrong!!(AC:%d)(ZO:%d)",
      (unsigned __int8)v7[50],
      unk_1799C9AE9);
    WriteServerStartHistory("Server, Type is Wrong ==> Account, Server(%d) != ZoneServer(%d)",
      (unsigned __int8)v7[50],
      unk_1799C9AE9);
    result = 1;
  }
  return result;
}


