/**
 * @file j_SF_SkillContHelpTimeInc_OnceCGameObjectUEAA_NPEA_140001D25_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_SF_SkillContHelpTimeInc_OnceCGameObjectUEAA_NPEA_140001D25
 * @note Address: 0x140001D25
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?SF_SkillContHelpTimeInc_Once@CGameObject@@UEAA_NPEAVCCharacter@@M@Z
 *Address: 0x140001D25
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


bool __fastcall CGameObject::SF_SkillContHelpTimeInc_Once(CGameObject *this, CCharacter *pDstObj, float fEffectValue)
{
  return CGameObject::SF_SkillContHelpTimeInc_Once(this, pDstObj, fEffectValue);
}



} // namespace Player
} // namespace RFOnline
