/**
 * @file j_mgr_user_banCPlayerQEAA_NPEADH0EZ_1400115FE_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_mgr_user_banCPlayerQEAA_NPEADH0EZ_1400115FE
 * @note Address: 0x1400115FE
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?mgr_user_ban@CPlayer@@QEAA_NPEADH0E@Z
 *Address: 0x1400115FE
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


bool __fastcall CPlayer::mgr_user_ban(CPlayer *this, char *uszCharName, int iPeriod, char *uszReason, char byBlockType)
{
  return CPlayer::mgr_user_ban(this, uszCharName, iPeriod, uszReason, byBlockType);
}



} // namespace Player
} // namespace RFOnline
