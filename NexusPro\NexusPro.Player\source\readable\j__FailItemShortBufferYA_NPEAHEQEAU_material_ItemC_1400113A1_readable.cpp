/**
 * @file j__FailItemShortBufferYA_NPEAHEQEAU_material_ItemC_1400113A1_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j__FailItemShortBufferYA_NPEAHEQEAU_material_ItemC_1400113A1
 * @note Address: 0x1400113A1
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?_FailItemShortBuffer@@YA_NPEAHEQEAU_material@_ItemCombine_exp_fld@@PEAPEAU_db_con@_STORAGE_LIST@@@Z
 *Address: 0x1400113A1
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


bool __fastcall _FailItemShortBuffer(int *nBuffer, char by<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, _ItemCombine_exp_fld::_material *pMtlList, _STORAGE_LIST::_db_con **pMt_Sv_Inv);
{
  return _FailItemShortBuffer(nBuffer, byMtSlotNum, pMtlList, pMt_Sv_Inv);
}



} // namespace Player
} // namespace RFOnline
