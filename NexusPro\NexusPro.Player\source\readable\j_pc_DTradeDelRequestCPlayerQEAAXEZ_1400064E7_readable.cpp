/**
 * @file j_pc_DTradeDelRequestCPlayerQEAAXEZ_1400064E7_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_DTradeDelRequestCPlayerQEAAXEZ_1400064E7
 * @note Address: 0x1400064E7
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_DTradeDelRequest@CPlayer@@QEAAXE@Z
 *Address: 0x1400064E7
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_DTradeDelRequest(CPlayer *this, char bySlotIndex)
{
  CPlayer::pc_DTradeDelRequest(this, bySlotIndex);
}



} // namespace Player
} // namespace RFOnline
