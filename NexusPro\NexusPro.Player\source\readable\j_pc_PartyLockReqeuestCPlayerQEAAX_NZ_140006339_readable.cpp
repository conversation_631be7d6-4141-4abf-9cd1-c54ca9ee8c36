/**
 * @file j_pc_PartyLockReqeuestCPlayerQEAAX_NZ_140006339_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_PartyLockReqeuestCPlayerQEAAX_NZ_140006339
 * @note Address: 0x140006339
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_PartyLockReqeuest@CPlayer@@QEAAX_N@Z
 *Address: 0x140006339
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_PartyLockReqeuest(CPlayer *this, bool bLock)
{
  CPlayer::pc_PartyLockReqeuest(this, bLock);
}



} // namespace Player
} // namespace RFOnline
