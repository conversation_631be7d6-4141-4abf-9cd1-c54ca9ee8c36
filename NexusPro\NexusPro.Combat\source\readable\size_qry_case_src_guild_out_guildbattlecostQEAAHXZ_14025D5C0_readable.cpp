/**
 * @file size_qry_case_src_guild_out_guildbattlecostQEAAHXZ_14025D5C0_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: size_qry_case_src_guild_out_guildbattlecostQEAAHXZ_14025D5C0
 * @note Address: 0x14025D5C0
 */

#include <cstdint>

namespace RFOnline {
namespace Combat {

﻿/*
 *Function: ?size@_qry_case_src_guild_out_guildbattlecost@@QEAAHXZ
 *Address: 0x14025D5C0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


signed __int64 __fastcall _qry_case_src_guild_out_guildbattlecost::size(_qry_case_src_guild_out_guildbattlecost *this)
{
  return 64i64;
}



} // namespace Combat
} // namespace RFOnline
