/**
 * @file j_pc_PlayAttack_TestCPlayerQEAAXEEGEPEAFZ_140007C70_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_PlayAttack_TestCPlayerQEAAXEEGEPEAFZ_140007C70
 * @note Address: 0x140007C70
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_PlayAttack_Test@CPlayer@@QEAAXEEGEPEAF@Z
 *Address: 0x140007C70
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_PlayAttack_Test(CPlayer *this, char byEffectCode, char byEffectIndex, unsigned __int16 wBulletItemSerial, char byWeaponPart, __int16 *pzTar)
{
  CPlayer::pc_PlayAttack_Test(this, byEffectCode, byEffectIndex, wBulletItemSerial, byWeaponPart, pzTar);
}



} // namespace Player
} // namespace RFOnline
