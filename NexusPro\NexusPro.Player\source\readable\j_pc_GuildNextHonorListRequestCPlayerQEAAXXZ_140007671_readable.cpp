/**
 * @file j_pc_GuildNextHonorListRequestCPlayerQEAAXXZ_140007671_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_GuildNextHonorListRequestCPlayerQEAAXXZ_140007671
 * @note Address: 0x140007671
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_GuildNextHonorListRequest@CPlayer@@QEAAXXZ
 *Address: 0x140007671
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_GuildNextHonorListRequest(CPlayer *this)
{
  CPlayer::pc_GuildNextHonorListRequest(this);
}



} // namespace Player
} // namespace RFOnline
