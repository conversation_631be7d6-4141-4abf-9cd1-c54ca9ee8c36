/**
 * @file j_RobbedHPCAnimusUEAA_NPEAVCCharacterHZ_14000462E_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_RobbedHPCAnimusUEAA_NPEAVCCharacterHZ_14000462E
 * @note Address: 0x14000462E
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?Rob<PERSON><PERSON>@CAnimus@@UEAA_NPEAVCCharacter@@H@Z
 *Address: 0x14000462E
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


bool __fastcall CAnimus::Rob<PERSON>HP(CAnimus *this, CCharacter *pDst, int nDecHP)
{
  return CAnimus::RobbedHP(this, pDst, nDecHP);
}



} // namespace Player
} // namespace RFOnline
