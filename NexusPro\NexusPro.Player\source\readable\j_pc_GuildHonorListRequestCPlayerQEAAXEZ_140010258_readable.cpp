/**
 * @file j_pc_GuildHonorListRequestCPlayerQEAAXEZ_140010258_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_GuildHonorListRequestCPlayerQEAAXEZ_140010258
 * @note Address: 0x140010258
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_GuildHonorListRequest@CPlayer@@QEAAXE@Z
 *Address: 0x140010258
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_GuildHonorListRequest(CPlayer *this, char byUI)
{
  CPlayer::pc_GuildHonorListRequest(this, byUI);
}



} // namespace Player
} // namespace RFOnline
