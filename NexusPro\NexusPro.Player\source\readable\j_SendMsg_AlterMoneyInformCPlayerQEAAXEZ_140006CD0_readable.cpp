/**
 * @file j_SendMsg_AlterMoneyInformCPlayerQEAAXEZ_140006CD0_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_SendMsg_AlterMoneyInformCPlayerQEAAXEZ_140006CD0
 * @note Address: 0x140006CD0
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?SendMsg_AlterMoneyInform@CPlayer@@QEAAXE@Z
 *Address: 0x140006CD0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::SendMsg_AlterMoneyInform(CPlayer *this, char byReasonCode)
{
  CPlayer::SendMsg_AlterMoneyInform(this, byReasonCode);
}



} // namespace Player
} // namespace RFOnline
