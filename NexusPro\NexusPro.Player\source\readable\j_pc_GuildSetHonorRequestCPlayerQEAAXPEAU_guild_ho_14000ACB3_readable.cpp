/**
 * @file j_pc_GuildSetHonorRequestCPlayerQEAAXPEAU_guild_ho_14000ACB3_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_GuildSetHonorRequestCPlayerQEAAXPEAU_guild_ho_14000ACB3
 * @note Address: 0x14000ACB3
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_GuildSetHonorRequest@CPlayer@@QEAAXPEAU_guild_honor_set_request_clzo@@@Z
 *Address: 0x14000ACB3
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_GuildSetHonorRequest(CPlayer *this, _guild_honor_set_request_clzo *pData)
{
  CPlayer::pc_GuildSetHonorRequest(this, pData);
}



} // namespace Player
} // namespace RFOnline
