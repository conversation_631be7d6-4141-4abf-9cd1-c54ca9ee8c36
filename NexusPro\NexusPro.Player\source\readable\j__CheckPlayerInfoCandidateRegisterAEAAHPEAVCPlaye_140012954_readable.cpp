/**
 * @file j__CheckPlayerInfoCandidateRegisterAEAAHPEAVCPlaye_140012954_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j__CheckPlayerInfoCandidateRegisterAEAAHPEAVCPlaye_140012954
 * @note Address: 0x140012954
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?_CheckPlayerInfo@CandidateRegister@@AEAAHPEAVCPlayer@@@Z
 *Address: 0x140012954
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


int __fastcall CandidateRegister::_CheckPlayerInfo(CandidateRegister *this, CPlayer *pOne)
{
  return CandidateRegister::_CheckPlayerInfo(this, pOne);
}



} // namespace Player
} // namespace RFOnline
