/**
 * @file j__GetPartyEffectMemberCCharacterQEAAHPEAV1_NPEAPE_140001FDC_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j__GetPartyEffectMemberCCharacterQEAAHPEAV1_NPEAPE_140001FDC
 * @note Address: 0x140001FDC
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?_GetPartyEffectMember@CCharacter@@QEAAHPEAV1@_NPEAPEAV1@@Z
 *Address: 0x140001FDC
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


int __fastcall CCharacter::_GetPartyEffectMember(CCharacter *this, CCharacter *pOriDst, bool bCircle, CCharacter **ppDsts)
{
  return CCharacter::_GetPartyEffectMember(this, pOri<PERSON>t, bCircle, ppDsts);
}



} // namespace Player
} // namespace RFOnline
