/**
 * @file j_RemoveSFContHelpByEffectCCharacterQEAAXHHZ_1400094B7_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_RemoveSFContHelpByEffectCCharacterQEAAXHHZ_1400094B7
 * @note Address: 0x1400094B7
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?RemoveSFContHelpByEffect@CCharacter@@QEAAXHH@Z
 *Address: 0x1400094B7
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CCharacter::RemoveSFContHelpByEffect(CCharacter *this, int nContParamCode, int nContParamIndex)
{
  CCharacter::RemoveSFContHelpByEffect(this, nContParamCode, nContParamIndex);
}



} // namespace Player
} // namespace RFOnline
