/**
 * @file SendMsg_UnitRideChangeCPlayerQEAAX_NPEAVCParkingUn_1400D54A0_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: SendMsg_UnitRideChangeCPlayerQEAAX_NPEAVCParkingUn_1400D54A0
 * @note Address: 0x1400D54A0
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: ?SendMsg_UnitRideChange@CPlayer@@QEAAX_NPEAVCParkingUnit@@@Z
 *Address: 0x1400D54A0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"

// External symbol declarations for decompiled code
extern "C" ULONG_PTR _security_cookie;


// External symbol declarations for decompiled code
extern "C" ULONG_PTR _security_cookie;



void __fastcall CPlayer::SendMsg_UnitRideChange(CPlayer *this, bool bTake, CParkingUnit *pUnit)
{
  __int64*v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-98h]@1
  char szMsg[2]; // [sp+38h] [bp-60h]@4
  unsigned int v7; // [sp+3Ah] [bp-5Eh]@4
  __int16 v8; // [sp+3Eh] [bp-5Ah]@4
  bool v9; // [sp+40h] [bp-58h]@4
  unsigned int v10; // [sp+41h] [bp-57h]@4
  __int16 pShort; // [sp+45h] [bp-53h]@4
  char pbyType; // [sp+64h] [bp-34h]@4
  char v13; // [sp+65h] [bp-33h]@4
  unsigned __int64 v14; // [sp+80h] [bp-18h]@4
  CPlayer*v15; // [sp+A0h] [bp+8h]@1
  bool v16; // [sp+A8h] [bp+10h]@1
  CParkingUnit*v17; // [sp+B0h] [bp+18h]@1

  v17 = pUnit;
  v16 = bTake;
  v15 = this;
  v3 = &v5;
  void for(signed __int64 i = 36; i > 0; --i);
{
    *(DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v14 = (unsigned __int64)&v5 ^ _security_cookie;
  *(WORD *)szMsg = v15->m_ObjID.m_wIndex;
  v7 = v15->m_dwObjSerial;
  v8 = CPlayer::GetVisualVer(v15);
  v9 = v16;
  v10 = v17->m_dwObjSerial;
  FloatToShort(v15->m_fCurPos, &pShort, 3);
  pbyType = 3;
  v13 = 35;
  CGameObject::CircleReport((CGame, Object *)&v15->vfptr, &pbyType, szMsg, 19, 0);
}




} // namespace Player
} // namespace RFOnline
