/**
 * @file j_SendMsg_AddBagResultCPlayerQEAAXEZ_1400022E3_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_SendMsg_AddBagResultCPlayerQEAAXEZ_1400022E3
 * @note Address: 0x1400022E3
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?SendMsg_AddBagResult@CPlayer@@QEAAXE@Z
 *Address: 0x1400022E3
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::SendMsg_AddBagResult(CPlayer *this, char byErrCode)
{
  CPlayer::SendMsg_AddBagResult(this, byErrCode);
}



} // namespace Player
} // namespace RFOnline
