/**
 * @file j_SearchNearPlayerAttackCAnimusQEAAPEAVCCharacterX_140011C93_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_SearchNearPlayerAttackCAnimusQEAAPEAVCCharacterX_140011C93
 * @note Address: 0x140011C93
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?SearchNearPlayerAttack@CAnimus@@QEAAPEAVCCharacter@@XZ
 *Address: 0x140011C93
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


CCharacter *__fastcall CAnimus::SearchNearPlayerAttack(CAnimus *this)
{
  return CAnimus::SearchNearPlayerAttack(this);
}



} // namespace Player
} // namespace RFOnline
