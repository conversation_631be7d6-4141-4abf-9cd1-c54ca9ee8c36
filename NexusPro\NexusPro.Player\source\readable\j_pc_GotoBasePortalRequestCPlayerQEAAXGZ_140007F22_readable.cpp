/**
 * @file j_pc_GotoBasePortalRequestCPlayerQEAAXGZ_140007F22_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_GotoBasePortalRequestCPlayerQEAAXGZ_140007F22
 * @note Address: 0x140007F22
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_GotoBasePortalRequest@CPlayer@@QEAAXG@Z
 *Address: 0x140007F22
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_GotoBasePortalRequest(CPlayer *this, unsigned __int16 wItemSerial)
{
  CPlayer::pc_GotoBasePortalRequest(this, wItemSerial);
}



} // namespace Player
} // namespace RFOnline
