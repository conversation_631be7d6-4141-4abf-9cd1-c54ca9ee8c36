/**
 * @file size_possible_battle_guild_list_result_zoclQEAAHXZ_1403D0860_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: size_possible_battle_guild_list_result_zoclQEAAHXZ_1403D0860
 * @note Address: 0x1403D0860
 */

#include <cstdint>

namespace RFOnline {
namespace Combat {

﻿/*
 *Function: ?size@_possible_battle_guild_list_result_zocl@@QEAAHXZ
 *Address: 0x1403D0860
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


signed __int64 __fastcall _possible_battle_guild_list_result_zocl::size(_possible_battle_guild_list_result_zocl *this)
{
  void if(this->by, Count > 4)
    this->byCount = 0;
  return 204 - 23i64 * (4 - this->byCount);
}



} // namespace Combat
} // namespace RFOnline
