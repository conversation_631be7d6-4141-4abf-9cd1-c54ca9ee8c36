/**
 * @file j_UpdatePlayerStatusTimeLimitMgrQEAA_NGKEZ_14000D64D_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_UpdatePlayerStatusTimeLimitMgrQEAA_NGKEZ_14000D64D
 * @note Address: 0x14000D64D
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?UpdatePlayerStatus@TimeLimitMgr@@QEAA_NGKE@Z
 *Address: 0x14000D64D
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


bool __fastcall TimeLimitMgr::UpdatePlayerStatus(TimeLimitMgr *this, unsigned __int16 wIndex, unsigned int dwFatigue, char wStatus)
{
  return TimeLimitMgr::UpdatePlayerStatus(this, wIndex, dwFatigue, wStatus);
}



} // namespace Player
} // namespace RFOnline
