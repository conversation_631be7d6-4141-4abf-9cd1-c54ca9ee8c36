/**
 * @file j_pc_PlayAttack_GenCPlayerQEAAXPEAVCCharacterEGG_N_140012B25_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_PlayAttack_GenCPlayerQEAAXPEAVCCharacterEGG_N_140012B25
 * @note Address: 0x140012B25
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_PlayAttack_Gen@CPlayer@@QEAAXPEAVCCharacter@@EGG_N@Z
 *Address: 0x140012B25
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_PlayAttack_Gen(CPlayer *this, CCharacter *pDst, char by<PERSON>tt<PERSON><PERSON>, unsigned __int16 wBulletSerial, unsigned __int16 wEffBtSerial, bool bCount)
{
  CPlayer::pc_PlayAttack_Gen(this, pDst, byAttPart, wBulletSerial, wEffBtSerial, bCount);
}



} // namespace Player
} // namespace RFOnline
