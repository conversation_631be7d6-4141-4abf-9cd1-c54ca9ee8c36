/**
 * @file j_pc_PotionDivisionCPlayerQEAAXGGEZ_140004FB1_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_PotionDivisionCPlayerQEAAXGGEZ_140004FB1
 * @note Address: 0x140004FB1
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_PotionDivision@CPlayer@@QEAAXGGE@Z
 *Address: 0x140004FB1
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_PotionDivision(CPlayer *this, unsigned __int16 wSerial, unsigned __int16 wTarSerial, char byAmount)
{
  CPlayer::pc_PotionDivision(this, wSerial, wTarSerial, byAmount);
}



} // namespace Player
} // namespace RFOnline
