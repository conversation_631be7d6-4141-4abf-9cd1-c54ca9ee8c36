/**
 * @file player_create_complete_moneyCMgrAccountLobbyHistor_1402347F0_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: player_create_complete_moneyCMgrAccountLobbyHistor_1402347F0
 * @note Address: 0x1402347F0
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: ?player_create_complete_money@CMgrAccountLobbyHistory@@QEAAXPEAU_AVATOR_DATA@@PEAD@Z
 *Address: 0x1402347F0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CMgrAccountLobbyHistory::player_create_complete_money(CMgrAccountLobbyHistory *this, _AVATOR_DATA *pAvator, char *pszFileName)
{
  __int64*v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-28h]@1
  CMgrAccountLobbyHistory*v6; // [sp+30h] [bp+8h]@1
  char*pszFileNamea; // [sp+40h] [bp+18h]@1

  pszFileNamea = pszFileName;
  v6 = this;
  v3 = &v5;
  void for(i = 8; i > 0; --i );
{
    *(DWORD *)v3 = -*********;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  sLData[0] = 0;
  sprintf_s<10240>(
    (char (*)[10240])sLBuf,
    "Create Complete Player Money: $D(%d), $G(%d)\r\n",
    pAvator->dbAvator.m_dwDalant,
    pAvator->dbAvator.m_dwGold);
  strcat_s<20000>((char (*)[20000])sLData, sLBuf);
  strcat_s<20000>((char (*)[20000])sLData, "\r\n\t============\r\n\r\n");
  CMgrAccountLobbyHistory::WriteFile(v6, pszFileNamea, sLData);
}




} // namespace Player
} // namespace RFOnline
