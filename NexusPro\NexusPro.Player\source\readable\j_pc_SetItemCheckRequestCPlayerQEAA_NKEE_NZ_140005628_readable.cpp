/**
 * @file j_pc_SetItemCheckRequestCPlayerQEAA_NKEE_NZ_140005628_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_SetItemCheckRequestCPlayerQEAA_NKEE_NZ_140005628
 * @note Address: 0x140005628
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_SetItemCheckRequest@CPlayer@@QEAA_NKEE_N@Z
 *Address: 0x140005628
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


bool __fastcall CPlayer::pc_SetItemCheckRequest(CPlayer *this, unsigned int dwSetItem, char bySetItemNum, char bySetEffectNum, bool bSet)
{
  return CPlayer::pc_SetItemCheckRequest(this, dwSetItem, bySetItemNum, bySetEffectNum, bSet);
}



} // namespace Player
} // namespace RFOnline
