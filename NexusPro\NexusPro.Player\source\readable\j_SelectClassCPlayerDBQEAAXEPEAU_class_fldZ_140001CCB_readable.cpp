/**
 * @file j_SelectClassCPlayerDBQEAAXEPEAU_class_fldZ_140001CCB_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_SelectClassCPlayerDBQEAAXEPEAU_class_fldZ_140001CCB
 * @note Address: 0x140001CCB
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?SelectClass@CPlayerDB@@QEAAXEPEAU_class_fld@@@Z
 *Address: 0x140001CCB
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayerDB::SelectClass(CPlayerDB *this, char byHistoryRecordNum, _class_fld *pSelectClass)
{
  CPlayerDB::SelectClass(this, byHistoryRecordNum, pSelectClass);
}



} // namespace Player
} // namespace RFOnline
