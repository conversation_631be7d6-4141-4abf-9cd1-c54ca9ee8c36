/**
 * @file j_pc_RequestPatriarchPunishmentCPlayerQEAAXEPEAD0Z_14000BDD4_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_RequestPatriarchPunishmentCPlayerQEAAXEPEAD0Z_14000BDD4
 * @note Address: 0x14000BDD4
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_RequestPatriarchPunishment@CPlayer@@QEAAXEPEAD0@Z
 *Address: 0x14000BDD4
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_RequestPatriarchPunishment(CPlayer *this, char byType, char *pwszName, char *pwszCont)
{
  CPlayer::pc_RequestPatriarchPunishment(this, byType, pwszName, pwszCont);
}



} // namespace Player
} // namespace RFOnline
