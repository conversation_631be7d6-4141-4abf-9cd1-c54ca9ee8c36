/**
 * @file j_SF_TransMonsterHPCPlayerUEAA_NPEAVCCharacterMZ_1400041C9_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_SF_TransMonsterHPCPlayerUEAA_NPEAVCCharacterMZ_1400041C9
 * @note Address: 0x1400041C9
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?SF_TransMonsterHP@CPlayer@@UEAA_NPEAVCCharacter@@M@Z
 *Address: 0x1400041C9
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


bool __fastcall CPlayer::SF_TransMonsterHP(CPlayer *this, CCharacter *pDstObj, float fEffectValue)
{
  return CPlayer::SF_TransMonsterHP(this, pDstObj, fEffectValue);
}



} // namespace Player
} // namespace RFOnline
