/**
 * @file j_WPActiveSkillCPlayerQEAA_NPEAU_be_damaged_charHP_14000A9D4_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_WPActiveSkillCPlayerQEAA_NPEAU_be_damaged_charHP_14000A9D4
 * @note Address: 0x14000A9D4
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?WPActiveSkill@CPlayer@@QEAA_NPEAU_be_damaged_char@@HPEAU_skill_fld@@H@Z
 *Address: 0x14000A9D4
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


bool __fastcall CPlayer::WPActiveSkill(CPlayer *this, _be_damaged_char *pDamList, int nDamagedObjNum, _skill_fld *pSkillFld, int nEffectCode)
{
  return CPlayer::WPActiveSkill(this, pDam<PERSON>ist, nDamagedObjNum, pSkillFld, nEffectCode);
}



} // namespace Player
} // namespace RFOnline
