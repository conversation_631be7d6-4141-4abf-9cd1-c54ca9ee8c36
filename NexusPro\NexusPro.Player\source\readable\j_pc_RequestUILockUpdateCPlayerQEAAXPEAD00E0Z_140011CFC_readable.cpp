/**
 * @file j_pc_RequestUILockUpdateCPlayerQEAAXPEAD00E0Z_140011CFC_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_RequestUILockUpdateCPlayerQEAAXPEAD00E0Z_140011CFC
 * @note Address: 0x140011CFC
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_RequestUILockUpdate@CPlayer@@QEAAXPEAD00E0@Z
 *Address: 0x140011CFC
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_RequestUILockUpdate(CPlayer *this, char *usz<PERSON>LockPWOld, char *uszUILockPW, char *uszUILockPW_Confirm, char by<PERSON>Lock_HintIndex, char *usz<PERSON>Lock_HintAnswer)
{
  CPlayer::pc_RequestUILockUpdate(
    this,
    usz<PERSON><PERSON>ockPWOld,
    usz<PERSON><PERSON>ockP<PERSON>,
    usz<PERSON><PERSON>ockPW_Confirm,
    byUILock_HintIndex,
    uszUILock_HintAnswer);
}



} // namespace Player
} // namespace RFOnline
