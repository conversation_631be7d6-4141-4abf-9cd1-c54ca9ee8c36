/**
 * @file j_SendMsg_AnimusFPInformCPlayerQEAAXXZ_1400033A0_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_SendMsg_AnimusFPInformCPlayerQEAAXXZ_1400033A0
 * @note Address: 0x1400033A0
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?SendMsg_AnimusFPInform@CPlayer@@QEAAXXZ
 *Address: 0x1400033A0
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::SendMsg_AnimusFPInform(CPlayer *this)
{
  CPlayer::SendMsg_AnimusFPInform(this);
}



} // namespace Player
} // namespace RFOnline
