/**
 * @file j_pc_PartyJoinApplicationCPlayerQEAAXGZ_14000B703_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_pc_PartyJoinApplicationCPlayerQEAAXGZ_14000B703
 * @note Address: 0x14000B703
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?pc_PartyJoinApplication@CPlayer@@QEAAXG@Z
 *Address: 0x14000B703
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


void __fastcall CPlayer::pc_PartyJoinApplication(CPlayer *this, unsigned __int16 wBossIndex)
{
  CPlayer::pc_PartyJoinApplication(this, wBossIndex);
}



} // namespace Player
} // namespace RFOnline
