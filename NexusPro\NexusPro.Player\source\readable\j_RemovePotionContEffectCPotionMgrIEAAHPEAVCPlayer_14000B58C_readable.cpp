/**
 * @file j_RemovePotionContEffectCPotionMgrIEAAHPEAVCPlayer_14000B58C_readable.cpp
 * @brief Human-readable RF Online function
 * @note Original: j_RemovePotionContEffectCPotionMgrIEAAHPEAVCPlayer_14000B58C
 * @note Address: 0x14000B58C
 */

#include <cstdint>

namespace RFOnline {
namespace Player {

﻿/*
 *Function: j_?RemovePotionContEffect@CPotionMgr@@IEAAHPEAVCPlayer@@AEAV_ContPotionData@@@Z
 *Address: 0x14000B58C
 */

#include "../../NexusPro.Core/headers/NexusProCommon.h"
#include "../../NexusPro.Core/headers/RFOnlineClasses.h"


int __fastcall CPotionMgr::RemovePotionContEffect(CPotionMgr *this, CPlayer *pApplyPlayer, _ContPotionData *ContPotionData)
{
  return CPotionMgr::RemovePotionContEffect(this, pApplyPlayer, ContPotionData);
}



} // namespace Player
} // namespace RFOnline
